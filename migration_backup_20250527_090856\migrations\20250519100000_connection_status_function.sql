-- Create a function to update connection status
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION update_connection_status(connection_id_param UUID, new_status TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER -- Run with privileges of function creator
AS $$
DECLARE
  success BOOLEAN;
  updated_count INTEGER;
BEGIN
  -- Update the connection status
  UPDATE user_connections
  SET 
    status = new_status,
    updated_at = NOW()
  WHERE id = connection_id_param;
  
  -- Check if update was successful
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  -- Return true if at least one row was updated
  success := (updated_count > 0);
  
  RETURN success;
END;
$$;
