-- Check and fix the id column in businesses table
DO $$
BEGIN
    -- First check if the id column has the proper default value
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'businesses' 
        AND column_name = 'id'
        AND column_default = 'uuid_generate_v4()'
    ) THEN
        -- Update the id column to have the proper default value
        ALTER TABLE public.businesses 
        ALTER COLUMN id SET DEFAULT uuid_generate_v4();
    END IF;
END
$$;

-- Make sure the uuid-ossp extension is enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Verify the change
SELECT 
    column_name, 
    data_type, 
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'businesses' 
AND column_name = 'id';
