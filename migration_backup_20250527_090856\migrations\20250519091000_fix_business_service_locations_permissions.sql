-- Comprehensive fix to align business_service_locations with business_relevant_industries
-- This script fixes RLS policies and permissions for business_service_locations

BEGIN;

-- 1. Drop existing policies on business_service_locations
DROP POLICY IF EXISTS "Enable read access for all users" ON public.business_service_locations;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.business_service_locations;
DROP POLICY IF EXISTS "Enable update for business owners" ON public.business_service_locations;
DROP POLICY IF EXISTS "Enable delete for business owners" ON public.business_service_locations;

-- 2. Create new policies matching the working business_relevant_industries policies
-- Enable read access for all users
CREATE POLICY "Enable read access for all"
    ON public.business_service_locations
    FOR SELECT
    TO authenticated
    USING (true);

-- Create a specific insert policy for business owners
CREATE POLICY "Enable insert for business owners"
    ON public.business_service_locations
    FOR INSERT
    TO authenticated
    WITH CHECK (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

-- Create a policy for update/delete operations
CREATE POLICY "Enable update/delete for business owners"
    ON public.business_service_locations
    FOR ALL
    TO authenticated
    USING (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

-- 3. Grant explicit permissions to authenticated users and service role
GRANT ALL ON public.business_service_locations TO authenticated;
GRANT ALL ON public.business_service_locations TO service_role;

-- 4. Verify the policies
SELECT
    schemaname,
    tablename,
    policyname,
    roles,
    cmd
FROM
    pg_policies
WHERE
    tablename = 'business_service_locations';

COMMIT;
