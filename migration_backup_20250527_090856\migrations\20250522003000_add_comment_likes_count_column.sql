-- Add comment_likes_count column to social_posts and update functions to maintain it
-- This helps track comment engagement directly on the post level

-- Add the new column
ALTER TABLE public.social_posts
ADD COLUMN comment_likes_count INTEGER DEFAULT 0;

-- Update existing posts with current comment likes count
UPDATE social_posts sp
SET comment_likes_count = COALESCE((
  SELECT SUM(pc.likes_count)
  FROM post_comments pc
  WHERE pc.post_id = sp.id
), 0);

-- Update functions to maintain the comment_likes_count

-- First, update the function that handles comment likes
CREATE OR REPLACE FUNCTION public.update_post_engagement_on_comment_like() 
RETURNS TRIGGER AS $$
DECLARE
  post_id_var UUID;
  likes_count_var INTEGER;
  comments_count_var INTEGER;
  comment_likes_count_var INTEGER;
BEGIN
  -- Get the post_id for the comment being liked
  SELECT post_id INTO post_id_var
  FROM post_comments
  WHERE id = COALESCE(NEW.comment_id, OLD.comment_id);

  -- Get likes count
  SELECT sp.likes_count INTO likes_count_var
  FROM social_posts sp
  WHERE sp.id = post_id_var;

  -- Get comments count
  SELECT sp.comments_count INTO comments_count_var
  FROM social_posts sp
  WHERE sp.id = post_id_var;

  -- Get total comment likes for the post
  SELECT COALESCE(SUM(pc.likes_count), 0) INTO comment_likes_count_var
  FROM post_comments pc
  WHERE pc.post_id = post_id_var;
  
  -- Update post with new engagement score formula and comment_likes_count
  UPDATE social_posts
  SET 
    comment_likes_count = comment_likes_count_var,
    engagement_score = (likes_count_var * 1.0) + (comments_count_var * 2.0) + (comment_likes_count_var * 0.5)
  WHERE id = post_id_var;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Update the post likes count function to use the new column
CREATE OR REPLACE FUNCTION public.update_post_likes_count() 
RETURNS TRIGGER AS $$
DECLARE
  likes_count_var INTEGER;
  comments_count_var INTEGER;
  comment_likes_count_var INTEGER;
BEGIN
  -- Ensure the function is executed in a trigger context
  IF TG_OP IS NULL THEN
    RAISE EXCEPTION 'update_post_likes_count() can only be called as a trigger';
  END IF;

  IF TG_OP = 'INSERT' OR TG_OP = 'DELETE' THEN
    -- Count likes
    SELECT COUNT(*) INTO likes_count_var
    FROM post_likes
    WHERE post_id = COALESCE(NEW.post_id, OLD.post_id);
    
    -- Get comments count
    SELECT sp.comments_count INTO comments_count_var
    FROM social_posts sp
    WHERE sp.id = COALESCE(NEW.post_id, OLD.post_id);

    -- Get total comment likes for the post
    SELECT COALESCE(SUM(pc.likes_count), 0) INTO comment_likes_count_var
    FROM post_comments pc
    WHERE pc.post_id = COALESCE(NEW.post_id, OLD.post_id);
    
    -- Update post with new engagement score formula and comment_likes_count
    UPDATE social_posts
    SET 
      likes_count = likes_count_var,
      comment_likes_count = comment_likes_count_var,
      engagement_score = (likes_count_var * 1.0) + (comments_count_var * 2.0) + (comment_likes_count_var * 0.5)
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Update the comments count function to use the new column
CREATE OR REPLACE FUNCTION public.update_post_comments_count() 
RETURNS TRIGGER AS $$
DECLARE
  comments_count_var INTEGER;
  likes_count_var INTEGER;
  comment_likes_count_var INTEGER;
BEGIN
  -- Ensure the function is executed in a trigger context
  IF TG_OP IS NULL THEN
    RAISE EXCEPTION 'update_post_comments_count() can only be called as a trigger';
  END IF;

  IF TG_OP = 'INSERT' OR TG_OP = 'DELETE' THEN
    -- Count top-level comments
    SELECT COUNT(*) INTO comments_count_var
    FROM post_comments
    WHERE post_id = COALESCE(NEW.post_id, OLD.post_id) AND parent_comment_id IS NULL;
    
    -- Get likes count
    SELECT sp.likes_count INTO likes_count_var
    FROM social_posts sp
    WHERE sp.id = COALESCE(NEW.post_id, OLD.post_id);

    -- Get total comment likes for the post
    SELECT COALESCE(SUM(pc.likes_count), 0) INTO comment_likes_count_var
    FROM post_comments pc
    WHERE pc.post_id = COALESCE(NEW.post_id, OLD.post_id);
    
    -- Update post with new engagement score formula and comment_likes_count
    UPDATE social_posts
    SET 
      comments_count = comments_count_var,
      comment_likes_count = comment_likes_count_var,
      engagement_score = (likes_count_var * 1.0) + (comments_count_var * 2.0) + (comment_likes_count_var * 0.5)
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;
