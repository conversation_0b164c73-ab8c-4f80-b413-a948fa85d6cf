-- SIMPL<PERSON>IED SEARCH MIGRATION FOR HARD DELETION APPLICATION
-- This version removes soft deletion references since the application uses hard deletion

---------------------------------------------------------
-- PART 1: CREATE INDEXES ON BASE TABLES
---------------------------------------------------------

-- Create indexes on the events table
CREATE INDEX IF NOT EXISTS idx_events_title_trgm ON events USING gin (title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_description_trgm ON events USING gin (description gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_physical_location_trgm ON events USING gin (physical_location gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_category_ids ON events USING gin (netzero_category_ids);
CREATE INDEX IF NOT EXISTS idx_events_industry_ids ON events USING gin (industry_ids);

-- Create indexes on the profiles table
CREATE INDEX IF NOT EXISTS idx_profiles_name_trgm ON profiles USING gin ((first_name || ' ' || last_name) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_profiles_bio_trgm ON profiles USING gin (bio gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_profiles_visibility ON profiles (profile_visibility) WHERE profile_visibility = true;

---------------------------------------------------------
-- PART 2: CREATE USER CONSENT SETTINGS
---------------------------------------------------------

-- Create the user consent settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_consent_settings (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    share_email_with_event_creators BOOLEAN NOT NULL DEFAULT FALSE,
    share_email_with_attendees BOOLEAN NOT NULL DEFAULT FALSE,
    share_contact_details BOOLEAN NOT NULL DEFAULT FALSE,
    consent_updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS to the consent settings table
ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

-- Users can only view and edit their own consent settings
CREATE POLICY view_own_consent_settings 
    ON public.user_consent_settings 
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY update_own_consent_settings 
    ON public.user_consent_settings 
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY insert_own_consent_settings 
    ON public.user_consent_settings
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

---------------------------------------------------------
-- PART 3: CREATE OPTIMIZED SEARCH VIEWS
---------------------------------------------------------

-- Events search view
CREATE OR REPLACE VIEW events_with_search AS
SELECT    e.id,
    e.title,
    e.description,
    e.start_date,
    e.physical_location,
    e.meeting_url,
    e.event_type,
    e.industry_ids,
    e.netzero_category_ids,
    e.creator_user_id,
    p.first_name as creator_first_name,
    p.last_name as creator_last_name,
    p.organization as creator_organization,
    -- Only show creator email if they have consented
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM user_consent_settings ucs 
            WHERE ucs.user_id = e.creator_user_id 
            AND ucs.share_email_with_event_creators = true
        ) THEN p.email 
        ELSE NULL 
    END as creator_email,    (
        SELECT array_agg(c.name)
        FROM unnest(e.netzero_category_ids) AS category_id
        JOIN netzero_categories c ON c.id = category_id::UUID
    ) as category_names,
    e.text_search_vector as text_search
FROM events e
LEFT JOIN profiles p ON p.id = e.creator_user_id
WHERE 
    -- Only show public events and events from visible profiles
    p.profile_visibility = true;

-- Professionals search view (simplified without soft deletion)
CREATE OR REPLACE VIEW professionals_with_search AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    p.profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id    ) as category_names,
    l.name as location_name,
    i.name as industry_name,
    p.text_search_vector as text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    -- Note: Removed soft deletion checks since app uses hard deletion
    p.profile_visibility = true;

-- Grant necessary permissions
GRANT SELECT ON events_with_search TO authenticated;
GRANT SELECT ON events_with_search TO anon;
GRANT SELECT ON professionals_with_search TO authenticated;
GRANT SELECT ON professionals_with_search TO anon;

---------------------------------------------------------
-- PART 4: CREATE SECURE PROFILE VIEW
---------------------------------------------------------

-- Create secure view for public profiles (simplified)
CREATE OR REPLACE VIEW public_professional_profiles AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    -- Only expose email if user has consented and with proper access control
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM user_consent_settings ucs 
            WHERE ucs.user_id = p.id AND 
                (ucs.share_email_with_event_creators = true OR 
                ucs.share_email_with_attendees = true OR 
                ucs.share_contact_details = true)
        ) THEN p.email 
        ELSE NULL 
    END as email,
    -- Only show social links if profile is public
    CASE 
        WHEN p.profile_visibility = true THEN p.linkedin
        ELSE NULL 
    END as linkedin,
    CASE 
        WHEN p.profile_visibility = true THEN p.twitter
        ELSE NULL 
    END as twitter,
    p.profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    l.name as location_name,
    i.name as industry_name,
    -- Include category names but not IDs
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) as category_names,
    -- Include professional type names but not IDs
    (
        SELECT array_agg(pt.name)
        FROM profile_professional_types ppt
        JOIN sustainability_professional_types pt ON pt.id = ppt.professional_type_id
        WHERE ppt.profile_id = p.id
    ) as professional_types,
    -- Create a text search vector excluding sensitive data
    to_tsvector('english', 
        coalesce(p.first_name, '') || ' ' || 
        coalesce(p.last_name, '') || ' ' || 
        coalesce(p.bio, '') || ' ' || 
        coalesce(p.title, '') || ' ' ||
        coalesce(p.organization, '')
    ) as text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    -- Simplified: removed soft deletion checks since app uses hard deletion
    p.profile_visibility = true;

-- Grant access to the view
GRANT SELECT ON public_professional_profiles TO authenticated;
GRANT SELECT ON public_professional_profiles TO anon;

---------------------------------------------------------
-- PART 5: CREATE CONSENT MANAGEMENT FUNCTION
---------------------------------------------------------

-- Create function to update user consent settings
CREATE OR REPLACE FUNCTION update_user_consent_settings(
    p_share_email_with_event_creators BOOLEAN,
    p_share_email_with_attendees BOOLEAN,
    p_share_contact_details BOOLEAN
)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
    v_updated BOOLEAN;
BEGIN
    INSERT INTO user_consent_settings (
        user_id,
        share_email_with_event_creators,
        share_email_with_attendees,
        share_contact_details,
        consent_updated_at,
        updated_at
    )
    VALUES (
        auth.uid(),
        p_share_email_with_event_creators,
        p_share_email_with_attendees,
        p_share_contact_details,
        now(),
        now()
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET
        share_email_with_event_creators = p_share_email_with_event_creators,
        share_email_with_attendees = p_share_email_with_attendees,
        share_contact_details = p_share_contact_details,
        consent_updated_at = now(),
        updated_at = now();
        
    GET DIAGNOSTICS v_updated = ROW_COUNT;
    
    RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION update_user_consent_settings TO authenticated;

---------------------------------------------------------
-- PART 6: PERFORMANCE INDEXES
---------------------------------------------------------

-- Create additional performance indexes
CREATE INDEX IF NOT EXISTS idx_user_consent_settings_user_id ON user_consent_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_events_creator_user_id ON events(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_main_industry_id ON profiles(main_industry_id);
CREATE INDEX IF NOT EXISTS idx_profiles_location_id ON profiles(location_id);

-- Ensure pg_trgm extension is enabled for full-text search
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Add indexes on base tables rather than views (views can't have indexes)
-- Create a dedicated column in the events table for text search if needed
ALTER TABLE events ADD COLUMN IF NOT EXISTS text_search_vector tsvector 
GENERATED ALWAYS AS (
    to_tsvector('english', 
        coalesce(title, '') || ' ' || 
        coalesce(description, '') || ' ' ||
        coalesce(physical_location, '') || ' ' ||
        coalesce(meeting_url, '')
    )
) STORED;

-- Add a text search vector column to the profiles table as well
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS text_search_vector tsvector
GENERATED ALWAYS AS (
    to_tsvector('english', 
        coalesce(first_name, '') || ' ' || 
        coalesce(last_name, '') || ' ' || 
        coalesce(bio, '') || ' ' || 
        coalesce(title, '') || ' ' ||
        coalesce(organization, '')
    )
) STORED;

CREATE INDEX IF NOT EXISTS idx_events_text_search_vector ON events USING gin(text_search_vector);
CREATE INDEX IF NOT EXISTS idx_profiles_text_search_vector ON profiles USING gin(text_search_vector);
