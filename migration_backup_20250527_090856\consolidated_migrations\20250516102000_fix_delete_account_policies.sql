-- Enable RLS on all relevant tables
ALTER TABLE public.user_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Ensure cascade deletes are set up properly
ALTER TABLE public.user_connections 
  DROP CONSTRAINT IF EXISTS user_connections_user_id_fkey,
  ADD CONSTRAINT user_connections_user_id_fkey 
    FOREIGN KEY (user_id) 
    REFERENCES public.profiles(id) 
    ON DELETE CASCADE;

ALTER TABLE public.user_connections 
  DROP CONSTRAINT IF EXISTS user_connections_connection_id_fkey,
  ADD CONSTRAINT user_connections_connection_id_fkey 
    FOREIGN KEY (connection_id) 
    REFERENCES public.profiles(id) 
    ON DELETE CASCADE;

-- Add or update delete policies
DO $$ 
BEGIN
  -- User Connections
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_connections' AND policyname = 'Users can delete their connections') THEN
    CREATE POLICY "Users can delete their connections" ON public.user_connections
      FOR DELETE USING (user_id = auth.uid() OR connection_id = auth.uid());
  END IF;

  -- Social Posts
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'social_posts' AND policyname = 'Users can delete own posts') THEN
    CREATE POLICY "Users can delete own posts" ON public.social_posts
      FOR DELETE USING (user_id = auth.uid());
  END IF;

  -- Post Comments
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'post_comments' AND policyname = 'Users can delete own comments') THEN
    CREATE POLICY "Users can delete own comments" ON public.post_comments
      FOR DELETE USING (user_id = auth.uid());
  END IF;

  -- Post Likes
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'post_likes' AND policyname = 'Users can delete own likes') THEN
    CREATE POLICY "Users can delete own likes" ON public.post_likes
      FOR DELETE USING (user_id = auth.uid());
  END IF;

  -- Post Categories
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'post_categories' AND policyname = 'Users can delete categories from own posts') THEN
    CREATE POLICY "Users can delete categories from own posts" ON public.post_categories
      FOR DELETE USING (
        EXISTS (
          SELECT 1 FROM social_posts 
          WHERE social_posts.id = post_categories.post_id 
          AND social_posts.user_id = auth.uid()
        )
      );
  END IF;

  -- Profiles
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles' AND policyname = 'Users can delete own profile') THEN
    CREATE POLICY "Users can delete own profile" ON public.profiles
      FOR DELETE USING (id = auth.uid());
  END IF;
END $$;
