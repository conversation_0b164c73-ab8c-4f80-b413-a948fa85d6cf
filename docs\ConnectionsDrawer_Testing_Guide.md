# ConnectionsDrawer Testing Guide

## Quick Start Testing

### 1. **Access the Application**
- Open: http://localhost:8081/
- Navigate to the Social Feed page
- Click the "Connections" button to open the ConnectionsDrawer

### 2. **Browser DevTools Setup**
1. Open Browser DevTools (F12)
2. Go to the Console tab
3. Look for connection-related logs (we added extensive logging)
4. Go to Network tab to monitor API calls

## Testing Scenarios

### **Scenario 1: Cancel Connection Request (Primary Focus)**

**Test Steps:**
1. Go to "Sent" tab in ConnectionsDrawer
2. Find a pending request (or create one first)
3. Click the "X" (withdraw) button
4. **Observe:**
   - UI updates immediately (optimistic update)
   - Request disappears from the list
   - Console logs show: "Cancelling request: [requestId]"
   - Network tab shows `cancel_connection_request` RPC call

**Expected Behavior:**
- ✅ Immediate UI response (< 100ms)
- ✅ Request removed from sent list
- ✅ Success toast notification
- ✅ No UI flickering or state reversion

**Error Testing:**
- Disconnect internet, try canceling → should revert optimistic update
- Check console for error handling logs

### **Scenario 2: Accept/Reject Connection Requests**

**Test Steps:**
1. Go to "Requests" tab
2. Find pending requests (or have someone send you one)
3. Click Accept (✓) or Reject (✗)
4. **Observe:**
   - Immediate UI update
   - Request moves from "Requests" to "Connections" (if accepted)
   - Request disappears (if rejected)

**Expected Behavior:**
- ✅ Instant UI feedback
- ✅ Proper state transitions
- ✅ Toast notifications
- ✅ Tab switching on accept

### **Scenario 3: Send New Connection Requests**

**Test Steps:**
1. Go to "Suggested" tab
2. Click "Connect" on a suggested user
3. **Observe:**
   - Button shows loading state
   - User moves to "Sent" tab
   - Optimistic request appears with blue border

**Expected Behavior:**
- ✅ Optimistic UI updates
- ✅ Visual loading indicators
- ✅ Proper state management

### **Scenario 4: Remove Existing Connections**

**Test Steps:**
1. Go to "Connections" tab
2. Click "Remove" on an existing connection
3. **Observe:**
   - Immediate removal from list
   - Confirmation toast

**Expected Behavior:**
- ✅ Instant UI response
- ✅ Connection removed from list

## Performance Testing

### **1. Speed Testing**
- **Measure UI Response Time:**
  ```javascript
  // In browser console, time the operations:
  console.time('cancel-request');
  // Click cancel button
  console.timeEnd('cancel-request');
  ```

- **Expected Results:**
  - UI response: < 100ms
  - Total operation: < 2 seconds

### **2. Network Performance**
- Check Network tab in DevTools
- Look for RPC function calls:
  - `cancel_connection_request`
  - `respond_to_connection_request`
  - `remove_connection`
- Verify single database calls (not multiple queries)

### **3. State Consistency Testing**
- Perform rapid operations (click multiple buttons quickly)
- Refresh page and verify state is correct
- Test concurrent operations in multiple tabs

## Error Scenarios Testing

### **1. Network Interruption**
```javascript
// In browser console:
// Simulate network failure
navigator.serviceWorker.register('data:application/javascript,')
  .then(() => console.log('Network simulation ready'));
```

**Test Steps:**
1. Disable network connection
2. Try connection operations
3. Re-enable network
4. Verify state recovery

### **2. Authentication Issues**
1. Clear browser storage/cookies
2. Try connection operations
3. Should show authentication errors

### **3. Invalid Data**
1. Try operations on non-existent requests
2. Should handle gracefully with error messages

## Console Debugging

### **Key Log Messages to Look For:**

```javascript
// Successful operations:
"Requesting connection to: [userId]"
"Connection request cancelled successfully"
"respondToRequest called with: {requestId, newStatus}"

// Error scenarios:
"Error cancelling request:"
"Request not found:"
"Authentication required"
```

### **Monitor State Changes:**
```javascript
// In browser console, watch state:
window.addEventListener('storage', (e) => {
  console.log('Storage changed:', e.key, e.newValue);
});
```

## Automated Testing Script

You can run this in the browser console to test multiple scenarios:

```javascript
// ConnectionsDrawer Test Suite
const testConnections = async () => {
  console.log('🧪 Starting ConnectionsDrawer Tests...');
  
  // Test 1: Check if drawer opens
  const connectionsBtn = document.querySelector('[aria-label*="Connections"], button:contains("Connections")');
  if (connectionsBtn) {
    connectionsBtn.click();
    console.log('✅ Drawer opened');
  } else {
    console.log('❌ Connections button not found');
    return;
  }
  
  // Wait for drawer to load
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test 2: Check tabs
  const tabs = ['connections', 'requests', 'sent', 'suggested'];
  tabs.forEach(tab => {
    const tabElement = document.querySelector(`[value="${tab}"]`);
    if (tabElement) {
      console.log(`✅ ${tab} tab found`);
    } else {
      console.log(`❌ ${tab} tab missing`);
    }
  });
  
  console.log('🧪 Tests completed. Check results above.');
};

// Run the test
testConnections();
```

## Performance Benchmarks

### **Before Optimization (Expected Issues):**
- Double optimistic updates
- UI flickering
- Slow response times
- State inconsistencies

### **After Optimization (Current State):**
- Single optimistic updates
- Immediate UI response (< 100ms)
- Consistent state management
- Proper error handling

## Database Testing

### **Verify RPC Functions Exist:**
```sql
-- Run in Supabase SQL Editor:
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_name IN (
  'cancel_connection_request',
  'respond_to_connection_request', 
  'remove_connection'
);
```

### **Test RPC Functions Directly:**
```sql
-- Test cancel_connection_request
SELECT cancel_connection_request('some-uuid-here');

-- Test respond_to_connection_request
SELECT respond_to_connection_request('some-uuid-here', 'accepted');
```

## Mobile Testing

### **Responsive Design:**
1. Open DevTools
2. Toggle device emulation
3. Test on various screen sizes
4. Verify touch interactions work properly

### **Touch Gestures:**
- Tap to open drawer
- Swipe to dismiss (if implemented)
- Tap buttons for actions

## Browser Compatibility

Test in multiple browsers:
- ✅ Chrome (primary)
- ✅ Firefox
- ✅ Safari
- ✅ Edge

## Production Testing Checklist

Before deploying:

- [ ] All optimistic updates work correctly
- [ ] Error handling covers edge cases
- [ ] No console errors in production build
- [ ] Performance meets expectations
- [ ] Mobile responsiveness verified
- [ ] Cross-browser compatibility confirmed
- [ ] RPC functions deployed to production database

## Troubleshooting Common Issues

### **Issue: UI doesn't update immediately**
- Check if optimistic updates are enabled
- Verify state management in useUserConnections

### **Issue: Operations fail silently**
- Check authentication status
- Verify RPC functions exist in database
- Check network connectivity

### **Issue: State gets out of sync**
- Refresh the page
- Check for race conditions
- Verify error handling reverts state properly

## Success Metrics

**The optimization is successful if:**
- ✅ UI responds in < 100ms
- ✅ No visible flickering or state jumps
- ✅ Error scenarios handled gracefully
- ✅ State remains consistent across operations
- ✅ User experience feels fast and responsive

---

## Next Steps After Testing

1. **If issues found:** Report specific scenarios and browser console logs
2. **If all tests pass:** Ready for production deployment
3. **Performance tuning:** Monitor real-world usage for further optimizations
