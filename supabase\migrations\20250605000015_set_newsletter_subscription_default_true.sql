-- Set newsletter subscription default to TRUE for new user signups
-- Date: 2025-06-05

<PERSON><PERSON><PERSON>;

-- Update handle_new_user function to set newsletter_subscription to TRUE by default
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
BEGIN
  -- Insert a new profile record for the new user
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    email, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  )
  RETURNING id INTO profile_id;

  -- Create default user_consent_settings for the new user
  -- Only insert into columns that actually exist
  INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    true,  -- profile_visibility - default to public
    true,  -- newsletter_subscription - default to ENABLED (changed from false)
    true,  -- show_businesses - default to visible
    true,  -- show_events - default to visible
    true,  -- show_connections - default to visible
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (user_id) DO NOTHING;

  -- Create notification preferences
  -- Only insert if the table exists and has the expected structure
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'notification_preferences'
  ) THEN
    -- Check if table has individual boolean columns structure
    IF EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'notification_preferences' 
      AND column_name = 'post_likes'
    ) THEN
      -- Insert with individual boolean columns
      INSERT INTO public.notification_preferences (
        profile_id,
        email_notifications,
        post_likes,
        post_comments,
        comment_replies,
        comment_likes,
        connection_requests,
        connection_accepted,
        event_signups,
        event_updates,
        system_notifications,
        created_at,
        updated_at
      )
      VALUES (
        profile_id,
        true, -- email_notifications
        true, -- post_likes
        true, -- post_comments
        true, -- comment_replies
        true, -- comment_likes
        true, -- connection_requests
        true, -- connection_accepted
        true, -- event_signups
        true, -- event_updates
        true, -- system_notifications
        NOW(),
        NOW()
      )
      ON CONFLICT (profile_id) DO NOTHING;
    END IF;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the signup
    RAISE WARNING 'Error in handle_new_user function: %', SQLERRM;
    -- Still return NEW so the user signup doesn't fail
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment to reflect the current functionality
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates a profile record, user consent settings, and notification preferences when a new user signs up. Newsletter subscription defaults to TRUE.';

-- Fix views that still reference profiles.profile_visibility
-- These views need to be updated to use user_consent_settings.profile_visibility

-- Update event_signups_with_users view
DROP VIEW IF EXISTS public.event_signups_with_users CASCADE;
CREATE VIEW public.event_signups_with_users AS
SELECT
    es.id,
    es.event_id,
    es.user_id,
    es.created_at,
    es.updated_at,
    es.hide_attendance,
    es.gdpr_consent,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    COALESCE(ucs.profile_visibility, true) as profile_visibility,
    -- Create user object for compatibility
    jsonb_build_object(
        'id', p.id,
        'full_name', CONCAT(p.first_name, ' ', p.last_name),
        'first_name', p.first_name,
        'last_name', p.last_name,
        'avatar_url', p.avatar_url,
        'title', p.title,
        'organization', p.organization,
        'profile_visibility', COALESCE(ucs.profile_visibility, true)
    ) as user
FROM event_signups es
JOIN profiles p ON es.user_id = p.id
LEFT JOIN user_consent_settings ucs ON p.id = ucs.user_id;

-- Update training_course_enrollments_view
DROP VIEW IF EXISTS public.training_course_enrollments_view CASCADE;
CREATE OR REPLACE VIEW public.training_course_enrollments_view AS
SELECT
    e.id,
    e.course_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    e.gdpr_consent,
    e.completion_date,
    e.feedback,
    e.rating,
    e.certificate_issued,
    e.notes,
    CONCAT(p.first_name, ' ', p.last_name) AS user_name,
    p.avatar_url,
    p.title AS user_title,
    p.organization AS user_organization,
    COALESCE(ucs.profile_visibility, true) as profile_visibility,
    t.title AS course_title,
    t.organization_name AS course_provider
FROM training_course_enrollments e
JOIN profiles p ON e.profile_id = p.id
LEFT JOIN user_consent_settings ucs ON p.id = ucs.user_id
JOIN training_courses t ON e.course_id = t.id;

-- Grant access to the updated views
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.training_course_enrollments_view TO authenticated;

-- Add comments
COMMENT ON VIEW public.event_signups_with_users IS
'Secure view of event signups with user data. Uses profile_visibility from user_consent_settings table.';

COMMENT ON VIEW public.training_course_enrollments_view IS
'Secure view of course enrollments with user data. Uses profile_visibility from user_consent_settings table.';

-- Fix social media tables to ensure they have the correct column structure
-- Check and fix post_likes table
DO $$
BEGIN
    -- Check if post_likes has user_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'post_likes'
        AND column_name = 'user_id'
    ) THEN
        -- Ensure it references profiles(id)
        ALTER TABLE public.post_likes
        DROP CONSTRAINT IF EXISTS post_likes_user_id_fkey;

        ALTER TABLE public.post_likes
        ADD CONSTRAINT post_likes_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed post_likes.user_id foreign key to reference profiles(id)';
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'post_likes'
        AND column_name = 'profile_id'
    ) THEN
        -- If it has profile_id, rename it to user_id for consistency with frontend
        ALTER TABLE public.post_likes RENAME COLUMN profile_id TO user_id;

        ALTER TABLE public.post_likes
        DROP CONSTRAINT IF EXISTS post_likes_profile_id_fkey;

        ALTER TABLE public.post_likes
        ADD CONSTRAINT post_likes_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Renamed post_likes.profile_id to user_id and fixed foreign key';
    END IF;
END $$;

-- Check and fix comment_likes table
DO $$
BEGIN
    -- Check if comment_likes has user_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'comment_likes'
        AND column_name = 'user_id'
    ) THEN
        -- Ensure it references profiles(id)
        ALTER TABLE public.comment_likes
        DROP CONSTRAINT IF EXISTS comment_likes_user_id_fkey;

        ALTER TABLE public.comment_likes
        ADD CONSTRAINT comment_likes_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed comment_likes.user_id foreign key to reference profiles(id)';
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'comment_likes'
        AND column_name = 'profile_id'
    ) THEN
        -- If it has profile_id, rename it to user_id for consistency with frontend
        ALTER TABLE public.comment_likes RENAME COLUMN profile_id TO user_id;

        ALTER TABLE public.comment_likes
        DROP CONSTRAINT IF EXISTS comment_likes_profile_id_fkey;

        ALTER TABLE public.comment_likes
        ADD CONSTRAINT comment_likes_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Renamed comment_likes.profile_id to user_id and fixed foreign key';
    END IF;
END $$;

-- Check and fix post_comments table
DO $$
BEGIN
    -- Check if post_comments has user_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'post_comments'
        AND column_name = 'user_id'
    ) THEN
        -- Ensure it references profiles(id)
        ALTER TABLE public.post_comments
        DROP CONSTRAINT IF EXISTS post_comments_user_id_fkey;

        ALTER TABLE public.post_comments
        ADD CONSTRAINT post_comments_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed post_comments.user_id foreign key to reference profiles(id)';
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'post_comments'
        AND column_name = 'profile_id'
    ) THEN
        -- If it has profile_id, rename it to user_id for consistency with frontend
        ALTER TABLE public.post_comments RENAME COLUMN profile_id TO user_id;

        ALTER TABLE public.post_comments
        DROP CONSTRAINT IF EXISTS post_comments_profile_id_fkey;

        ALTER TABLE public.post_comments
        ADD CONSTRAINT post_comments_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Renamed post_comments.profile_id to user_id and fixed foreign key';
    END IF;
END $$;

COMMIT;
