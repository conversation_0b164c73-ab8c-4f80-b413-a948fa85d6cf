-- Add parent_id column to industries table for hierarchical structure

-- Add the parent_id column
ALTER TABLE public.industries
ADD COLUMN IF NOT EXISTS parent_id UUID REFERENCES public.industries(id);

-- Add index for better query performance on parent_id lookups
CREATE INDEX IF NOT EXISTS industries_parent_id_idx ON public.industries(parent_id);

-- Add a comment explaining the column's purpose
COMMENT ON COLUMN public.industries.parent_id IS 'Reference to the parent industry for hierarchical categorization.';

-- Update existing records to set appropriate parent-child relationships
DO $$
DECLARE
    main_industry_id UUID;
    sub_industry record;
BEGIN
    -- Example hierarchical data
    -- First, create main industry categories
    INSERT INTO public.industries (name, description, parent_id)
    VALUES
        ('Energy and Utilities', 'Energy generation, distribution, and utilities', NULL)
    ON CONFLICT (name) DO UPDATE
    SET description = EXCLUDED.description, parent_id = NULL
    RETURNING id INTO main_industry_id;

    -- Then set up sub-industries under Energy and Utilities
    UPDATE public.industries
    SET parent_id = main_industry_id
    WHERE name IN (
        'Renewable Energy',
        'Power Generation',
        'Energy Distribution',
        'Utilities Management'
    );

    -- Repeat for other main categories
    INSERT INTO public.industries (name, description, parent_id)
    VALUES
        ('Manufacturing and Production', 'Industrial manufacturing and production processes', NULL)
    ON CONFLICT (name) DO UPDATE
    SET description = EXCLUDED.description, parent_id = NULL
    RETURNING id INTO main_industry_id;

    -- Set sub-industries for Manufacturing
    UPDATE public.industries
    SET parent_id = main_industry_id
    WHERE name IN (
        'Sustainable Manufacturing',
        'Green Production',
        'Industrial Processing',
        'Clean Technology Manufacturing'
    );

    -- Continue with other main categories
    INSERT INTO public.industries (name, description, parent_id)
    VALUES
        ('Construction and Infrastructure', 'Construction and infrastructure development', NULL)
    ON CONFLICT (name) DO UPDATE
    SET description = EXCLUDED.description, parent_id = NULL
    RETURNING id INTO main_industry_id;

    -- Set sub-industries for Construction
    UPDATE public.industries
    SET parent_id = main_industry_id
    WHERE name IN (
        'Green Building',
        'Sustainable Construction',
        'Infrastructure Development',
        'Building Materials'
    );
END $$;
