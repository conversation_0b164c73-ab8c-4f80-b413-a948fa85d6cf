-- Create a security-hardened delete account function 
-- that can be called from Edge Functions to avoid exposing service role key

-- First, create a function that handles the deletion logic
CREATE OR REPLACE FUNCTION delete_user_account(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  result BOOLEAN := FALSE;
BEGIN
  -- Only proceed if user exists
  IF EXISTS (SELECT 1 FROM auth.users WHERE id = user_id) THEN
    -- 1. Delete likes first as they reference comments and posts
    DELETE FROM post_likes WHERE user_id = $1;
    
    -- 2. Delete all comments as they reference posts
    DELETE FROM post_comments WHERE user_id = $1;
    
    -- 3. Delete posts (which will trigger image deletion via our trigger)
    DELETE FROM social_posts WHERE user_id = $1;
    
    -- 4. Delete user businesses (which will cascade to related tables)
    DELETE FROM businesses WHERE owner_id = $1;
    
    -- 5. Delete profile (which will cascade to connections)
    DELETE FROM profiles WHERE id = $1;
    
    -- 6. Finally, delete the auth user (requires admin rights)
    -- This will be handled by the Edge Function with service role key
    
    result := TRUE;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments explaining the security model
COMMENT ON FUNCTION delete_user_account IS 
'Securely deletes all user data in the correct order to respect foreign key constraints. 
This function uses SECURITY DEFINER to run with elevated privileges but should only be 
called from an authenticated context where the caller has been verified as the 
account owner or an admin. The final auth.users deletion must be done via an Edge Function.';

-- Grant permission to authenticated users to call this function
GRANT EXECUTE ON FUNCTION delete_user_account TO authenticated;

-- Create a Row Level Security policy to ensure users can only delete their own accounts
CREATE POLICY delete_own_account_policy
  ON profiles
  FOR DELETE
  TO authenticated
  USING (auth.uid() = id);
