-- Create training course enrollments table to track user enrollment in courses
CREATE TABLE training_course_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES training_courses(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    completion_date TIMESTAMPTZ,
    feedback TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    certificate_issued BOOLEAN DEFAULT FALSE,
    notes TEXT,
    UNIQUE(course_id, user_id)
);

-- Create trigger to automatically update updated_at timestamp
CREATE TRIGGER set_training_course_enrollments_updated_at
BEFORE UPDATE ON training_course_enrollments
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Add Row Level Security (RLS) policies
ALTER TABLE training_course_enrollments ENABLE ROW LEVEL SECURITY;

-- Creator of the course can see all enrollments for their courses
CREATE POLICY "Course creators can view enrollments for their courses"
ON training_course_enrollments
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM training_courses 
        WHERE id = training_course_enrollments.course_id 
        AND creator_id = auth.uid()
    )
);

-- Users can view their own enrollments
CREATE POLICY "Users can view their own enrollments"
ON training_course_enrollments
FOR SELECT
USING (user_id = auth.uid());

-- Users can insert their own enrollments
CREATE POLICY "Users can enroll in courses"
ON training_course_enrollments
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- Users can update their own enrollments (e.g., to cancel)
CREATE POLICY "Users can update their own enrollments"
ON training_course_enrollments
FOR UPDATE
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid() AND status IN ('pending', 'cancelled'));

-- Course creators can update any enrollment for their courses
CREATE POLICY "Course creators can update enrollments for their courses"
ON training_course_enrollments
FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM training_courses 
        WHERE id = training_course_enrollments.course_id 
        AND creator_id = auth.uid()
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM training_courses 
        WHERE id = training_course_enrollments.course_id 
        AND creator_id = auth.uid()
    )
);

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON training_course_enrollments TO authenticated;

-- Create index for fast lookups
CREATE INDEX idx_training_course_enrollments_course_id ON training_course_enrollments(course_id);
CREATE INDEX idx_training_course_enrollments_user_id ON training_course_enrollments(user_id);
CREATE INDEX idx_training_course_enrollments_status ON training_course_enrollments(status);

-- Add comment to describe the table
COMMENT ON TABLE training_course_enrollments IS 'Tracks user enrollment in training courses';
