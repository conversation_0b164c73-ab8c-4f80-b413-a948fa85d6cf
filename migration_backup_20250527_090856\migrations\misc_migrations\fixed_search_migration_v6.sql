-- =======================================================
-- FIXED SEARCH MIGRATION v6
-- Addressing user_consent_settings table dependency issue
-- =======================================================

---------------------------------------------------------
-- PART 1: USER CONSENT SETTINGS TABLE FIRST
---------------------------------------------------------

-- Create the user_consent_settings table first since other objects depend on it
CREATE TABLE IF NOT EXISTS public.user_consent_settings (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    share_email_with_event_creators BOOLEAN NOT NULL DEFAULT FALSE,
    share_email_with_attendees BOOLEAN NOT NULL DEFAULT FALSE,
    share_contact_details BOOLEAN NOT NULL DEFAULT FALSE,
    consent_updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS to the consent settings table
ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

-- Users can only view and edit their own consent settings
CREATE POLICY view_own_consent_settings 
    ON public.user_consent_settings 
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY update_own_consent_settings 
    ON public.user_consent_settings 
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY insert_own_consent_settings 
    ON public.user_consent_settings 
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Create index for user_id to speed up joins
CREATE INDEX IF NOT EXISTS idx_user_consent_settings_user_id ON public.user_consent_settings(user_id);

---------------------------------------------------------
-- PART 2: SEARCH OPTIMIZATION VIEWS
---------------------------------------------------------

-- Create events with search view for improved search performance
CREATE OR REPLACE VIEW events_with_search AS
SELECT 
    e.*,
    setweight(to_tsvector('english', coalesce(e.title, '')), 'A') ||
    setweight(to_tsvector('english', coalesce(e.description, '')), 'B') ||
    setweight(to_tsvector('english', 
        coalesce(l.name, '') || ' ' || 
        coalesce(l.city, '') || ' ' || 
        coalesce(l.region, '') || ' ' || 
        coalesce(l.country, '') || ' ' ||
        coalesce(c.name, '')
    ), 'C') as document
FROM events e
LEFT JOIN locations l ON l.id = e.location_id
LEFT JOIN categories c ON c.id = e.category_id
WHERE e.deleted_at IS NULL;

-- Create index for full text search on events
CREATE INDEX IF NOT EXISTS idx_events_title_trgm ON events USING gin(title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_description_trgm ON events USING gin(description gin_trgm_ops);

-- Create view for professional profiles with search
CREATE OR REPLACE VIEW professionals_with_search AS
SELECT 
    p.*,
    setweight(to_tsvector('english', 
        coalesce(p.first_name, '') || ' ' || 
        coalesce(p.last_name, '')
    ), 'A') ||
    setweight(to_tsvector('english', 
        coalesce(p.title, '') || ' ' || 
        coalesce(p.organization, '')
    ), 'B') ||
    setweight(to_tsvector('english', 
        coalesce(p.bio, '') || ' ' || 
        coalesce(l.name, '') || ' ' || 
        coalesce(l.city, '') || ' ' || 
        coalesce(l.region, '') || ' ' || 
        coalesce(l.country, '') || ' ' ||
        coalesce(i.name, '')
    ), 'C') as document
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    p.profile_visibility = true
    -- Exclude deleted or inactive profiles
    AND p.deleted_at IS NULL
    AND p.is_active = true;

-- Create index for full text search on profiles
CREATE INDEX IF NOT EXISTS idx_profiles_name_trgm ON profiles USING gin((first_name || ' ' || last_name) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_profiles_bio_trgm ON profiles USING gin(bio gin_trgm_ops);

---------------------------------------------------------
-- PART 3: SECURE PUBLIC PROFESSIONAL PROFILES VIEW
---------------------------------------------------------

-- Create the secure professional profiles view that respects consent settings
CREATE OR REPLACE VIEW public_professional_profiles AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    -- Only expose email if user has explicitly consented
    CASE 
        WHEN (
            EXISTS (
                SELECT 1 FROM user_consent_settings ucs 
                WHERE ucs.user_id = p.id AND 
                (ucs.share_email_with_event_creators = true OR 
                 ucs.share_email_with_attendees = true OR 
                 ucs.share_contact_details = true)
            )
        ) THEN p.email 
        ELSE NULL 
    END as email,
    -- Only expose phone if user has consented to share contact details
    CASE 
        WHEN (
            EXISTS (
                SELECT 1 FROM user_consent_settings ucs 
                WHERE ucs.user_id = p.id AND ucs.share_contact_details = true
            )
        ) THEN p.phone 
        ELSE NULL 
    END as phone,
    p.location_id,
    l.name as location_name,
    l.city as location_city,
    l.region as location_region,
    l.country as location_country,
    p.main_industry_id,
    i.name as industry_name,
    -- Search-friendly text field
    to_tsvector('english',
        coalesce(p.first_name, '') || ' ' || 
        coalesce(p.last_name, '') || ' ' || 
        coalesce(p.bio, '') || ' ' || 
        coalesce(p.title, '') || ' ' ||
        coalesce(p.organization, '')
    ) as text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    p.profile_visibility = true
    -- Exclude deleted or inactive profiles
    AND p.deleted_at IS NULL
    AND p.is_active = true;

-- Grant access to the view
GRANT SELECT ON public_professional_profiles TO authenticated;
GRANT SELECT ON public_professional_profiles TO anon;

---------------------------------------------------------
-- PART 4: CONSENT MANAGEMENT FUNCTIONS
---------------------------------------------------------

-- Function to update user consent settings
CREATE OR REPLACE FUNCTION update_user_consent_settings(
    p_share_email_with_event_creators BOOLEAN,
    p_share_email_with_attendees BOOLEAN,
    p_share_contact_details BOOLEAN
)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
    v_updated BOOLEAN;
BEGIN
    INSERT INTO user_consent_settings (
        user_id,
        share_email_with_event_creators,
        share_email_with_attendees,
        share_contact_details,
        consent_updated_at,
        updated_at
    )
    VALUES (
        auth.uid(),
        p_share_email_with_event_creators,
        p_share_email_with_attendees,
        p_share_contact_details,
        now(),
        now()
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET
        share_email_with_event_creators = EXCLUDED.share_email_with_event_creators,
        share_email_with_attendees = EXCLUDED.share_email_with_attendees,
        share_contact_details = EXCLUDED.share_contact_details,
        consent_updated_at = EXCLUDED.consent_updated_at,
        updated_at = EXCLUDED.updated_at;
        
    GET DIAGNOSTICS v_updated = ROW_COUNT;
    RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;
