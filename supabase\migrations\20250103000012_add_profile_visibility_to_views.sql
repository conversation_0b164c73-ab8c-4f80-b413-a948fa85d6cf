-- Add profile_visibility to secure views for profile linking
-- This allows the frontend to show profile links only for users with public profiles

BEGIN;

-- Update event_signups_with_users view to include profile_visibility
DROP VIEW IF EXISTS public.event_signups_with_users;
CREATE VIEW public.event_signups_with_users AS
SELECT 
    es.id,
    es.event_id,
    es.user_id,
    es.created_at,
    es.updated_at,
    es.hide_attendance,
    es.gdpr_consent,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.profile_visibility,
    -- Create user object for compatibility
    jsonb_build_object(
        'id', p.id,
        'full_name', CONCAT(p.first_name, ' ', p.last_name),
        'first_name', p.first_name,
        'last_name', p.last_name,
        'avatar_url', p.avatar_url,
        'title', p.title,
        'organization', p.organization,
        'profile_visibility', p.profile_visibility
    ) as user
FROM event_signups es
JOIN profiles p ON es.user_id = p.id;

-- Update training_course_enrollments_view to include profile_visibility
DROP VIEW IF EXISTS public.training_course_enrollments_view;
CREATE OR REPLACE VIEW public.training_course_enrollments_view AS
SELECT
    e.id,
    e.course_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    e.gdpr_consent,
    e.completion_date,
    e.feedback,
    e.rating,
    e.certificate_issued,
    e.notes,
    CONCAT(p.first_name, ' ', p.last_name) AS user_name,
    p.avatar_url,
    p.title AS user_title,
    p.organization AS user_organization,
    p.profile_visibility,
    -- REMOVED: p.email AS user_email (for security)
    t.title AS course_title,
    t.organization_name AS course_provider
FROM training_course_enrollments e
JOIN profiles p ON e.profile_id = p.id
JOIN training_courses t ON e.course_id = t.id;

-- Grant access to the updated views
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.training_course_enrollments_view TO authenticated;

-- Add comments
COMMENT ON VIEW public.event_signups_with_users IS 
'Secure view of event signups with user data. Includes profile_visibility for conditional profile linking.';

COMMENT ON VIEW public.training_course_enrollments_view IS 
'Secure view of course enrollments with user data. Includes profile_visibility for conditional profile linking.';

COMMIT;

-- Verify the changes
SELECT 'Profile visibility added to secure views successfully' as status;
