-- Revise jobs table and add junction tables

-- Drop existing jobs-related tables
DROP TABLE IF EXISTS public.jobs CASCADE;
DROP TABLE IF EXISTS public.job_locations CASCADE;
DROP TABLE IF EXISTS public.job_industries CASCADE;
DROP TABLE IF EXISTS public.job_categories CASCADE;

-- Create the jobs table
CREATE TABLE public.jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    organization TEXT NOT NULL,
    description TEXT NOT NULL,
    salary_bracket salary_bracket NOT NULL,
    location_type job_location_type NOT NULL,
    job_function job_function NOT NULL,
    contract_type job_contract_type NOT NULL,
    hours_type job_hours_type NOT NULL,
    office_location TEXT,
    application_url TEXT NOT NULL,
    date_posted TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'draft', 'closed')),
    CONSTRAINT valid_office_location CHECK (
        (location_type = 'Remote' AND office_location IS NULL) OR
        (location_type IN ('Hybrid', 'Office') AND office_location IS NOT NULL)
    )
);

-- Create junction table for job locations
CREATE TABLE public.job_locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES public.locations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(job_id, location_id)
);

-- Create junction table for job industries
CREATE TABLE public.job_industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(job_id, industry_id)
);

-- Create junction table for job categories
CREATE TABLE public.job_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES public.net_zero_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(job_id, category_id)
);

-- Enable RLS on all tables
ALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_categories ENABLE ROW LEVEL SECURITY;

-- RLS policies for jobs table
CREATE POLICY "Anyone can read jobs" ON public.jobs
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create jobs" ON public.jobs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can see their own jobs" ON public.jobs
    FOR SELECT USING (auth.uid() = created_by);

CREATE POLICY "Users can update their own jobs" ON public.jobs
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own jobs" ON public.jobs
    FOR DELETE USING (auth.uid() = created_by);

-- RLS policies for job_locations
CREATE POLICY "Anyone can read job locations" ON public.job_locations
    FOR SELECT USING (true);

CREATE POLICY "Job creators can manage locations" ON public.job_locations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE jobs.id = job_locations.job_id
            AND jobs.created_by = auth.uid()
        )
    );

-- RLS policies for job_industries
CREATE POLICY "Anyone can read job industries" ON public.job_industries
    FOR SELECT USING (true);

CREATE POLICY "Job creators can manage industries" ON public.job_industries
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE jobs.id = job_industries.job_id
            AND jobs.created_by = auth.uid()
        )
    );

-- RLS policies for job_categories
CREATE POLICY "Anyone can read job categories" ON public.job_categories
    FOR SELECT USING (true);

CREATE POLICY "Job creators can manage categories" ON public.job_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE jobs.id = job_categories.job_id
            AND jobs.created_by = auth.uid()
        )
    );

-- Add indexes for common search patterns
CREATE INDEX jobs_date_posted_idx ON public.jobs(date_posted DESC);
CREATE INDEX jobs_location_type_idx ON public.jobs(location_type);
CREATE INDEX jobs_job_function_idx ON public.jobs(job_function);
CREATE INDEX jobs_contract_type_idx ON public.jobs(contract_type);
CREATE INDEX jobs_status_idx ON public.jobs(status);
CREATE INDEX jobs_created_by_idx ON public.jobs(created_by);

-- Add indexes for junction tables
CREATE INDEX job_locations_job_idx ON public.job_locations(job_id);
CREATE INDEX job_locations_location_idx ON public.job_locations(location_id);
CREATE INDEX job_industries_job_idx ON public.job_industries(job_id);
CREATE INDEX job_industries_industry_idx ON public.job_industries(industry_id);
CREATE INDEX job_categories_job_idx ON public.job_categories(job_id);
CREATE INDEX job_categories_category_idx ON public.job_categories(category_id);

-- Create a view for easy querying of jobs with all their related data
CREATE OR REPLACE VIEW public.jobs_with_details AS
SELECT 
    j.*,
    array_agg(DISTINCT l.location_id) as location_ids,
    array_agg(DISTINCT loc.name) as locations,
    array_agg(DISTINCT i.industry_id) as industry_ids,
    array_agg(DISTINCT ind.name) as industries,
    array_agg(DISTINCT c.category_id) as category_ids,
    array_agg(DISTINCT cat.name) as categories
FROM public.jobs j
LEFT JOIN public.job_locations l ON j.id = l.job_id
LEFT JOIN public.locations loc ON l.location_id = loc.id
LEFT JOIN public.job_industries i ON j.id = i.job_id
LEFT JOIN public.industries ind ON i.industry_id = ind.id
LEFT JOIN public.job_categories c ON j.id = c.job_id
LEFT JOIN public.net_zero_categories cat ON c.category_id = cat.id
GROUP BY j.id;
