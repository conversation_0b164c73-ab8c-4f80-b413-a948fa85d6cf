-- IMMEDIATE TRIGGER FIX - Recreate the signup trigger (CORRECTED)
-- Date: 2025-01-06

BEGIN;

-- =====================================================
-- 1. RECREATE THE HANDLE_NEW_USER FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
BEGIN
  RAISE NOTICE 'handle_new_user triggered for user: %', NEW.id;
  
  -- Insert a new profile record for the new user
  BEGIN
    INSERT INTO public.profiles (
      id, 
      first_name, 
      last_name, 
      email, 
      social_visibility, 
      subscription_tier, 
      subscription_status, 
      created_at, 
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.raw_user_meta_data->>'first_name',
      NEW.raw_user_meta_data->>'last_name',
      NEW.email,
      'public',
      'none',
      'trial',
      NEW.created_at,
      NEW.created_at
    )
    RETURNING id INTO profile_id;
    
    RAISE NOTICE 'Profile created for user: %', NEW.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'Profile creation failed for user %: %', NEW.id, SQLERRM;
      RETURN NEW; -- Don't fail the signup
  END;

  -- Create default user_consent_settings
  BEGIN
    INSERT INTO public.user_consent_settings (
      user_id,
      profile_visibility,
      newsletter_subscription,
      show_businesses,
      show_events,
      show_connections,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      true, true, true, true, true,
      NEW.created_at,
      NEW.created_at
    )
    ON CONFLICT (user_id) DO NOTHING;
    
    RAISE NOTICE 'User consent settings created for user: %', NEW.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'User consent settings creation failed for user %: %', NEW.id, SQLERRM;
  END;

  -- Create notification preferences
  BEGIN
    INSERT INTO public.notification_preferences (
      profile_id,
      email_notifications,
      post_likes,
      post_comments,
      comment_replies,
      comment_likes,
      connection_requests,
      connection_accepted,
      event_signups,
      event_updates,
      system_notifications,
      created_at,
      updated_at
    )
    VALUES (
      profile_id,
      true, true, true, true, true, true, true, true, true, true,
      NEW.created_at, NEW.created_at
    )
    ON CONFLICT (profile_id) DO NOTHING;
    
    RAISE NOTICE 'Notification preferences created for user: %', NEW.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'Notification preferences creation failed for user %: %', NEW.id, SQLERRM;
  END;

  RAISE NOTICE 'handle_new_user completed successfully for user: %', NEW.id;
  RETURN NEW;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Critical error in handle_new_user for user %: %', NEW.id, SQLERRM;
    RETURN NEW; -- Don't fail the signup
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 2. RECREATE THE TRIGGER
-- =====================================================

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 3. FIX THE CURRENT USER IMMEDIATELY
-- =====================================================

-- Create missing records for the current user
DO $$
DECLARE
    target_user_id UUID := '2084c3f6-839c-407e-a1b2-d9b162701bb3';
    user_record RECORD;
BEGIN
    -- Get user data
    SELECT 
        id, email, created_at, updated_at, raw_user_meta_data
    INTO user_record
    FROM auth.users 
    WHERE id = target_user_id;
    
    IF user_record.id IS NOT NULL THEN
        -- Create profile
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            email, 
            social_visibility, 
            subscription_tier, 
            subscription_status, 
            created_at, 
            updated_at
        )
        VALUES (
            user_record.id,
            user_record.raw_user_meta_data->>'first_name',
            user_record.raw_user_meta_data->>'last_name',
            user_record.email,
            'public',
            'none',
            'trial',
            user_record.created_at,
            user_record.created_at
        )
        ON CONFLICT (id) DO NOTHING;
        
        -- Create user consent settings
        INSERT INTO public.user_consent_settings (
            user_id,
            profile_visibility,
            newsletter_subscription,
            show_businesses,
            show_events,
            show_connections,
            created_at,
            updated_at
        )
        VALUES (
            user_record.id,
            true, true, true, true, true,
            user_record.created_at,
            user_record.created_at
        )
        ON CONFLICT (user_id) DO NOTHING;

        -- Create notification preferences
        INSERT INTO public.notification_preferences (
            profile_id,
            email_notifications,
            post_likes,
            post_comments,
            comment_replies,
            comment_likes,
            connection_requests,
            connection_accepted,
            event_signups,
            event_updates,
            system_notifications,
            created_at,
            updated_at
        )
        VALUES (
            user_record.id,
            true, true, true, true, true, true, true, true, true, true,
            user_record.created_at, user_record.created_at
        )
        ON CONFLICT (profile_id) DO NOTHING;
        
        RAISE NOTICE 'Fixed records for user: %', target_user_id;
    END IF;
END $$;

-- =====================================================
-- 4. TEST THE TRIGGER
-- =====================================================

-- Test by creating a dummy user (will be deleted immediately)
DO $$
DECLARE
    test_user_id UUID := '11111111-1111-1111-1111-111111111111';
BEGIN
    -- Clean up any existing test user
    DELETE FROM auth.users WHERE id = test_user_id;
    
    -- Insert test user to trigger the function
    INSERT INTO auth.users (
        id, 
        email, 
        created_at, 
        updated_at,
        raw_user_meta_data
    ) VALUES (
        test_user_id,
        '<EMAIL>',
        NOW(),
        NOW(),
        '{"first_name": "Test", "last_name": "Trigger"}'::jsonb
    );
    
    -- Check if records were created
    IF EXISTS (SELECT 1 FROM public.profiles WHERE id = test_user_id) THEN
        RAISE NOTICE 'SUCCESS: Trigger is working - profile created';
    ELSE
        RAISE WARNING 'FAILED: Trigger not working - no profile created';
    END IF;
    
    -- Clean up test user
    DELETE FROM auth.users WHERE id = test_user_id;
    
END $$;

COMMIT;
