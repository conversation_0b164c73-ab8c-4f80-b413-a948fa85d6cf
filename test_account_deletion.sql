-- Test account deletion functionality
-- This script tests if account deletion properly cascades through all related tables

-- =====================================================
-- 1. CHECK FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Check all foreign key constraints that reference auth.users or profiles
SELECT 
    tc.table_schema,
    tc.table_name,
    kcu.column_name,
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND (
        (ccu.table_schema = 'auth' AND ccu.table_name = 'users')
        OR (ccu.table_schema = 'public' AND ccu.table_name = 'profiles')
    )
ORDER BY tc.table_schema, tc.table_name;

-- =====================================================
-- 2. SIMULATE ACCOUNT DELETION TEST
-- =====================================================

-- Create a test function to simulate account deletion
CREATE OR REPLACE FUNCTION test_account_deletion_simulation()
RETURNS TABLE(
    step_name TEXT,
    table_name TEXT,
    records_before INTEGER,
    records_after INTEGER,
    deletion_successful BOOLEAN,
    error_message TEXT
) AS $$
DECLARE
    test_user_id UUID := '*************-9999-9999-************';
    test_email TEXT := '<EMAIL>';
    step_name TEXT;
    table_name TEXT;
    records_before INTEGER;
    records_after INTEGER;
    deletion_successful BOOLEAN;
    error_message TEXT;
BEGIN
    -- Clean up any existing test data first
    DELETE FROM auth.users WHERE id = test_user_id;
    
    -- Step 1: Create test user and related records
    step_name := 'CREATE_TEST_USER';
    table_name := 'auth.users';
    
    BEGIN
        -- Insert test user into auth.users (this should trigger profile creation)
        INSERT INTO auth.users (
            id, 
            email, 
            created_at, 
            updated_at,
            raw_user_meta_data
        ) VALUES (
            test_user_id,
            test_email,
            NOW(),
            NOW(),
            '{"first_name": "Test", "last_name": "User"}'::jsonb
        );
        
        deletion_successful := TRUE;
        error_message := NULL;
    EXCEPTION
        WHEN OTHERS THEN
            deletion_successful := FALSE;
            error_message := SQLERRM;
    END;
    
    RETURN QUERY SELECT step_name, table_name, 0, 1, deletion_successful, error_message;
    
    -- Wait a moment for triggers to complete
    PERFORM pg_sleep(0.1);
    
    -- Step 2: Check what records were created
    SELECT COUNT(*)::INTEGER FROM public.profiles WHERE id = test_user_id INTO records_before;
    RETURN QUERY SELECT 'CHECK_PROFILE_CREATED'::TEXT, 'public.profiles'::TEXT, 0, records_before, (records_before > 0), NULL::TEXT;
    
    SELECT COUNT(*)::INTEGER FROM public.notification_preferences WHERE profile_id = test_user_id INTO records_before;
    RETURN QUERY SELECT 'CHECK_NOTIFICATION_PREFS_CREATED'::TEXT, 'public.notification_preferences'::TEXT, 0, records_before, (records_before > 0), NULL::TEXT;
    
    SELECT COUNT(*)::INTEGER FROM public.user_consent_settings WHERE user_id = test_user_id INTO records_before;
    RETURN QUERY SELECT 'CHECK_CONSENT_SETTINGS_CREATED'::TEXT, 'public.user_consent_settings'::TEXT, 0, records_before, (records_before > 0), NULL::TEXT;
    
    -- Step 3: Test deletion cascade
    step_name := 'DELETE_USER';
    table_name := 'auth.users';
    
    -- Count records before deletion
    SELECT COUNT(*)::INTEGER FROM public.profiles WHERE id = test_user_id INTO records_before;
    
    BEGIN
        -- Delete the user (this should cascade to all related tables)
        DELETE FROM auth.users WHERE id = test_user_id;
        deletion_successful := TRUE;
        error_message := NULL;
    EXCEPTION
        WHEN OTHERS THEN
            deletion_successful := FALSE;
            error_message := SQLERRM;
    END;
    
    -- Count records after deletion
    SELECT COUNT(*)::INTEGER FROM public.profiles WHERE id = test_user_id INTO records_after;
    
    RETURN QUERY SELECT step_name, 'public.profiles'::TEXT, records_before, records_after, (records_after = 0), error_message;
    
    -- Check other tables
    SELECT COUNT(*)::INTEGER FROM public.notification_preferences WHERE profile_id = test_user_id INTO records_after;
    RETURN QUERY SELECT 'CASCADE_CHECK'::TEXT, 'public.notification_preferences'::TEXT, 0, records_after, (records_after = 0), NULL::TEXT;
    
    SELECT COUNT(*)::INTEGER FROM public.user_consent_settings WHERE user_id = test_user_id INTO records_after;
    RETURN QUERY SELECT 'CASCADE_CHECK'::TEXT, 'public.user_consent_settings'::TEXT, 0, records_after, (records_after = 0), NULL::TEXT;
    
    -- Check other important tables that should cascade
    SELECT COUNT(*)::INTEGER FROM public.user_connections WHERE user_id = test_user_id OR connection_id = test_user_id INTO records_after;
    RETURN QUERY SELECT 'CASCADE_CHECK'::TEXT, 'public.user_connections'::TEXT, 0, records_after, (records_after = 0), NULL::TEXT;
    
    SELECT COUNT(*)::INTEGER FROM public.event_signups WHERE profile_id = test_user_id INTO records_after;
    RETURN QUERY SELECT 'CASCADE_CHECK'::TEXT, 'public.event_signups'::TEXT, 0, records_after, (records_after = 0), NULL::TEXT;
    
    SELECT COUNT(*)::INTEGER FROM public.training_course_enrollments WHERE profile_id = test_user_id INTO records_after;
    RETURN QUERY SELECT 'CASCADE_CHECK'::TEXT, 'public.training_course_enrollments'::TEXT, 0, records_after, (records_after = 0), NULL::TEXT;
    
    -- Final cleanup
    DELETE FROM auth.users WHERE id = test_user_id;
    
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. RUN THE DELETION TEST
-- =====================================================

-- Run the account deletion simulation
SELECT * FROM test_account_deletion_simulation();

-- =====================================================
-- 4. CHECK FOR ORPHANED RECORDS
-- =====================================================

-- Check for any orphaned records that might indicate cascade issues
SELECT 
    'Profiles without auth users' as issue_type,
    COUNT(*) as count
FROM public.profiles p
LEFT JOIN auth.users u ON p.id = u.id
WHERE u.id IS NULL;

SELECT 
    'Notification preferences without profiles' as issue_type,
    COUNT(*) as count
FROM public.notification_preferences np
LEFT JOIN public.profiles p ON np.profile_id = p.id
WHERE p.id IS NULL;

SELECT 
    'User consent settings without auth users' as issue_type,
    COUNT(*) as count
FROM public.user_consent_settings ucs
LEFT JOIN auth.users u ON ucs.user_id = u.id
WHERE u.id IS NULL;

SELECT 
    'User connections with invalid user_id' as issue_type,
    COUNT(*) as count
FROM public.user_connections uc
LEFT JOIN public.profiles p ON uc.user_id = p.id
WHERE p.id IS NULL;

SELECT 
    'User connections with invalid connection_id' as issue_type,
    COUNT(*) as count
FROM public.user_connections uc
LEFT JOIN public.profiles p ON uc.connection_id = p.id
WHERE p.id IS NULL;

-- Clean up the test function
DROP FUNCTION IF EXISTS test_account_deletion_simulation();
