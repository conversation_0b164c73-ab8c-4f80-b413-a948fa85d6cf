-- This migration provides diagnostic queries to identify issues with nested queries
-- for businesses, business_relevant_industries, and industries tables

-- First, let's check if all tables exist
DO $$
BEGIN
    RAISE NOTICE 'Checking if required tables exist:';
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'businesses') THEN
        RAISE NOTICE 'Table public.businesses exists';
    ELSE
        RAISE NOTICE 'Table public.businesses DOES NOT exist';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'business_relevant_industries') THEN
        RAISE NOTICE 'Table public.business_relevant_industries exists';
    ELSE
        RAISE NOTICE 'Table public.business_relevant_industries DOES NOT exist';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'industries') THEN
        RAISE NOTICE 'Table public.industries exists';
    ELSE
        RAISE NOTICE 'Table public.industries DOES NOT exist';
    END IF;
END $$;

-- Next, check table structures to ensure proper foreign keys exist
DO $$
DECLARE
    col_exists boolean;
BEGIN
    RAISE NOTICE 'Checking table structures:';
    
    -- Check business_relevant_industries structure
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'business_relevant_industries' 
        AND column_name = 'business_id'
    ) INTO col_exists;
    
    IF col_exists THEN
        RAISE NOTICE 'Column business_id exists in business_relevant_industries';
    ELSE
        RAISE NOTICE 'Column business_id DOES NOT exist in business_relevant_industries';
    END IF;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'business_relevant_industries' 
        AND column_name = 'industry_id'
    ) INTO col_exists;
    
    IF col_exists THEN
        RAISE NOTICE 'Column industry_id exists in business_relevant_industries';
    ELSE
        RAISE NOTICE 'Column industry_id DOES NOT exist in business_relevant_industries';
    END IF;
    
    -- Check industries structure
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'industries' 
        AND column_name = 'parent_id'
    ) INTO col_exists;
    
    IF col_exists THEN
        RAISE NOTICE 'Column parent_id exists in industries';
    ELSE
        RAISE NOTICE 'Column parent_id DOES NOT exist in industries';
    END IF;
END $$;

-- Check foreign key constraints
DO $$
DECLARE
    fk_exists boolean;
BEGIN
    RAISE NOTICE 'Checking foreign key constraints:';
    
    -- Check if business_relevant_industries.business_id references businesses.id
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.key_column_usage kcu ON kcu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND kcu.table_schema = 'public'
        AND kcu.table_name = 'business_relevant_industries'
        AND kcu.column_name = 'business_id'
        AND ccu.table_schema = 'public'
        AND ccu.table_name = 'businesses'
        AND ccu.column_name = 'id'
    ) INTO fk_exists;
    
    IF fk_exists THEN
        RAISE NOTICE 'Foreign key constraint: business_relevant_industries.business_id -> businesses.id exists';
    ELSE
        RAISE NOTICE 'Foreign key constraint: business_relevant_industries.business_id -> businesses.id DOES NOT exist';
    END IF;
    
    -- Check if business_relevant_industries.industry_id references industries.id
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.key_column_usage kcu ON kcu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND kcu.table_schema = 'public'
        AND kcu.table_name = 'business_relevant_industries'
        AND kcu.column_name = 'industry_id'
        AND ccu.table_schema = 'public'
        AND ccu.table_name = 'industries'
        AND ccu.column_name = 'id'
    ) INTO fk_exists;
    
    IF fk_exists THEN
        RAISE NOTICE 'Foreign key constraint: business_relevant_industries.industry_id -> industries.id exists';
    ELSE
        RAISE NOTICE 'Foreign key constraint: business_relevant_industries.industry_id -> industries.id DOES NOT exist';
    END IF;
END $$;

-- Check RLS policies
DO $$
DECLARE
    r RECORD;
BEGIN
    RAISE NOTICE 'Checking RLS policies:';
    
    RAISE NOTICE 'Policies on businesses:';
    FOR r IN (
        SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
        FROM pg_policies
        WHERE schemaname = 'public' AND tablename = 'businesses'
        ORDER BY policyname
    ) LOOP
        RAISE NOTICE 'Policy %: permissive=%, roles=%, cmd=%, using=%, with_check=%',
            r.policyname, r.permissive, r.roles, r.cmd, r.qual, r.with_check;
    END LOOP;
    
    RAISE NOTICE 'Policies on business_relevant_industries:';
    FOR r IN (
        SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
        FROM pg_policies
        WHERE schemaname = 'public' AND tablename = 'business_relevant_industries'
        ORDER BY policyname
    ) LOOP
        RAISE NOTICE 'Policy %: permissive=%, roles=%, cmd=%, using=%, with_check=%',
            r.policyname, r.permissive, r.roles, r.cmd, r.qual, r.with_check;
    END LOOP;
    
    RAISE NOTICE 'Policies on industries:';
    FOR r IN (
        SELECT tablename, policyname, permissive, roles, cmd, qual, with_check
        FROM pg_policies
        WHERE schemaname = 'public' AND tablename = 'industries'
        ORDER BY policyname
    ) LOOP
        RAISE NOTICE 'Policy %: permissive=%, roles=%, cmd=%, using=%, with_check=%',
            r.policyname, r.permissive, r.roles, r.cmd, r.qual, r.with_check;
    END LOOP;
END $$;

-- Test the problematic query directly
DO $$
DECLARE
    test_owner_id uuid := '59a7ffe5-794e-45d4-90f7-988dc9588d43';  -- From the failing URL
    business_count integer;
    business_with_industries_count integer;
BEGIN
    RAISE NOTICE 'Testing query execution:';
    
    -- Count businesses for this owner
    SELECT COUNT(*) INTO business_count
    FROM public.businesses
    WHERE owner_id = test_owner_id;
    
    RAISE NOTICE 'Found % businesses for owner_id %', business_count, test_owner_id;
    
    -- Try the nested query
    SELECT COUNT(*) INTO business_with_industries_count
    FROM (
        SELECT b.id, 
               (SELECT json_agg(json_build_object(
                   'industry_id', bri.industry_id,
                   'industry', (SELECT json_build_object('id', i.id, 'name', i.name)
                                FROM public.industries i
                                WHERE i.id = bri.industry_id)
               ))
                FROM public.business_relevant_industries bri
                WHERE bri.business_id = b.id) AS business_relevant_industries
        FROM public.businesses b
        WHERE b.owner_id = test_owner_id
    ) subq;
    
    RAISE NOTICE 'Nested query returned % results', business_with_industries_count;
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error executing nested query: %', SQLERRM;
END $$;

-- Let's create a fix for proper RLS nested query policies
-- Uncomment and run if diagnostic shows issues with policies
/*
-- Add specific policies for nested queries if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'business_relevant_industries'
        AND policyname = 'Service role nested query policy'
    ) THEN
        CREATE POLICY "Service role nested query policy" ON public.business_relevant_industries
            FOR SELECT 
            TO service_role
            USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'industries'
        AND policyname = 'Anyone can view industries'
    ) THEN
        CREATE POLICY "Anyone can view industries" ON public.industries
            FOR SELECT 
            TO PUBLIC
            USING (true);
    END IF;
END $$;
*/
