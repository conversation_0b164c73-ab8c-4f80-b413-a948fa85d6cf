-- Check for triggers on social media tables that might be causing the "posts" error

-- Check all triggers on social media tables
SELECT 
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation,
    action_statement
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND event_object_table IN ('social_posts', 'post_likes', 'post_comments', 'comment_likes', 'post_categories')
ORDER BY event_object_table, trigger_name;

-- Check trigger functions that might reference "posts"
SELECT 
    routine_name,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_type = 'FUNCTION'
AND routine_name IN (
    SELECT DISTINCT 
        REPLACE(REPLACE(action_statement, 'EXECUTE FUNCTION ', ''), '()', '')
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public'
    AND event_object_table IN ('social_posts', 'post_likes', 'post_comments', 'comment_likes', 'post_categories')
)
ORDER BY routine_name;

-- Also check for any functions that might be called by RPC
SELECT 
    routine_name,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_type = 'FUNCTION'
AND (
    routine_name ILIKE '%post%'
    OR routine_name ILIKE '%like%'
    OR routine_name ILIKE '%comment%'
)
AND routine_definition ILIKE '%posts%'
ORDER BY routine_name;
