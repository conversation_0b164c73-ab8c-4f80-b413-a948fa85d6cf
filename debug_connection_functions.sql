-- Debug script to find the exact function causing mv_active_connections error
-- Run this in Supabase SQL Editor

-- 1. Show ALL functions that contain 'connection' in name or definition
SELECT 
    'All connection-related functions' as section,
    routine_name,
    routine_type,
    security_type,
    routine_definition
FROM information_schema.routines 
WHERE (routine_name LIKE '%connection%' OR routine_definition LIKE '%connection%')
AND routine_schema = 'public'
ORDER BY routine_name;

-- 2. Search for any functions that reference mv_active_connections
SELECT 
    'Functions referencing mv_active_connections' as section,
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_definition LIKE '%mv_active_connections%'
AND routine_schema = 'public';

-- 3. Check if mv_active_connections still exists anywhere
SELECT 
    'Materialized views check' as section,
    schemaname,
    matviewname,
    definition
FROM pg_matviews 
WHERE matviewname LIKE '%connection%';

-- 4. Check for any views that might reference mv_active_connections
SELECT 
    'Views check' as section,
    schemaname,
    viewname,
    definition
FROM pg_views 
WHERE definition LIKE '%mv_active_connections%'
AND schemaname = 'public';

-- 5. Show the exact definition of remove_connection function
SELECT 
    'remove_connection function definition' as section,
    routine_definition
FROM information_schema.routines 
WHERE routine_name = 'remove_connection'
AND routine_schema = 'public';

-- 6. Test calling the function directly to see the exact error
SELECT 
    'Direct function test' as section,
    public.remove_connection('00000000-0000-0000-0000-000000000000'::uuid) as result;

-- 7. Check for any triggers that might be calling old functions
SELECT 
    'Triggers check' as section,
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers
WHERE action_statement LIKE '%mv_active_connections%'
OR action_statement LIKE '%connection%';

-- 8. Look for any other objects that might reference the old materialized view
SELECT 
    'Dependencies check' as section,
    pg_describe_object(classid, objid, objsubid) as object_description
FROM pg_depend d
JOIN pg_class c ON d.refobjid = c.oid
WHERE c.relname = 'mv_active_connections';
