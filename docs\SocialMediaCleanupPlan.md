# Social Feed Implementation Plan

## Overview

This document outlines the simplified approach for the NetZero Platform's social feed implementation, focusing on core functionality and removing duplicate components.

## Core Components to Keep

1. **SocialFeed (Page)**: Main page component for the feed
2. **SocialFeedPost**: For displaying individual posts 
3. **PostComment**: For comment display and interaction
4. **PostCreator**: For creating new posts
5. **CategoryFilter**: For filtering posts by category

## Core Hooks to Keep

1. **useSocialFeed**: Main hook for managing feed data
2. **usePostComments**: For comment functionality
3. **usePostLikes**: For managing post likes
4. **useUserConnections**: For user connection management

## Implementation Steps

### 1. Clean up duplicate components
- Keep only the components in the `/social` directory
- Remove any duplicate files outside this directory

### 2. Update imports
- Ensure all imports point to the correct component locations
- Fix any broken imports in the SocialFeed page

### 3. Verify core functionality
- Creating posts
- Viewing posts in feed
- Commenting on posts
- Liking posts
- Replying to comments

## Database Tables Used

The implementation relies on the following Supabase tables:

- `social_posts`: Main post data
- `post_likes`: For tracking likes on posts
- `post_comments`: For post comments
- `comment_likes`: For likes on comments
- `post_categories`: For associating categories with posts
- `user_connections`: For managing user connections

## Component Structure

```
SocialFeed (Page)
├─ PostCreator
├─ CategoryFilter
├─ SocialFeedPost
│  ├─ PostComment
│  └─ PostCategoriesDisplay
└─ Sidebars (Connections, Trending)
```

## Hook Structure

```
useSocialFeed
├─ usePostLikes
├─ usePostComments
└─ useUserConnections
```
