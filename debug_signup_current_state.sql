-- Debug current signup state
-- Run this in Supabase SQL Editor to check current function and table structures

-- 1. Check current handle_new_user function
SELECT 
    p.proname as function_name,
    pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND p.proname = 'handle_new_user';

-- 2. Check if trigger exists
SELECT 
    t.tgname as trigger_name,
    t.tgenabled as enabled,
    c.relname as table_name,
    p.proname as function_name
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE c.relname = 'users' 
AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'auth');

-- 3. Check profiles table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 4. Check notification_preferences table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'notification_preferences'
ORDER BY ordinal_position;

-- 5. Check user_consent_settings table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'user_consent_settings'
ORDER BY ordinal_position;

-[
  {
    "column_name": "user_id",
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": null
  },
  {
    "column_name": "consent_updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": "now()"
  },
  {
    "column_name": "profile_visibility",
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true"
  },
  {
    "column_name": "newsletter_subscription",
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false"
  },
  {
    "column_name": "show_businesses",
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true"
  },
  {
    "column_name": "show_events",
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true"
  },
  {
    "column_name": "show_connections",
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "true"
  }
]

-- 7. Check recent users to see what's missing
SELECT
    'Recent users analysis' as analysis_type,
    COUNT(*) as total_users
FROM auth.users
WHERE created_at > NOW() - INTERVAL '7 days';

SELECT
    'Profiles created in last 7 days' as analysis_type,
    COUNT(*) as total_profiles
FROM public.profiles
WHERE created_at > NOW() - INTERVAL '7 days';

SELECT
    'Notification preferences created in last 7 days' as analysis_type,
    COUNT(*) as total_notification_prefs
FROM public.notification_preferences
WHERE created_at > NOW() - INTERVAL '7 days';

SELECT
    'User consent settings created in last 7 days' as analysis_type,
    COUNT(*) as total_consent_settings
FROM public.user_consent_settings
WHERE created_at > NOW() - INTERVAL '7 days';

-- 8. Check for orphaned records
SELECT
    'Users without profiles' as issue_type,
    COUNT(*) as count
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

SELECT
    'Profiles without notification preferences' as issue_type,
    COUNT(*) as count
FROM public.profiles p
LEFT JOIN public.notification_preferences np ON p.id = np.profile_id
WHERE np.profile_id IS NULL;

SELECT
    'Users without consent settings' as issue_type,
    COUNT(*) as count
FROM auth.users u
LEFT JOIN public.user_consent_settings ucs ON u.id = ucs.user_id
WHERE ucs.user_id IS NULL;
