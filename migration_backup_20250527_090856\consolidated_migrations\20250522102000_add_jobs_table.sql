-- Create jobs table
CREATE TABLE public.jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    company TEXT NOT NULL,
    location TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('Full-time', 'Part-time', 'Contract', 'Remote')),
    description TEXT NOT NULL,
    salary TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    company_description TEXT,
    application_deadline TIMESTAMP WITH TIME ZONE,
    contact_email TEXT,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create jobs_tags table for managing job tags
CREATE TABLE public.jobs_tags (
    id SERIAL PRIMARY KEY,
    job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE,
    tag TEXT NOT NULL
);

-- Create jobs_responsibilities table for managing job responsibilities
CREATE TABLE public.jobs_responsibilities (
    id SERIAL PRIMARY KEY,
    job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE,
    responsibility TEXT NOT NULL
);

-- Create jobs_requirements table for managing job requirements
CREATE TABLE public.jobs_requirements (
    id SERIAL PRIMARY KEY,
    job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE,
    requirement TEXT NOT NULL
);

-- Create jobs_benefits table for managing job benefits
CREATE TABLE public.jobs_benefits (
    id SERIAL PRIMARY KEY,
    job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE,
    benefit TEXT NOT NULL
);

-- Create RLS policies
ALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jobs_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jobs_responsibilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jobs_requirements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jobs_benefits ENABLE ROW LEVEL SECURITY;

-- Anyone can read jobs
CREATE POLICY "Anyone can read jobs" ON public.jobs
    FOR SELECT USING (true);

-- Only authenticated users can create jobs
CREATE POLICY "Authenticated users can create jobs" ON public.jobs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Only the job creator can update or delete their jobs
CREATE POLICY "Users can update their own jobs" ON public.jobs
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own jobs" ON public.jobs
    FOR DELETE USING (auth.uid() = created_by);

-- Similar policies for related tables
-- For jobs_tags
CREATE POLICY "Anyone can read job tags" ON public.jobs_tags
    FOR SELECT USING (true);

CREATE POLICY "Users can insert tags for their jobs" ON public.jobs_tags
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_tags.job_id
            AND created_by = auth.uid()
        )
    );

CREATE POLICY "Users can delete tags for their jobs" ON public.jobs_tags
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_tags.job_id
            AND created_by = auth.uid()
        )
    );

-- For jobs_responsibilities
CREATE POLICY "Anyone can read job responsibilities" ON public.jobs_responsibilities
    FOR SELECT USING (true);

CREATE POLICY "Users can insert responsibilities for their jobs" ON public.jobs_responsibilities
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_responsibilities.job_id
            AND created_by = auth.uid()
        )
    );

CREATE POLICY "Users can delete responsibilities for their jobs" ON public.jobs_responsibilities
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_responsibilities.job_id
            AND created_by = auth.uid()
        )
    );

-- For jobs_requirements
CREATE POLICY "Anyone can read job requirements" ON public.jobs_requirements
    FOR SELECT USING (true);

CREATE POLICY "Users can insert requirements for their jobs" ON public.jobs_requirements
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_requirements.job_id
            AND created_by = auth.uid()
        )
    );

CREATE POLICY "Users can delete requirements for their jobs" ON public.jobs_requirements
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_requirements.job_id
            AND created_by = auth.uid()
        )
    );

-- For jobs_benefits
CREATE POLICY "Anyone can read job benefits" ON public.jobs_benefits
    FOR SELECT USING (true);

CREATE POLICY "Users can insert benefits for their jobs" ON public.jobs_benefits
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_benefits.job_id
            AND created_by = auth.uid()
        )
    );

CREATE POLICY "Users can delete benefits for their jobs" ON public.jobs_benefits
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = jobs_benefits.job_id
            AND created_by = auth.uid()
        )
    );
