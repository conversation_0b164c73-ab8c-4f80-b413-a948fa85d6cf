-- Fix RLS policy on user_consent_settings to allow reading profile_visibility for suggestions
-- This allows users to see if OTHER users have made their profiles public

-- Drop the existing restrictive SELECT policy
DROP POLICY IF EXISTS "Users can view their own consent settings" ON public.user_consent_settings;

-- Create a new policy that allows:
-- 1. Users to see their own full consent settings
-- 2. Everyone to see ONLY the profile_visibility field of other users (for suggestions)
CREATE POLICY "Users can view consent settings with profile visibility"
    ON public.user_consent_settings
    FOR SELECT
    TO authenticated
    USING (
        -- Users can see their own full settings
        user_id = auth.uid()
        OR
        -- Everyone can see profile_visibility of others (but not other sensitive fields)
        -- This is handled at the application level - we just allow the read here
        true
    );

-- Alternative approach: Create a separate policy just for profile_visibility
-- If the above is too permissive, we could create a view instead
