-- Fix members visibility and force remove email_notifications column
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: FORCE REMOVE EMAIL_NOTIFICATIONS COLUMN
-- ================================================================

-- Drop all dependent views that might prevent column removal
DROP VIEW IF EXISTS public.user_privacy_settings_summary CASCADE;
DROP VIEW IF EXISTS public.notification_system_health CASCADE;
DROP VIEW IF EXISTS public.detailed_consent_breakdown CASCADE;

-- Force remove the email_notifications column with CASCADE
ALTER TABLE public.user_consent_settings 
DROP COLUMN IF EXISTS email_notifications CASCADE;

-- ================================================================
-- PART 2: FIX PROFILE VISIBILITY INCONSISTENCY
-- ================================================================

-- Check if profiles table has profile_visibility column
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'profile_visibility'
        AND table_schema = 'public'
    ) THEN
        -- Sync profile_visibility from profiles to user_consent_settings
        UPDATE public.user_consent_settings ucs
        SET profile_visibility = p.profile_visibility
        FROM public.profiles p
        WHERE ucs.user_id = p.id
        AND p.profile_visibility IS NOT NULL;
        
        RAISE NOTICE 'Synced profile_visibility from profiles table to user_consent_settings';
        
        -- Remove the column from profiles table to avoid confusion
        ALTER TABLE public.profiles DROP COLUMN profile_visibility CASCADE;
        RAISE NOTICE 'Removed profile_visibility column from profiles table';
    ELSE
        RAISE NOTICE 'profiles.profile_visibility column does not exist';
    END IF;
END $$;

-- Ensure all users have user_consent_settings records with profile_visibility = true by default
INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    created_at,
    updated_at
)
SELECT 
    p.id,
    true, -- Default to visible
    false,
    true,
    true,
    true,
    NOW(),
    NOW()
FROM public.profiles p
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_consent_settings ucs 
    WHERE ucs.user_id = p.id
);

-- ================================================================
-- PART 3: UPDATE VIEWS TO USE CORRECT VISIBILITY COLUMN
-- ================================================================

-- Update public_professional_profiles view to use user_consent_settings.profile_visibility
DROP VIEW IF EXISTS public.public_professional_profiles CASCADE;

CREATE OR REPLACE VIEW public.public_professional_profiles AS 
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    p.website,
    p.linkedin,
    p.twitter,
    p.location_id,
    p.main_industry_id,
    p.subscription_tier,
    p.is_sustainability_professional,
    p.created_at,
    p.updated_at,
    -- Use user_consent_settings.profile_visibility as the source of truth
    COALESCE(ucs.profile_visibility, true) as profile_visibility,
    l.name as location_name,
    i.name as industry_name,
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) AS category_names,
    (
        SELECT array_agg(pt.name)
        FROM profile_professional_types ppt
        JOIN sustainability_professional_types pt ON pt.id = ppt.professional_type_id
        WHERE ppt.profile_id = p.id
    ) AS professional_types,
    to_tsvector('english', 
        COALESCE(p.first_name, '') || ' ' || 
        COALESCE(p.last_name, '') || ' ' || 
        COALESCE(p.title, '') || ' ' || 
        COALESCE(p.organization, '') || ' ' || 
        COALESCE(p.bio, '')
    ) AS search_vector
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
LEFT JOIN public.locations l ON l.id = p.location_id
LEFT JOIN public.industries i ON i.id = p.main_industry_id
-- Only show profiles where user has consented to be visible
WHERE COALESCE(ucs.profile_visibility, true) = true;

-- Grant access to the view
GRANT SELECT ON public.public_professional_profiles TO authenticated;

-- ================================================================
-- PART 4: CREATE FUNCTION TO GET VISIBLE PROFILES
-- ================================================================

-- Create a function that frontend can use to get visible profiles
CREATE OR REPLACE FUNCTION public.get_visible_profiles(
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_search_query TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    title TEXT,
    organization TEXT,
    bio TEXT,
    website TEXT,
    linkedin TEXT,
    twitter TEXT,
    location_name TEXT,
    industry_name TEXT,
    category_names TEXT[],
    professional_types TEXT[],
    subscription_tier TEXT,
    is_sustainability_professional BOOLEAN,
    profile_visibility BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ppp.id,
        ppp.first_name,
        ppp.last_name,
        ppp.avatar_url,
        ppp.title,
        ppp.organization,
        ppp.bio,
        ppp.website,
        ppp.linkedin,
        ppp.twitter,
        ppp.location_name,
        ppp.industry_name,
        ppp.category_names,
        ppp.professional_types,
        ppp.subscription_tier,
        ppp.is_sustainability_professional,
        ppp.profile_visibility
    FROM public.public_professional_profiles ppp
    WHERE 
        (p_search_query IS NULL OR 
         ppp.search_vector @@ plainto_tsquery('english', p_search_query) OR
         ppp.first_name ILIKE '%' || p_search_query || '%' OR
         ppp.last_name ILIKE '%' || p_search_query || '%' OR
         ppp.organization ILIKE '%' || p_search_query || '%')
    ORDER BY 
        CASE 
            WHEN p_search_query IS NOT NULL THEN 
                ts_rank(ppp.search_vector, plainto_tsquery('english', p_search_query))
            ELSE 0
        END DESC,
        ppp.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- ================================================================
-- PART 5: RECREATE ESSENTIAL VIEWS
-- ================================================================

-- Recreate user_privacy_settings_summary view without email_notifications
CREATE OR REPLACE VIEW public.user_privacy_settings_summary AS
SELECT 
    p.id as profile_id,
    p.first_name,
    p.last_name,
    p.email,
    -- Consent settings (without email_notifications)
    ucs.profile_visibility,
    ucs.newsletter_subscription,
    ucs.show_businesses,
    ucs.show_events,
    ucs.show_connections,
    ucs.consent_updated_at,
    ucs.created_at as consent_created_at,
    ucs.updated_at as consent_last_updated,
    -- Notification preferences (including email_notifications)
    np.email_notifications,
    np.post_likes,
    np.post_comments,
    np.comment_replies,
    np.comment_likes,
    np.connection_requests,
    np.connection_accepted,
    np.event_signups,
    np.event_updates,
    np.system_notifications,
    -- GDPR consent summary from event signups
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id) as total_event_signups,
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id AND es.gdpr_consent = true) as gdpr_consents_given
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
LEFT JOIN public.notification_preferences np ON p.id = np.profile_id;

-- Grant access to the view
GRANT SELECT ON public.user_privacy_settings_summary TO authenticated;

-- Recreate notification system health view
CREATE OR REPLACE VIEW public.notification_system_health AS
SELECT 
    'Notification Preferences' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN email_notifications = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN post_comments = false THEN 1 END) as post_comments_disabled_count,
    COUNT(CASE WHEN event_signups = false THEN 1 END) as event_signups_disabled_count
FROM public.notification_preferences
UNION ALL
SELECT 
    'Profile Visibility' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN profile_visibility = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN profile_visibility = false THEN 1 END) as post_comments_disabled_count,
    0 as event_signups_disabled_count
FROM public.user_consent_settings;

-- Grant access to the view
GRANT SELECT ON public.notification_system_health TO authenticated;

COMMIT;

-- ================================================================
-- VERIFICATION AND REPORTING
-- ================================================================

-- Report the fixes
SELECT 'Profile visibility fix summary:' as info;
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN profile_visibility = true THEN 1 END) as visible_profiles,
    COUNT(CASE WHEN profile_visibility = false THEN 1 END) as hidden_profiles,
    COUNT(CASE WHEN profile_visibility IS NULL THEN 1 END) as null_visibility
FROM public.user_consent_settings;

-- Test the public_professional_profiles view
SELECT 'Visible members count:' as info;
SELECT COUNT(*) as visible_members_count
FROM public.public_professional_profiles;

-- Check if email_notifications column was removed
SELECT 'email_notifications column status:' as info;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'user_consent_settings' 
            AND column_name = 'email_notifications'
            AND table_schema = 'public'
        ) THEN 'STILL_EXISTS'
        ELSE 'SUCCESSFULLY_REMOVED'
    END as column_status;
