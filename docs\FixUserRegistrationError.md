# Fix for User Registration "Database Error" Issue

## The Problem

Users are experiencing an error when trying to register for the NetZeroHub platform:

```
Database error saving new user
```

This error occurs because the trigger function that creates a profile when a new user signs up is using an invalid value (`seed`) for the `subscription_tier` column. The database constraint on this column only accepts: `none`, `seedling`, `sapling`, and `woodland`.

## Root Cause Analysis

1. The trigger function `handle_new_user()` is setting `subscription_tier` to `'seed'`
2. The database has a constraint `profiles_subscription_tier_check` that requires this value to be one of: `none`, `seedling`, `sapling`, `woodland`
3. This mismatch causes the profile creation to fail, which causes the overall user registration to fail

This discrepancy appears to have been introduced in February 2024 when a migration changed the expected values from `seed` to `none` but the trigger function was not updated accordingly.

## Solution

The fix involves updating the trigger function to use `'none'` instead of `'seed'` for the default subscription tier.

### Option 1: Run the Fix Script (Quickest)

1. Open the Supabase SQL Editor
2. Execute the `FixUserRegistrationConstraint.sql` script

This script will:
- Drop the existing trigger
- Update the function to use `'none'` for the subscription tier
- Re-create the trigger
- Verify and fix the constraint if needed

### Option 2: Apply the Complete Schema

If you're doing a complete database reset:

1. Open the Supabase SQL Editor
2. Execute the `NetZeroHub_Complete_Schema.sql` script

The schema has been updated to use `'none'` for the subscription tier in both the table definition and the trigger function.

## Verification

After applying either fix, try registering a new test user. The registration should complete successfully, and a profile record should be created with `subscription_tier` set to `'none'`.

## Long-term Recommendations

1. Make sure that when database constraints are changed, related triggers and functions are updated accordingly
2. Consider adding a validation step in your CI/CD pipeline to check for this type of mismatch
3. Document the allowed values for constrained fields in a central location to prevent future issues
