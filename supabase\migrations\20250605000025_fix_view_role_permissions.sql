-- Fix events_with_creator view permissions for authenticated users
-- The view works for admin but fails for authenticated users
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- GRANT PROPER PERMISSIONS TO THE VIEW
-- ================================================================

-- Ensure the view has SELECT permissions for authenticated and anon roles
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;

-- ================================================================
-- ENSURE UNDERLYING TABLES HAVE PROPER PERMISSIONS
-- ================================================================

-- Grant permissions on events table
GRANT SELECT ON public.events TO authenticated;
GRANT SELECT ON public.events TO anon;

-- Grant permissions on profiles table  
GRANT SELECT ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;

-- ================================================================
-- CHECK IF VIEW NEEDS TO BE RECREATED WITH SECURITY DEFINER
-- ================================================================

-- Drop and recreate the view with proper security settings
DROP VIEW IF EXISTS public.events_with_creator CASCADE;

-- Recreate with security_invoker = off to run with definer's permissions
CREATE OR REPLACE VIEW public.events_with_creator
WITH (security_invoker = off) AS
SELECT 
    e.id,
    e.creator_user_id,
    e.title,
    e.start_date,
    e.start_time,
    e.end_date,
    e.end_time,
    e.description,
    e.image_url,
    e.event_type,
    e.physical_location,
    e.meeting_url,
    e.event_category,
    e.tags,
    e.created_at,
    e.updated_at,
    e.industry_ids,
    e.netzero_category_ids,
    e.owner_id,
    e.status,
    e.capacity,
    e.is_private,
    e.category,
    e.industry,
    -- Creator information from profiles table
    concat(p.first_name, ' ', p.last_name) AS creator_name,
    p.avatar_url AS creator_avatar_url,
    p.email AS creator_email
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id;

-- Grant permissions to the recreated view
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;

-- Add comment
COMMENT ON VIEW public.events_with_creator IS
'Events view with creator information. Uses security_invoker = off for proper permission handling.';

-- ================================================================
-- TEST THE FIX
-- ================================================================

-- Test that the view works
SELECT 
    'FINAL_TEST' as test_type,
    COUNT(*) as event_count
FROM public.events_with_creator;

COMMIT;
