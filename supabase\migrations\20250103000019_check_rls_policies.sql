-- Check RLS policies and other potential sources of "posts" reference

-- Check RLS policies for any "posts" references
SELECT 
    'RLS_POLICY' as object_type,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
AND (
    qual ILIKE '%posts%' 
    OR with_check ILIKE '%posts%'
    OR policyname ILIKE '%posts%'
)
ORDER BY tablename, policyname;

-- Check if RLS is enabled on our social media tables
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN ('social_posts', 'post_likes', 'post_comments', 'comment_likes', 'post_categories')
ORDER BY tablename;

-- Check for any constraints that might reference "posts"
SELECT 
    'CONSTRAINT' as object_type,
    table_name,
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_schema = 'public'
AND constraint_name ILIKE '%posts%'
ORDER BY table_name, constraint_name;

-- Check for any indexes that might reference "posts"
SELECT 
    'INDEX' as object_type,
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
AND indexname ILIKE '%posts%'
ORDER BY tablename, indexname;
