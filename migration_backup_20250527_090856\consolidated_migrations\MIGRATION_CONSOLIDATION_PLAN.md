# Migration Consolidation Plan

This document outlines the plan for consolidating the migrations from multiple locations into a single, organized directory structure.

## Current Issues

1. **Multiple Migration Locations**:
   - `migrations/` (147 SQL files)
   - `migrations/misc_migrations/` (8 versioned SQL files)
   - `supabase/migrations/` (4 base SQL files)
   - `supabase/migrations/archive/` (48 SQL files)

2. **Duplicate Files**: 48 files appear in both the main migrations folder and the archive folder.

3. **Version Inconsistencies**: Multiple versions of the same migration in the misc_migrations folder.

4. **Timestamp Conflicts**: Some files have the same timestamp but different names.

## Consolidation Steps

1. **Preserve Base Structure**:
   - Keep the 4 foundational migration files from `supabase/migrations/`
   - These will form the base of our migration sequence

2. **Integrate Unique Migrations**:
   - Add the 99 unique files from the main migrations directory
   - Ensure proper timestamp sequencing

3. **Consolidate Versioned Files**:
   - Use only the final version of files in the misc_migrations folder
   - Rename with proper timestamps

4. **Resolve Conflicts**:
   - For duplicate migrations between folders, keep only one version
   - For timestamp conflicts, adjust timestamps to maintain order

5. **Final Structure**:
   - All migrations in a single `supabase/migrations/` folder
   - Clear, linear migration history
   - No redundant or conflicting files

## Implementation Timeline

1. **Phase 1**: Create temporary directory with organized migrations
2. **Phase 2**: Validate migration content and sequence
3. **Phase 3**: Replace existing supabase/migrations with consolidated version
4. **Phase 4**: Update documentation and clean up temporary directories
