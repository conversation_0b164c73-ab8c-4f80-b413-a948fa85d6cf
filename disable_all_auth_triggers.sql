-- Disable all custom triggers on auth.users to isolate the issue

-- Disable all non-system triggers on auth.users
ALTER TABLE auth.users DISABLE TRIGGER on_auth_user_created;
ALTER TABLE auth.users DISABLE TRIGGER create_welcome_notification_trigger;
ALTER TABLE auth.users DISABLE TRIGGER on_auth_email_updated;

-- If there's a sync email trigger, disable it too
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_trigger t
    JOIN pg_class c ON t.tgrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE n.nspname = 'auth' 
    AND c.relname = 'users'
    AND t.tgname = 'z_sync_email_after_signup'
  ) THEN
    ALTER TABLE auth.users DISABLE TRIGGER z_sync_email_after_signup;
  END IF;
END $$;

-- Check which triggers are now disabled
SELECT 
    t.tgname as trigger_name,
    CASE 
      WHEN t.tgenabled = 'O' THEN 'ENABLED'
      WHEN t.tgenabled = 'D' THEN 'DISABLED'
      ELSE 'OTHER'
    END as status,
    c.relname as table_name,
    p.proname as function_name
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'auth' 
AND c.relname = 'users'
AND t.tgname NOT LIKE 'RI_ConstraintTrigger%'
ORDER BY t.tgname;

SELECT 'All custom auth triggers disabled - test signup now' as status;
