-- REMOVE MESSAGING SYSTEM FROM NETZERO PLATFORM
-- This migration removes the direct messaging capability while preserving the notifications system
-- Created: May 27, 2025

---------------------------------------------------------
-- PART 1: DROP MESSAGING VIEWS
---------------------------------------------------------

-- Remove the conversations view
DROP VIEW IF EXISTS conversations_with_details;

---------------------------------------------------------
-- PART 2: REVOKE PERMISSIONS
---------------------------------------------------------

-- Revoke permissions for messaging tables and functions
R<PERSON><PERSON><PERSON> SELECT ON conversations FROM authenticated;
R<PERSON>VO<PERSON> SELECT ON conversation_participants FROM authenticated;
<PERSON><PERSON><PERSON><PERSON> SELECT, INSERT ON messages FROM authenticated;
REVOKE SELECT ON message_limits FROM authenticated;

REVOKE EXECUTE ON FUNCTION start_conversation FROM authenticated;
REVOKE EXECUTE ON FUNCTION send_message FROM authenticated;
REVOKE EXECUTE ON FUNCTION mark_message_as_read FROM authenticated;
REVOKE EXECUTE ON FUNCTION mark_conversation_messages_as_read FROM authenticated;
REVOKE EXECUTE ON FUNCTION get_unread_message_count FROM authenticated;

---------------------------------------------------------
-- PART 3: DROP MESSAGING FUNCTIONS
---------------------------------------------------------

-- Drop messaging functions
DROP FUNCTION IF EXISTS start_conversation(UUID[]);
DROP FUNCTION IF EXISTS send_message(UUID, TEXT);
DROP FUNCTION IF EXISTS mark_message_as_read(UUID);
DROP FUNCTION IF EXISTS mark_conversation_messages_as_read(UUID);
DROP FUNCTION IF EXISTS get_unread_message_count();

---------------------------------------------------------
-- PART 4: DROP MESSAGING RLS POLICIES
---------------------------------------------------------

-- Drop RLS policies for messages
DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.messages;
DROP POLICY IF EXISTS "Users can send messages in their conversations" ON public.messages;

-- Drop RLS policies for conversation_participants
DROP POLICY IF EXISTS "Users can view conversation participants for their conversations" ON public.conversation_participants;

-- Drop RLS policies for conversations
DROP POLICY IF EXISTS "Users can view conversations they're part of" ON public.conversations;

-- Drop RLS policies for message_limits
DROP POLICY IF EXISTS "Users can view their own message limits" ON public.message_limits;

---------------------------------------------------------
-- PART 5: DROP MESSAGING TABLES
---------------------------------------------------------

-- Drop tables in correct order to respect foreign key constraints
DROP TABLE IF EXISTS public.messages;
DROP TABLE IF EXISTS public.conversation_participants;
DROP TABLE IF EXISTS public.conversations;
DROP TABLE IF EXISTS public.message_limits;

---------------------------------------------------------
-- PART 6: UPDATE NOTIFICATION FUNCTIONS
---------------------------------------------------------

-- Update the create_notification function to filter out message-related notifications
CREATE OR REPLACE FUNCTION create_notification(
    p_user_id UUID,
    p_type VARCHAR(50),
    p_message TEXT,
    p_related_id UUID,
    p_related_type VARCHAR(50),
    p_actor_id UUID DEFAULT NULL
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
    v_notification_id UUID;
BEGIN
    -- Skip creating notification if the related_type is 'conversation'
    IF p_related_type = 'conversation' THEN
        RETURN NULL;
    END IF;

    -- Insert notification with the current database structure
    INSERT INTO notifications (
        id, 
        user_id,
        type,
        content,
        related_id,
        related_type,
        is_read,
        created_at,
        updated_at
    )
    VALUES (
        gen_random_uuid(),
        p_user_id,
        p_type,
        p_message,
        p_related_id,
        p_related_type,
        FALSE,
        now(),
        now()
    )
    RETURNING id INTO v_notification_id;
    
    RETURN v_notification_id;
END;
$$ LANGUAGE plpgsql;

-- Update the user_notifications_with_details view to exclude conversation-related notifications
CREATE OR REPLACE VIEW user_notifications_with_details AS
SELECT 
    n.id,
    n.user_id,
    n.type,
    n.related_id AS related_entity_id,
    n.related_type AS related_entity_type,
    n.content AS message,
    NULL::JSONB AS data,
    n.is_read,
    n.created_at,
    n.updated_at,
    NULL AS actor_first_name,
    NULL AS actor_last_name,
    NULL AS actor_avatar_url
FROM notifications n
WHERE n.related_type != 'conversation' OR n.related_type IS NULL;

-- Grant permissions to the updated view
GRANT SELECT ON user_notifications_with_details TO authenticated;

---------------------------------------------------------
-- PART 7: CLEAN UP ANY EXISTING CONVERSATION NOTIFICATIONS
---------------------------------------------------------

-- Delete any existing conversation-related notifications
DELETE FROM notifications 
WHERE related_type = 'conversation' 
   OR type = 'new_message';

-- COMMIT;
