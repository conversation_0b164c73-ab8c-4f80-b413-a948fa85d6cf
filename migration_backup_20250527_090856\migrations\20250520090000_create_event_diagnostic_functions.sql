-- <PERSON>reate functions to help with event system diagnostics

-- Function to get all tables in a schema
CREATE OR REPLACE FUNCTION public.get_tables_in_schema(schema_name text)
RETURNS TABLE (tablename text)
LANGUAGE sql
SECURITY DEFINER
SET search_path = pg_catalog, pg_temp
AS $$
  SELECT tablename::text
  FROM pg_catalog.pg_tables
  WHERE schemaname = schema_name;
$$;

-- Function to get all views in a schema
CREATE OR REPLACE FUNCTION public.get_views_in_schema(schema_name text)
RETURNS TABLE (viewname text)
LANGUAGE sql
SECURITY DEFINER
SET search_path = pg_catalog, pg_temp
AS $$
  SELECT viewname::text
  FROM pg_catalog.pg_views
  WHERE schemaname = schema_name;
$$;

-- Function to get columns of a table
CREATE OR REPLACE FUNCTION public.get_table_columns(table_name text)
RETURNS TABLE (
  column_name text,
  data_type text,
  is_nullable boolean
)
LANGUAGE sql
SECURITY DEFINER
SET search_path = pg_catalog, pg_temp
AS $$
  SELECT 
    column_name::text,
    data_type::text,
    (is_nullable = 'YES') as is_nullable
  FROM information_schema.columns
  WHERE table_schema = 'public' AND table_name = get_table_columns.table_name;
$$;

-- Function to recreate the events_with_creator view if it's missing
CREATE OR REPLACE FUNCTION public.create_events_with_creator_view_if_missing()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = pg_catalog, pg_temp
AS $$
DECLARE
  view_exists boolean;
BEGIN
  -- Check if the view already exists
  SELECT EXISTS (
    SELECT 1
    FROM pg_catalog.pg_views
    WHERE schemaname = 'public' AND viewname = 'events_with_creator'
  ) INTO view_exists;
  
  -- If view doesn't exist, create it
  IF NOT view_exists THEN
    -- Drop view if it exists (this shouldn't happen based on our check, but just in case)
    DROP VIEW IF EXISTS public.events_with_creator;
    
    -- Create the view
    CREATE VIEW public.events_with_creator AS
    SELECT 
      e.*,
      au.email as creator_email,
      CONCAT(p.first_name, ' ', p.last_name) as creator_name,
      p.avatar_url as creator_avatar_url
    FROM 
      public.events e
    JOIN 
      auth.users au ON e.creator_user_id = au.id
    LEFT JOIN 
      public.profiles p ON e.creator_user_id = p.id;

    -- Grant appropriate permissions on the view
    ALTER VIEW public.events_with_creator OWNER TO postgres;
    GRANT SELECT ON public.events_with_creator TO authenticated;
    GRANT SELECT ON public.events_with_creator TO service_role;
    
    RETURN TRUE;
  ELSE
    -- View already exists
    RETURN FALSE;
  END IF;
END;
$$;

-- Grant necessary permissions on these functions
GRANT EXECUTE ON FUNCTION public.get_tables_in_schema TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_views_in_schema TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_table_columns TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_events_with_creator_view_if_missing TO authenticated;
