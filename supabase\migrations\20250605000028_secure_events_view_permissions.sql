-- Replace the admin-privileged view with a secure version
-- This removes the security risk of running with admin permissions
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- REPLACE THE INSECURE VIEW WITH A SECURE VERSION
-- ================================================================

-- Drop the view that runs with admin permissions
DROP VIEW IF EXISTS public.events_with_creator CASCADE;

-- Recreate the view with proper security (security_invoker = on is the default and secure)
-- This view will run with the calling user's permissions, not admin permissions
CREATE OR REPLACE VIEW public.events_with_creator AS
SELECT 
    e.id,
    e.creator_user_id,
    e.title,
    e.start_date,
    e.start_time,
    e.end_date,
    e.end_time,
    e.description,
    e.image_url,
    e.event_type,
    e.physical_location,
    e.meeting_url,
    e.event_category,
    e.tags,
    e.created_at,
    e.updated_at,
    e.industry_ids,
    e.netzero_category_ids,
    e.owner_id,
    e.status,
    e.capacity,
    e.is_private,
    e.category,
    e.industry,
    -- Creator information from profiles table
    concat(p.first_name, ' ', p.last_name) AS creator_name,
    p.avatar_url AS creator_avatar_url,
    p.email AS creator_email
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id;

-- ================================================================
-- ENSURE PROPER PERMISSIONS ON UNDERLYING TABLES
-- ================================================================

-- Make sure the underlying tables have the necessary permissions for all users
-- Events table permissions
GRANT SELECT ON public.events TO authenticated;
GRANT SELECT ON public.events TO anon;

-- Profiles table permissions (for creator information)
GRANT SELECT ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;

-- ================================================================
-- GRANT PERMISSIONS TO THE SECURE VIEW
-- ================================================================

-- Grant permissions to the view (this is safe since the view itself doesn't elevate privileges)
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;

-- ================================================================
-- ENSURE RLS POLICIES ALLOW PROPER ACCESS
-- ================================================================

-- Check if events table has RLS enabled and create appropriate policies if needed
DO $$
DECLARE
    events_rls_enabled BOOLEAN;
    profiles_rls_enabled BOOLEAN;
BEGIN
    -- Check RLS status for events table
    SELECT rowsecurity INTO events_rls_enabled
    FROM pg_tables 
    WHERE schemaname = 'public' AND tablename = 'events';
    
    -- Check RLS status for profiles table
    SELECT rowsecurity INTO profiles_rls_enabled
    FROM pg_tables 
    WHERE schemaname = 'public' AND tablename = 'profiles';
    
    RAISE NOTICE 'RLS Status - Events: %, Profiles: %', events_rls_enabled, profiles_rls_enabled;
    
    -- If events has RLS enabled, ensure there's a policy that allows reading events
    IF events_rls_enabled THEN
        -- Create a policy to allow reading all events (if it doesn't exist)
        IF NOT EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE schemaname = 'public' 
            AND tablename = 'events' 
            AND policyname = 'Allow reading all events'
        ) THEN
            CREATE POLICY "Allow reading all events" ON public.events
                FOR SELECT
                USING (true);
            RAISE NOTICE 'Created policy: Allow reading all events';
        END IF;
    END IF;
    
    -- If profiles has RLS enabled, ensure there's a policy that allows reading profiles
    IF profiles_rls_enabled THEN
        -- Create a policy to allow reading all profiles (if it doesn't exist)
        IF NOT EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE schemaname = 'public' 
            AND tablename = 'profiles' 
            AND policyname = 'Allow reading all profiles'
        ) THEN
            CREATE POLICY "Allow reading all profiles" ON public.profiles
                FOR SELECT
                USING (true);
            RAISE NOTICE 'Created policy: Allow reading all profiles';
        END IF;
    END IF;
END $$;

-- Add comment to the secure view
COMMENT ON VIEW public.events_with_creator IS 
'Events view with creator information. Runs with caller permissions (secure) and relies on proper table permissions and RLS policies.';

-- ================================================================
-- TEST THE SECURE VIEW
-- ================================================================

-- Test that the secure view works
SELECT 
    'SECURE_VIEW_TEST' as test_type,
    COUNT(*) as event_count,
    COUNT(CASE WHEN creator_name IS NOT NULL THEN 1 END) as events_with_creator_names
FROM public.events_with_creator;

-- Show a sample to verify it's working
SELECT 
    'SAMPLE_EVENTS' as test_type,
    id,
    title,
    creator_name,
    creator_email
FROM public.events_with_creator
LIMIT 3;

COMMIT;
