-- Check if share_email_with_attendees column is actually used in current database
-- This will help determine if we can safely remove it

-- 1. Check if there are any current functions that use share_email_with_attendees
SELECT 
    'Current functions using share_email_with_attendees:' as info,
    p.proname as function_name,
    pg_get_function_arguments(p.oid) as arguments,
    CASE 
        WHEN pg_get_functiondef(p.oid) ILIKE '%share_email_with_attendees%' THEN 'USES_COLUMN'
        ELSE 'NO_USAGE'
    END as usage_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND p.proname NOT LIKE '%agg%'
AND pg_get_functiondef(p.oid) ILIKE '%share_email_with_attendees%';

-- 2. Check if there are any current views that use share_email_with_attendees
SELECT 
    'Current views using share_email_with_attendees:' as info,
    viewname,
    definition
FROM pg_views
WHERE schemaname = 'public'
AND definition ILIKE '%share_email_with_attendees%';

-- 3. Check if the get_event_attendee_emails function exists and what it does
SELECT 
    'get_event_attendee_emails function:' as info,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'public'
            AND p.proname = 'get_event_attendee_emails'
        ) THEN 'EXISTS'
        ELSE 'DOES_NOT_EXIST'
    END as function_exists;

-- 4. If the function exists, show its definition
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname = 'get_event_attendee_emails'
    ) THEN
        RAISE NOTICE 'get_event_attendee_emails function definition:';
        RAISE NOTICE '%', pg_get_functiondef('public.get_event_attendee_emails'::regproc);
    ELSE
        RAISE NOTICE 'get_event_attendee_emails function does not exist';
    END IF;
END $$;

-- 5. Check if there's a get_event_attendee_emails_for_attendees function
SELECT 
    'get_event_attendee_emails_for_attendees function:' as info,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'public'
            AND p.proname = 'get_event_attendee_emails_for_attendees'
        ) THEN 'EXISTS'
        ELSE 'DOES_NOT_EXIST'
    END as function_exists;

-- 6. Check if update_user_consent_settings function exists and uses the column
SELECT 
    'update_user_consent_settings function:' as info,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'public'
            AND p.proname = 'update_user_consent_settings'
        ) THEN 'EXISTS'
        ELSE 'DOES_NOT_EXIST'
    END as function_exists;

-- 7. Show the update_user_consent_settings function definition if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname = 'update_user_consent_settings'
    ) THEN
        RAISE NOTICE 'update_user_consent_settings function definition:';
        RAISE NOTICE '%', pg_get_functiondef('public.update_user_consent_settings'::regproc);
    ELSE
        RAISE NOTICE 'update_user_consent_settings function does not exist';
    END IF;
END $$;

-- 8. Check if there are any triggers that might use this column
SELECT 
    'Triggers that might use share_email_with_attendees:' as info,
    t.tgname as trigger_name,
    c.relname as table_name,
    pg_get_triggerdef(t.oid) as trigger_definition
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
AND pg_get_triggerdef(t.oid) ILIKE '%share_email_with_attendees%';

-- 9. Check if there are any RLS policies that use this column
SELECT 
    'RLS policies using share_email_with_attendees:' as info,
    schemaname,
    tablename,
    policyname,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public'
AND (qual ILIKE '%share_email_with_attendees%' OR with_check ILIKE '%share_email_with_attendees%');

-- 10. Summary: Is the column actually being used?
SELECT 
    'SUMMARY - Column usage analysis:' as info,
    (
        SELECT COUNT(*) FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname NOT LIKE '%agg%'
        AND pg_get_functiondef(p.oid) ILIKE '%share_email_with_attendees%'
    ) as functions_using_column,
    (
        SELECT COUNT(*) FROM pg_views
        WHERE schemaname = 'public'
        AND definition ILIKE '%share_email_with_attendees%'
    ) as views_using_column,
    (
        SELECT COUNT(*) FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE n.nspname = 'public'
        AND pg_get_triggerdef(t.oid) ILIKE '%share_email_with_attendees%'
    ) as triggers_using_column,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM pg_proc p
            JOIN pg_namespace n ON p.pronamespace = n.oid
            WHERE n.nspname = 'public'
            AND p.proname NOT LIKE '%agg%'
            AND pg_get_functiondef(p.oid) ILIKE '%share_email_with_attendees%'
        ) > 0 THEN 'COLUMN_IS_USED'
        ELSE 'COLUMN_NOT_USED_SAFE_TO_REMOVE'
    END as recommendation;
