-- Test script to verify connection functions are working properly
-- Run this in Supabase SQL Editor to test the functions

-- 1. Check if user_connections table exists and has data
SELECT 'user_connections table check' as test_name, 
       COUNT(*) as record_count,
       COUNT(DISTINCT status) as status_types
FROM public.user_connections;

-- 2. Check current user context
SELECT 'auth context check' as test_name,
       auth.uid() as current_user_id,
       auth.role() as current_role;

-- 3. Test the functions exist and are callable
SELECT 'function existence check' as test_name,
       routine_name,
       routine_type,
       security_type
FROM information_schema.routines 
WHERE routine_name IN (
    'request_connection',
    'respond_to_connection_request',
    'cancel_connection_request',
    'remove_connection'
)
AND routine_schema = 'public'
ORDER BY routine_name;

-- 4. Test calling a function (this should return an auth error if not logged in, which is expected)
SELECT 'function call test' as test_name,
       public.remove_connection('00000000-0000-0000-0000-000000000000'::uuid) as result;

-- 5. Check RLS policies
SELECT 'RLS policies check' as test_name,
       policyname,
       cmd as policy_type,
       CASE WHEN qual IS NOT NULL THEN 'Has USING clause' ELSE 'No USING clause' END as using_clause
FROM pg_policies 
WHERE tablename = 'user_connections'
ORDER BY cmd, policyname;

-- 6. Check table structure
SELECT 'table structure check' as test_name,
       column_name,
       data_type,
       is_nullable,
       column_default
FROM information_schema.columns
WHERE table_name = 'user_connections'
AND table_schema = 'public'
ORDER BY ordinal_position;
