-- Drop all existing event_signups policies to avoid conflicts
DO $$
BEGIN
    DROP POLICY IF EXISTS "Everyone can view event signups" ON public.event_signups;
    DROP POLICY IF EXISTS "Users can sign up for events" ON public.event_signups;
    DROP POLICY IF EXISTS "Users can update their own signups" ON public.event_signups;
    DROP POLICY IF EXISTS "Users can delete their own signups" ON public.event_signups;
    DROP POLICY IF EXISTS "Enable read access for all users" ON public.event_signups;
    DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.event_signups;
    DROP POLICY IF EXISTS "Enable update for users or event owners" ON public.event_signups;
    DROP POLICY IF EXISTS "Enable delete for users or event owners" ON public.event_signups;

    -- Make sure RLS is enabled
    ALTER TABLE public.event_signups ENABLE ROW LEVEL SECURITY;

    -- Create simplified, clear policies
    
    -- 1. Allow anyone to view non-hidden signups
    CREATE POLICY "View event signups" ON public.event_signups
        FOR SELECT
        USING (
            -- Can see if: attendance not hidden OR is the user's own signup OR is the event owner
            (NOT hide_attendance) OR 
            auth.uid() = user_id OR 
            EXISTS (
                SELECT 1 FROM events 
                WHERE events.id = event_id 
                AND events.creator_user_id = auth.uid()
            )
        );

    -- 2. Allow authenticated users to sign up for events (but not their own events)
    CREATE POLICY "Create event signup" ON public.event_signups
        FOR INSERT
        WITH CHECK (
            auth.role() = 'authenticated' AND  -- Must be logged in
            auth.uid() = user_id AND          -- Can only sign up themselves
            NOT EXISTS (                       -- Cannot sign up for own events
                SELECT 1 FROM events
                WHERE events.id = event_id
                AND events.creator_user_id = auth.uid()
            )
        );

    -- 3. Allow users to update their own signups
    CREATE POLICY "Update own signup" ON public.event_signups
        FOR UPDATE
        USING (auth.uid() = user_id)
        WITH CHECK (auth.uid() = user_id);

    -- 4. Allow users to delete their own signups
    CREATE POLICY "Delete own signup" ON public.event_signups
        FOR DELETE
        USING (auth.uid() = user_id);

    -- Grant permissions
    GRANT ALL ON public.event_signups TO authenticated;
    GRANT SELECT ON public.event_signups TO anon;
END $$;