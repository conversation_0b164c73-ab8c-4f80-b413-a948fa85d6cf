-- Find all references to user_id column that might be causing the issue

-- 1. Check if there are any functions that reference user_id in a table that doesn't have it
SELECT 
    p.proname as function_name,
    pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND pg_get_functiondef(p.oid) ILIKE '%user_id%'
AND p.proname IN ('handle_new_user', 'create_welcome_notification', 'sync_auth_email_to_profile');

-- 2. Check all triggers that might be referencing user_id
SELECT 
    t.tgname as trigger_name,
    c.relname as table_name,
    p.proname as function_name,
    pg_get_triggerdef(t.oid) as trigger_definition
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'auth' 
AND c.relname = 'users';

-- 3. Check what columns actually exist in profiles table
SELECT 
    'profiles' as table_name,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'profiles'
AND column_name ILIKE '%user%'
ORDER BY ordinal_position;
