-- Test the proper single-step fix
-- Date: 2025-06-05

-- Simulate exactly what happens during signup
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_metadata JSONB := '{"first_name": "Test", "last_name": "User"}';
    
    -- Simulate the NEW record that the trigger receives
    simulated_new RECORD;
    
    profile_count INTEGER;
    consent_count INTEGER;
    notification_count INTEGER;
BEGIN
    RAISE NOTICE '=== TESTING PROPER SINGLE-STEP APPROACH ===';
    RAISE NOTICE 'Test user ID: %', test_user_id;
    
    -- Create a simulated NEW record like the trigger would receive
    SELECT 
        test_user_id as id,
        test_email as email,
        test_metadata as raw_user_meta_data,
        NOW() as created_at
    INTO simulated_new;
    
    -- Test the exact logic from our new function
    DECLARE
        user_profile_id UUID;
        profile_created BOOLEAN := FALSE;
    BEGIN
        -- Step 1: Create profile (same logic as function)
        RAISE NOTICE 'Step 1: Creating profile...';
        BEGIN
            INSERT INTO public.profiles (
                id, 
                first_name, 
                last_name, 
                email, 
                social_visibility, 
                subscription_tier, 
                subscription_status, 
                created_at, 
                updated_at
            )
            VALUES (
                simulated_new.id,
                simulated_new.raw_user_meta_data->>'first_name',
                simulated_new.raw_user_meta_data->>'last_name',
                simulated_new.email,
                'public',
                'none',
                'trial',
                simulated_new.created_at,
                simulated_new.created_at
            );
            
            user_profile_id := simulated_new.id;
            profile_created := TRUE;
            RAISE NOTICE 'Step 1: SUCCESS - Full profile created';

        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Step 1: Full profile failed, trying minimal...';
                BEGIN
                    INSERT INTO public.profiles (id, created_at, updated_at)
                    VALUES (simulated_new.id, simulated_new.created_at, simulated_new.created_at);

                    user_profile_id := simulated_new.id;
                    profile_created := TRUE;
                    RAISE NOTICE 'Step 1: SUCCESS - Minimal profile created';
                    
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'Step 1: FAILED - Even minimal profile failed: %', SQLERRM;
                        profile_created := FALSE;
                END;
        END;
        
        -- Step 2: Create user consent settings
        IF profile_created THEN
            RAISE NOTICE 'Step 2: Creating user consent settings...';
            BEGIN
                INSERT INTO public.user_consent_settings (
                    user_id,
                    profile_visibility,
                    newsletter_subscription,
                    show_businesses,
                    show_events,
                    show_connections,
                    created_at,
                    updated_at
                )
                VALUES (
                    simulated_new.id,
                    true, true, true, true, true,
                    simulated_new.created_at, simulated_new.created_at
                );
                RAISE NOTICE 'Step 2: SUCCESS - User consent settings created';
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Step 2: FAILED - %', SQLERRM;
            END;
        END IF;
        
        -- Step 3: Create notification preferences
        IF profile_created THEN
            RAISE NOTICE 'Step 3: Creating notification preferences...';
            BEGIN
                -- Double-check profile exists
                IF EXISTS (SELECT 1 FROM public.profiles WHERE id = user_profile_id) THEN
                    INSERT INTO public.notification_preferences (
                        profile_id,
                        email_notifications,
                        post_likes,
                        post_comments,
                        comment_replies,
                        comment_likes,
                        connection_requests,
                        connection_accepted,
                        event_signups,
                        event_updates,
                        system_notifications,
                        created_at,
                        updated_at
                    )
                    VALUES (
                        user_profile_id,
                        true, true, true, true, true, true, true, true, true, true,
                        simulated_new.created_at, simulated_new.created_at
                    );
                    RAISE NOTICE 'Step 3: SUCCESS - Notification preferences created';
                ELSE
                    RAISE NOTICE 'Step 3: FAILED - Profile does not exist for foreign key';
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Step 3: FAILED - %', SQLERRM;
            END;
        END IF;
        
        -- Check final counts
        SELECT COUNT(*) INTO profile_count FROM public.profiles WHERE id = test_user_id;
        SELECT COUNT(*) INTO consent_count FROM public.user_consent_settings WHERE user_id = test_user_id;
        SELECT COUNT(*) INTO notification_count FROM public.notification_preferences WHERE notification_preferences.profile_id = test_user_id;

        RAISE NOTICE 'Final counts - Profiles: %, Consent: %, Notifications: %',
            profile_count, consent_count, notification_count;

        -- Clean up
        DELETE FROM public.notification_preferences WHERE notification_preferences.profile_id = test_user_id;
        DELETE FROM public.user_consent_settings WHERE user_id = test_user_id;
        DELETE FROM public.profiles WHERE id = test_user_id;
        
        RAISE NOTICE '=== TEST COMPLETED AND CLEANED UP ===';
    END;
END $$;
