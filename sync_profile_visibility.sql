-- =====================================================
-- SYNC PROFILE VISIBILITY: Keep Both Approach
-- =====================================================
-- Source of truth: user_consent_settings.profile_visibility
-- Cached for performance: profiles.profile_visibility
-- Auto-sync via triggers

-- =====================================================
-- 1. ADD CACHED COLUMN TO PROFILES
-- =====================================================

-- Add profile_visibility column to profiles table (if not exists)
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS profile_visibility BOOLEAN DEFAULT true;

-- =====================================================
-- 2. SYNC EXISTING DATA
-- =====================================================

-- Copy current data from user_consent_settings to profiles
UPDATE profiles 
SET profile_visibility = COALESCE(ucs.profile_visibility, true)
FROM user_consent_settings ucs 
WHERE profiles.id = ucs.user_id;

-- Set default true for profiles without consent settings
UPDATE profiles 
SET profile_visibility = true
WHERE profile_visibility IS NULL;

-- =====================================================
-- 3. CREATE SYNC TRIGGER FUNCTION
-- =====================================================

-- Function to sync profile_visibility changes
CREATE OR REPLACE FUNCTION sync_profile_visibility()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle INSERT/UPDATE on user_consent_settings
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE profiles 
        SET profile_visibility = COALESCE(NEW.profile_visibility, true)
        WHERE id = NEW.user_id;
        RETURN NEW;
    END IF;
    
    -- Handle DELETE on user_consent_settings
    IF TG_OP = 'DELETE' THEN
        UPDATE profiles 
        SET profile_visibility = true  -- Default to visible when consent settings deleted
        WHERE id = OLD.user_id;
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. CREATE TRIGGERS
-- =====================================================

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS sync_profile_visibility_trigger ON user_consent_settings;

-- Create trigger to sync changes from user_consent_settings to profiles
CREATE TRIGGER sync_profile_visibility_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_consent_settings
    FOR EACH ROW
    EXECUTE FUNCTION sync_profile_visibility();

-- =====================================================
-- 5. CREATE PERFORMANCE INDEX
-- =====================================================

-- Create index for fast filtering on profiles.profile_visibility
CREATE INDEX IF NOT EXISTS idx_profiles_visibility_cached 
ON profiles(profile_visibility) WHERE profile_visibility = true;

-- =====================================================
-- 6. HANDLE NEW USER CREATION
-- =====================================================

-- Function to ensure new profiles have correct visibility
CREATE OR REPLACE FUNCTION ensure_profile_visibility()
RETURNS TRIGGER AS $$
BEGIN
    -- When a new profile is created, check if user_consent_settings exists
    IF NEW.profile_visibility IS NULL THEN
        SELECT COALESCE(ucs.profile_visibility, true) INTO NEW.profile_visibility
        FROM user_consent_settings ucs 
        WHERE ucs.user_id = NEW.id;
        
        -- If no consent settings exist, default to true
        IF NEW.profile_visibility IS NULL THEN
            NEW.profile_visibility := true;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS ensure_profile_visibility_trigger ON profiles;

-- Create trigger for new profile creation
CREATE TRIGGER ensure_profile_visibility_trigger
    BEFORE INSERT ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION ensure_profile_visibility();

-- =====================================================
-- 7. VERIFICATION QUERIES
-- =====================================================

-- Check sync status
SELECT 
    'Sync Status Check' as check_type,
    COUNT(*) as total_profiles,
    COUNT(CASE WHEN p.profile_visibility = COALESCE(ucs.profile_visibility, true) THEN 1 END) as synced_profiles,
    COUNT(CASE WHEN p.profile_visibility != COALESCE(ucs.profile_visibility, true) THEN 1 END) as out_of_sync
FROM profiles p
LEFT JOIN user_consent_settings ucs ON p.id = ucs.user_id;

-- Show sample data
SELECT 
    'Sample Data' as check_type,
    p.id,
    p.first_name,
    p.profile_visibility as cached_visibility,
    COALESCE(ucs.profile_visibility, true) as source_visibility,
    CASE 
        WHEN p.profile_visibility = COALESCE(ucs.profile_visibility, true) THEN 'SYNCED'
        ELSE 'OUT_OF_SYNC'
    END as sync_status
FROM profiles p
LEFT JOIN user_consent_settings ucs ON p.id = ucs.user_id
LIMIT 5;

-- =====================================================
-- 8. COMMENTS
-- =====================================================

COMMENT ON COLUMN profiles.profile_visibility IS 
'Cached copy of user_consent_settings.profile_visibility for fast queries. Auto-synced via triggers.';

COMMENT ON FUNCTION sync_profile_visibility() IS 
'Automatically syncs profile_visibility changes from user_consent_settings to profiles table.';

COMMENT ON TRIGGER sync_profile_visibility_trigger ON user_consent_settings IS 
'Keeps profiles.profile_visibility in sync with user_consent_settings.profile_visibility.';

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================

SELECT 
    '✅ Profile visibility sync system created successfully!' as status,
    'Source of truth: user_consent_settings.profile_visibility' as source,
    'Cached for performance: profiles.profile_visibility' as cache,
    'Auto-sync: Triggers keep them synchronized' as sync_method;
