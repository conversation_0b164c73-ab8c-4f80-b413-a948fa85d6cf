-- Create function to synchronize comment like counts
CREATE OR REPLACE FUNCTION public.sync_comment_like_count(comment_id_param UUID)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE post_comments
  SET likes_count = (
    SELECT COUNT(*)
    FROM comment_likes
    WHERE comment_id = comment_id_param
  )
  WHERE id = comment_id_param;
END;
$$;

-- Create function to increment comment like count
CREATE OR REPLACE FUNCTION public.increment_comment_like_count(comment_id_param UUID)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE post_comments
  SET likes_count = likes_count + 1
  WHERE id = comment_id_param;
END;
$$;

-- Create function to decrement comment like count
CREATE OR REPLACE FUNCTION public.decrement_comment_like_count(comment_id_param UUID)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE post_comments
  SET likes_count = GREATEST(likes_count - 1, 0)
  WHERE id = comment_id_param;
END;
$$;

-- Create function to sync all comment like counts
CREATE OR REPLACE FUNCTION public.sync_all_comment_like_counts()
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  UPDATE post_comments pc
  SET likes_count = (
    SELECT COUNT(*)
    FROM comment_likes cl
    WHERE cl.comment_id = pc.id
  );
END;
$$;
