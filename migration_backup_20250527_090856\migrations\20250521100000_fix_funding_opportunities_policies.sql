-- Drop all existing policies first to start fresh
DROP POLICY IF EXISTS "Anyone can view published funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can insert their own funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Authenticated users can insert funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can update their own funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can delete their own funding opportunities" ON "public"."funding_opportunities";

-- First, enable RLS if not already enabled
ALTER TABLE "public"."funding_opportunities" ENABLE ROW LEVEL SECURITY;

-- 1. Allow anyone to view published opportunities
CREATE POLICY "Anyone can view published funding opportunities" 
ON "public"."funding_opportunities" 
FOR SELECT 
USING (status = 'Published' OR auth.uid() = creator_id);

-- 2. Allow authenticated users to insert opportunities (simplified policy)
CREATE POLICY "Authenticated users can insert opportunities" 
ON "public"."funding_opportunities" 
FOR INSERT 
WITH CHECK (
    auth.uid() IS NOT NULL 
    AND auth.uid() = creator_id
);

-- 3. Allow users to update their own opportunities
CREATE POLICY "Users can update their own funding opportunities" 
ON "public"."funding_opportunities" 
FOR UPDATE 
USING (auth.uid() = creator_id);

-- 4. Allow users to delete their own opportunities
CREATE POLICY "Users can delete their own funding opportunities" 
ON "public"."funding_opportunities" 
FOR DELETE 
USING (auth.uid() = creator_id);

-- Grant necessary privileges
GRANT ALL ON "public"."funding_opportunities" TO authenticated;
GRANT SELECT ON "public"."funding_opportunities" TO anon;
