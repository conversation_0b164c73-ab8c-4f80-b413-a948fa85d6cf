-- Migration to fix business directory display permissions
-- This ensures that any user (authenticated or not) can read businesses

-- First, ensure anon and authenticated users can read all businesses
GRANT SELECT ON public.businesses TO anon;
GRANT SELECT ON public.businesses TO authenticated;

-- Next, ensure that they can read related tables for display purposes
GRANT SELECT ON public.netzero_categories TO anon;
GRANT SELECT ON public.netzero_categories TO authenticated;
GRANT SELECT ON public.locations TO anon;
GRANT SELECT ON public.locations TO authenticated;

-- Drop any existing RLS policies that might be conflicting
DROP POLICY IF EXISTS "Anyone can read businesses" ON public.businesses;

-- Create a simple read-only policy for all users
CREATE POLICY "Anyone can view businesses" 
ON public.businesses
FOR SELECT
USING (true);

-- Ensure RLS is enabled on the businesses table
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;

-- For business owners, ensure they can manage their own businesses
DROP POLICY IF EXISTS "Owners can manage their businesses" ON public.businesses;
CREATE POLICY "Owners can manage their businesses"
ON public.businesses
FOR ALL
USING (auth.uid() = owner_id);

-- Grant service_role full access (for admin operations)
GRANT ALL ON public.businesses TO service_role;
