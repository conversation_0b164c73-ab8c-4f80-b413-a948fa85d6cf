-- Test basic table access to see if we can query the tables directly

-- Test if we can select from post_likes
SELECT 'post_likes' as table_name, COUNT(*) as row_count FROM public.post_likes;

-- Test if we can select from post_comments  
SELECT 'post_comments' as table_name, COUNT(*) as row_count FROM public.post_comments;

-- Test if we can select from comment_likes
SELECT 'comment_likes' as table_name, COUNT(*) as row_count FROM public.comment_likes;

-- Test if we can select from social_posts
SELECT 'social_posts' as table_name, COUNT(*) as row_count FROM public.social_posts;

-- Check table structure for post_likes
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'post_likes'
ORDER BY ordinal_position;

-- Check foreign key constraints for post_likes
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public'
AND tc.table_name = 'post_likes';
