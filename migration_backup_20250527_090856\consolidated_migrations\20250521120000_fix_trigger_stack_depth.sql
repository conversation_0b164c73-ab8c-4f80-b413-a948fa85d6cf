-- Drop the existing trigger first
DROP TRIGGER IF EXISTS check_expired_funding_opportunities ON funding_opportunities;
DROP FUNCTION IF EXISTS auto_close_expired_funding_opportunities();

-- Create a better version of the function that won't cause recursive updates
CREATE OR REPLACE FUNCTION auto_close_expired_funding_opportunities()
RETURNS TRIGGER AS $$
BEGIN
    -- Only run the update if we're not already in an UPDATE operation
    -- and only for the specific record being inserted/updated
    IF (TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.status = 'Published')) THEN
        IF (NEW.deadline < CURRENT_DATE AND NEW.status = 'Published') THEN
            NEW.status = 'Closed';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a new BEFORE trigger instead of AFTER
CREATE TRIGGER check_expired_funding_opportunities
BEFORE INSERT OR UPDATE ON funding_opportunities
FOR EACH ROW
EXECUTE FUNCTION auto_close_expired_funding_opportunities();
