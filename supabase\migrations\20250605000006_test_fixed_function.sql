-- Test the fixed handle_new_user function
-- Date: 2025-06-05

-- Test the function manually with a fake user record
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_metadata JSONB := '{"first_name": "Test", "last_name": "User"}';
    error_msg TEXT;
    profile_count INTEGER;
    consent_count INTEGER;
    notification_count INTEGER;
BEGIN
    -- Try to manually execute what the function should do
    RAISE NOTICE 'Testing fixed function with user_id: %', test_user_id;
    
    -- Test 1: Insert into profiles (with correct columns)
    BEGIN
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            email, 
            social_visibility, 
            subscription_tier, 
            subscription_status, 
            created_at, 
            updated_at
        )
        VALUES (
            test_user_id,
            test_metadata->>'first_name',
            test_metadata->>'last_name',
            test_email,
            'public',
            'none',
            'trial',
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Profile insert: SUCCESS';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Profile insert: FAILED - %', error_msg;
    END;
    
    -- Test 2: Insert into user_consent_settings (with only existing columns)
    BEGIN
        INSERT INTO public.user_consent_settings (
            user_id,
            profile_visibility,
            newsletter_subscription,
            show_businesses,
            show_events,
            show_connections,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            true, false, true, true, true,
            NOW(), NOW()
        );
        RAISE NOTICE 'User consent settings insert: SUCCESS';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'User consent settings insert: FAILED - %', error_msg;
    END;
    
    -- Test 3: Insert into notification_preferences
    BEGIN
        INSERT INTO public.notification_preferences (
            profile_id,
            email_notifications,
            post_likes,
            post_comments,
            comment_replies,
            comment_likes,
            connection_requests,
            connection_accepted,
            event_signups,
            event_updates,
            system_notifications,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            true, true, true, true, true, true, true, true, true, true,
            NOW(), NOW()
        );
        RAISE NOTICE 'Notification preferences insert: SUCCESS';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Notification preferences insert: FAILED - %', error_msg;
    END;
    
    -- Check if records were created
    SELECT COUNT(*) INTO profile_count FROM public.profiles WHERE id = test_user_id;
    SELECT COUNT(*) INTO consent_count FROM public.user_consent_settings WHERE user_id = test_user_id;
    SELECT COUNT(*) INTO notification_count FROM public.notification_preferences WHERE profile_id = test_user_id;
    
    RAISE NOTICE 'Records created - Profiles: %, Consent: %, Notifications: %', 
        profile_count, consent_count, notification_count;
    
    -- Clean up test records
    DELETE FROM public.notification_preferences WHERE profile_id = test_user_id;
    DELETE FROM public.user_consent_settings WHERE user_id = test_user_id;
    DELETE FROM public.profiles WHERE id = test_user_id;
    
    RAISE NOTICE 'Test completed and cleaned up';
END $$;
