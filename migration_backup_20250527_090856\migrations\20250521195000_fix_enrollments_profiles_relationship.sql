-- Add profile_id column and foreign key to training_course_enrollments
ALTER TABLE training_course_enrollments
ADD COLUMN profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE;

-- Update existing records to set profile_id based on user_id
UPDATE training_course_enrollments e
SET profile_id = p.id
FROM profiles p
WHERE p.id = e.user_id;

-- Create index for performance
CREATE INDEX training_course_enrollments_profile_id_idx ON training_course_enrollments(profile_id);

-- Update the view to use profile_id instead of joining through user_id
DROP VIEW IF EXISTS training_course_enrollments_view;
CREATE OR REPLACE VIEW training_course_enrollments_view AS
SELECT 
    e.*,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.avatar_url,
    p.title as user_title,
    p.organization as user_organization,
    p.email as user_email,
    t.title as course_title,
    t.organization_name as course_provider
FROM 
    training_course_enrollments e
JOIN 
    profiles p ON e.profile_id = p.id
JOIN 
    training_courses t ON e.course_id = t.id
WHERE 
    auth.uid() = e.user_id OR 
    auth.uid() IN (
        SELECT creator_id 
        FROM training_courses 
        WHERE id = e.course_id
    );
