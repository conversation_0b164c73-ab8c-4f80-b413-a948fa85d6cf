// Live user connection statistics component for UserCard
import { useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users } from "lucide-react";
import { useSocialNetworkV2 } from "@/hooks/social/useSocialNetworkV2";
import { getCloudflareImageUrl } from "@/lib/cloudflare";
import { ConnectionsDrawer } from "@/components/social/ConnectionsDrawer";
import { supabase } from "@/integrations/supabase/client";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

interface UserConnectionStatsProps {
  className?: string;
}

export const UserConnectionStats = ({ className }: UserConnectionStatsProps) => {
  const navigate = useNavigate();
  const { connections, connectionRequests, sentRequests, refreshAll } = useSocialNetworkV2();

  // Load connection data when component mounts
  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  // Calculate stats from hook data
  const totalConnections = connections.length;
  const totalPendingRequests = connectionRequests.length;
  const totalSentRequests = sentRequests.length;

  // Get recent connections (last 3)
  const recentConnections = connections
    .sort((a, b) => new Date(b.connected_at).getTime() - new Date(a.connected_at).getTime())
    .slice(0, 3);

  // Calculate new connections this month
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const newConnectionsThisMonth = connections.filter(connection => {
    const connectionDate = new Date(connection.connected_at);
    return connectionDate.getMonth() === currentMonth &&
           connectionDate.getFullYear() === currentYear;
  }).length;

  // Handle profile click navigation
  const handleProfileClick = (userId: string) => {
    navigate(`/members/${userId}`);
  };

  // Listen for connection updates from ConnectionsDrawer
  useEffect(() => {
    const handleConnectionUpdate = () => {
      refreshAll();
    };

    window.addEventListener('connectionDataUpdated', handleConnectionUpdate);

    return () => {
      window.removeEventListener('connectionDataUpdated', handleConnectionUpdate);
    };
  }, [refreshAll]);

  return (
    <>
      <div className={`mt-6 w-full ${className || ''}`}>
        <div className="border-t pt-4">
          <div className="flex items-center justify-center mb-4">
            <Users className="mr-2 h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Your Network</h3>
          </div>

          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{totalConnections}</p>
              <p className="text-xs text-muted-foreground">Connections</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-500">{newConnectionsThisMonth}</p>
              <p className="text-xs text-muted-foreground">New this month</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-amber-500">{totalPendingRequests}</p>
              <p className="text-xs text-muted-foreground">Pending</p>
            </div>
          </div>

          {recentConnections.length > 0 && (
            <div className="space-y-3 mb-4">
              <h4 className="text-sm font-medium">Recent Connections</h4>
              {recentConnections.map((connection) => {
                const fullName = [connection.first_name, connection.last_name].filter(Boolean).join(' ') || 'Unknown User';
                const avatarUrl = connection.avatar_url
                  ? getCloudflareImageUrl(connection.avatar_url)
                  : null;

                return (
                  <div
                    key={connection.connection_id}
                    className="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                    onClick={() => handleProfileClick(connection.user_id)}
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      {avatarUrl ? (
                        <AvatarImage src={avatarUrl} alt={fullName} />
                      ) : (
                        <AvatarFallback className="rounded-lg">
                          {fullName.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div className="ml-3 text-left">
                      <p className="text-sm font-medium hover:text-primary">{fullName}</p>
                      <p className="text-xs text-muted-foreground">
                        {[connection.title, connection.organization].filter(Boolean).join(' at ') || 'Professional'}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

        </div>
      </div>

      {/* ConnectionsDrawer renders its own button */}
      <div className="mt-4 flex justify-center">
        <ConnectionsDrawer
          defaultOpen={false}
          initialTab="connections"
        />
      </div>
    </>
  );
};

export default UserConnectionStats;
