# Fixing Profile Column Mismatch in NetZeroHub Platform

## Problem
We're encountering another database mismatch error:

```
Could not find the 'main_industry_id' column of 'profiles' in the schema cache
```

The application code is trying to access a column called `main_industry_id` in the `profiles` table, but the database only has a column called `industry_id`.

## Root Cause
This is a classic case of schema drift between:
- What the application code expects (column name: `main_industry_id`)
- What actually exists in the database (column name: `industry_id`)

This type of mismatch often happens when:
- Database changes are made without updating application code
- Application code is developed against a different database schema
- Migration scripts are written with different column names than what the application expects

## Solution
I've created a script (`FixProfileColumns.sql`) that provides two possible fixes:

1. **Add a generated column** (preferred approach):
   - Add `main_industry_id` as a generated column that's always equal to `industry_id`
   - This creates a virtual column that automatically stays in sync with `industry_id`
   - The application can use either column name without issues
   - No data duplication or sync issues

2. **Use a trigger as fallback**:
   - If the generated column approach doesn't work for some reason, a trigger is created
   - The trigger keeps both columns in sync when either one is updated
   - This ensures data consistency regardless of which column is modified

## How to Apply the Fix

Run the `FixProfileColumns.sql` script against your Supabase database:

1. Open the Supabase dashboard (https://supabase.com/dashboard/project/psowjyllxqzllhbiyjtn)
2. Go to the SQL Editor
3. Copy and paste the contents of `FixProfileColumns.sql`
4. Run the script

## Verification

After applying the fix:
1. The script will display a notice indicating if the column was successfully created
2. You can query the `profiles` table to verify both column names work
3. The application should no longer show the "Could not find column" error
4. Updates to either column will be reflected in both places

## Long-term Recommendation

For a permanent solution, consider:
1. Standardizing column names across the application and database
2. Using TypeScript types or an ORM with schema validation
3. Implementing CI checks that verify database schema matches application expectations
