-- Drop existing policies for business_relevant_industries
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable write access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable write access for authenticated users" ON public.business_relevant_industries;

-- Create new policies for business_relevant_industries
CREATE POLICY "Enable read access for all"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for business owners"
    ON public.business_relevant_industries
    FOR INSERT
    TO authenticated
    WITH CHECK (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

CREATE POLICY "Enable update/delete for business owners"
    ON public.business_relevant_industries
    FOR ALL
    TO authenticated
    USING (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

-- Grant additional permissions
GRANT ALL ON public.business_relevant_industries TO authenticated;

-- Verify permissions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'business_relevant_industries' 
        AND policyname = 'Enable insert for business owners'
    ) THEN
        RAISE NOTICE 'Missing insert policy for business_relevant_industries table';
    END IF;
END $$;
