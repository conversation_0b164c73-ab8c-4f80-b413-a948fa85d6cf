-- Test if the useUserCategories hook query is working
-- This simulates the exact query used by the hook

-- Replace 'USER_ID_HERE' with an actual user ID that has categories
-- You can get a user ID from: SELECT user_id FROM user_categories LIMIT 1;

-- First, get a user ID that has categories
SELECT DISTINCT user_id 
FROM user_categories 
LIMIT 5;

-- Then test the exact query used by useUserCategories hook
-- Replace the UUID below with one from the query above
SELECT 
    category_id,
    netzero_categories.id,
    netzero_categories.name,
    netzero_categories.parent_id
FROM user_categories
LEFT JOIN netzero_categories ON netzero_categories.id = user_categories.category_id
WHERE user_categories.user_id = 'REPLACE_WITH_ACTUAL_USER_ID';
