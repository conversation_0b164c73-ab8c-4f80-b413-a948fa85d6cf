-- Create RPC function to get public profiles for suggestions
-- This function can bypass <PERSON><PERSON> to properly filter by profile_visibility

CREATE OR REPLACE FUNCTION get_public_profiles_for_suggestions(
    requesting_user_id UUID,
    suggestion_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    first_name TEXT,
    last_name TEXT,
    title TEXT,
    organization TEXT,
    avatar_url TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        p.title,
        p.organization,
        p.avatar_url
    FROM profiles p
    LEFT JOIN user_consent_settings ucs ON ucs.user_id = p.id
    WHERE 
        p.id != requesting_user_id
        AND (
            -- If no consent settings exist, default to visible (backward compatibility)
            ucs.profile_visibility IS NULL 
            OR 
            -- Only show profiles where visibility is explicitly true
            ucs.profile_visibility = true
        )
        AND p.id NOT IN (
            -- Exclude users already connected or with pending requests
            SELECT CASE 
                WHEN uc.user_id = requesting_user_id THEN uc.connection_id
                ELSE uc.user_id
            END
            FROM user_connections uc
            WHERE (uc.user_id = requesting_user_id OR uc.connection_id = requesting_user_id)
        )
    ORDER BY p.created_at DESC
    LIMIT suggestion_limit;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_public_profiles_for_suggestions(UUID, INTEGER) TO authenticated;
