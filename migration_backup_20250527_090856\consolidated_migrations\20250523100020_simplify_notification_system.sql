-- SIMPLIFY NOTIFICATION SYSTEM
-- This migration creates a simplified notification system with minimal but reliable functionality

-- First, drop the existing view to avoid conflicts
DROP VIEW IF EXISTS user_notifications_with_details;

-- Create a simplified version of the view that matches the frontend interface
CREATE OR REPLACE VIEW user_notifications_with_details AS
SELECT 
    n.id,
    n.user_id,
    n.type,
    n.related_id AS related_entity_id,
    n.related_type AS related_entity_type,
    n.content AS message,
    -- Parse content as JSON in case it contains structured data
    CASE 
      WHEN n.content ~ '^\\s*\\{.*\\}\\s*$' THEN n.content::jsonb
      ELSE NULL::jsonb
    END AS data,
    n.is_read,
    n.created_at,
    n.updated_at,
    -- Set these to NULL for now - can be populated later if needed
    NULL AS actor_first_name,
    NULL AS actor_last_name,
    NULL AS actor_avatar_url
FROM notifications n;

-- Grant permissions to the view
GRANT SELECT ON user_notifications_with_details TO authenticated;

-- Create a simplified function to create notifications with just the essential fields
CREATE OR REPLACE FUNCTION create_simple_notification(
    p_user_id UUID,
    p_type VARCHAR(50),
    p_message TEXT,
    p_related_id UUID DEFAULT NULL,
    p_related_type VARCHAR(50) DEFAULT NULL
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
    v_notification_id UUID;
BEGIN
    -- Insert notification with the minimum required fields
    INSERT INTO notifications (
        user_id,
        type,
        content,
        related_id,
        related_type,
        is_read
    )
    VALUES (
        p_user_id,
        p_type,
        p_message,
        p_related_id,
        p_related_type,
        FALSE
    )
    RETURNING id INTO v_notification_id;
    
    RETURN v_notification_id;
END;
$$ LANGUAGE plpgsql;

-- Grant permission to service role
GRANT EXECUTE ON FUNCTION create_simple_notification TO service_role;

-- Create a simple test function to add test notifications (for debugging only)
CREATE OR REPLACE FUNCTION create_test_notifications()
RETURNS VOID
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user's ID
    v_user_id := auth.uid();
    
    -- Create a few test notifications
    PERFORM create_simple_notification(
        v_user_id,
        'system',
        'Welcome to the NetZero Platform!'
    );
    
    PERFORM create_simple_notification(
        v_user_id,
        'post_like',
        'Someone liked your post',
        '00000000-0000-0000-0000-000000000001'::UUID,
        'posts'
    );
    
    PERFORM create_simple_notification(
        v_user_id,
        'event_signup',
        'Someone signed up for your event',
        '00000000-0000-0000-0000-000000000002'::UUID,
        'events'
    );
    
    PERFORM create_simple_notification(
        v_user_id,
        'connection_request',
        'You received a new connection request',
        '00000000-0000-0000-0000-000000000003'::UUID,
        'profiles'
    );
END;
$$ LANGUAGE plpgsql;

-- Grant permission to authenticated users for testing
GRANT EXECUTE ON FUNCTION create_test_notifications TO authenticated;
