-- Drop the old view and function if they exist
DROP VIEW IF EXISTS training_course_enrollments_view;
DROP FUNCTION IF EXISTS get_enrollment_access;

-- Create the view using only RLS-protected tables
CREATE VIEW training_course_enrollments_view AS
SELECT 
    e.id,
    e.course_id,
    e.user_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.avatar_url,
    p.title as user_title,
    p.organization as user_organization,
    p.email as user_email,
    t.title as course_title,
    t.organization_name as course_provider
FROM 
    training_course_enrollments e
JOIN 
    profiles p ON e.profile_id = p.id
JOIN 
    training_courses t ON e.course_id = t.id;

-- Grant access to authenticated users
GRANT SELECT ON training_course_enrollments_view TO authenticated;

-- Note: The view will inherit RLS policies from the base tables
-- training_course_enrollments and training_courses already have RLS policies
-- that control access based on user_id and creator_id respectively
