-- Fix businesses RLS policy to allow authenticated users to read all businesses
-- This ensures ProfileBusinesses component can show businesses for any user profile

BEGIN;

-- Drop the policy if it exists, then create it
DROP POLICY IF EXISTS "businesses_authenticated_read" ON businesses;

-- Add policy for authenticated users to read all businesses
CREATE POLICY "businesses_authenticated_read" ON businesses
    FOR SELECT TO authenticated USING (true);

-- Grant necessary permissions
GRANT SELECT ON businesses TO authenticated;

COMMIT;
