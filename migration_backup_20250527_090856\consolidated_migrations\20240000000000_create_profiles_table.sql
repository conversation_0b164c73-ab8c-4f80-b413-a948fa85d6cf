-- Create profiles table linked to Supabase auth.users
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  title TEXT,
  organization TEXT,
  bio TEXT,
  expertise TEXT,
  website TEXT,
  linkedin TEXT,
  twitter TEXT,
  social_visibility TEXT DEFAULT 'public' CHECK (social_visibility IN ('public', 'connections', 'private')),
  profile_visibility BOOLEAN DEFAULT true,
  email_notifications BOOLEAN DEFAULT true,
  newsletter_subscription BOOLEAN DEFAULT false,
  show_businesses BOOLEAN DEFAULT true,
  show_events BOOLEAN DEFAULT true,
  show_connections BOOLEAN DEFAULT true,
  subscription_tier TEXT DEFAULT 'none' CHECK (subscription_tier IN ('none', 'seedling', 'sapling', 'woodland')),
  sponsorship_tier TEXT DEFAULT 'none' CHECK (sponsorship_tier IN ('none', 'seedling', 'sapling', 'woodland')),
  subscription_start_date TIMESTAMP WITH TIME ZONE,
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'canceled', 'expired', 'trial')),
  payment_method_id TEXT,
  customer_id TEXT,
  post_count INTEGER DEFAULT 0,
  connection_count INTEGER DEFAULT 0,
  industry_id UUID,
  main_industry_id UUID GENERATED ALWAYS AS (industry_id) STORED,
  location_id UUID,
  is_sustainability_professional BOOLEAN DEFAULT false,
  email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create trigger to create profile when a new user signs up
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email)
  VALUES (NEW.id, NEW.email);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on auth.users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();

-- Enable RLS on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for basic profile access
CREATE POLICY "Users can view basic profile info"
ON public.profiles
FOR SELECT
USING (TRUE);

-- Create policy for email access
CREATE POLICY "Users can only see their own email"
ON public.profiles
FOR SELECT
USING (
  -- Can only see email if you are the profile owner
  auth.uid() = id
)
WITH CHECK (
  auth.uid() = id
);

-- Create policy for updating profiles
CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);
