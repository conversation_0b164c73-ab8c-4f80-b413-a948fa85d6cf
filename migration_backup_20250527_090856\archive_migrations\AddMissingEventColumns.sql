-- Add missing columns to the events table to fix schema mismatch errors
--
-- This script addresses the following errors:
-- 1. "Could not find the 'industry_ids' column of 'events' in the schema cache"
-- 2. "Could not find the 'netzero_category_ids' column of 'events' in the schema cache"
--
-- These errors occur because the frontend code is trying to use columns that don't exist
-- in the database schema. This script adds those missing columns to resolve the errors.
BEGIN;

-- First, drop the old industry column if it's no longer needed
-- Uncomment if you want to drop the old column
-- ALTER TABLE public.events DROP COLUMN IF EXISTS industry;

-- Add the new industry_ids column
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS industry_ids TEXT[] DEFAULT '{}';

-- Add the new netzero_category_ids column
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS netzero_category_ids TEXT[] DEFAULT '{}';

-- Update the RLS policies to ensure they work with the new column
-- No changes needed for RLS policies since they're based on creator_user_id

-- Create an index on the new columns for better performance
CREATE INDEX IF NOT EXISTS idx_events_industry_ids ON public.events USING GIN (industry_ids);
CREATE INDEX IF NOT EXISTS idx_events_netzero_category_ids ON public.events USING GIN (netzero_category_ids);

-- Update any existing events to migrate data if needed (optional)
-- UPDATE public.events SET industry_ids = ARRAY[industry] WHERE industry IS NOT NULL;

COMMIT;

-- Verify the column was added
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'events' 
ORDER BY ordinal_position;
