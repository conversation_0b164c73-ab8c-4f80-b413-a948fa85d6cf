# Education Feature Implementation Plan

## 1. Component Restructuring
- [ ] Create new Features/Education directory structure
  - [ ] Move existing components:
    - [ ] Move UserEducation.tsx to Features/Education/UserEducation.tsx
    - [ ] Move UserEducationForm.tsx to Features/Education/UserEducationForm.tsx
    - [ ] Move EducationProfile.tsx to Features/Education/EducationProfile.tsx
  - [ ] Create new components:
    - [ ] Features/Education/EnrollmentRequest.tsx (for handling enrollment requests)
    - [ ] Features/Education/CourseDetails.tsx (reusable course display component)
    - [ ] Features/Education/EnrollmentList.tsx (for course creators to view requests)

## 2. Database Updates
- [ ] Apply new migration for enhanced training_courses table
- [ ] Create new table for enrollment requests:
```sql
CREATE TABLE training_course_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID REFERENCES training_courses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'waitlisted')),
    request_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    response_message TEXT,
    response_at TIMESTAMPTZ
);
```
- [ ] Add RLS policies for enrollment management
- [ ] Create notification triggers for enrollment requests

## 3. Enrollment Feature Implementation
- [ ] Create enrollment request form with:
  - [ ] Contact information confirmation
  - [ ] Optional message to course provider
  - [ ] Preferred start date (if multiple dates available)
- [ ] Implement enrollment request submission
- [ ] Add notification system for:
  - [ ] Course provider when new request received
  - [ ] User when request status changes
- [ ] Create enrollment management interface for course providers

## 4. Course Provider Dashboard Enhancements
- [ ] Add EnrollmentList component to view all requests
- [ ] Implement actions:
  - [ ] Approve/Reject requests
  - [ ] Send responses to applicants
  - [ ] Manage enrollment capacity
  - [ ] Track enrollment statistics

## 5. User Experience Improvements
- [ ] Add status indicators for enrollment requests
- [ ] Implement email notifications
- [ ] Add course availability status
- [ ] Create waitlist functionality
- [ ] Add course ratings and reviews system

## 6. API Endpoints Required
- [ ] POST /api/training-courses/{id}/enroll
- [ ] GET /api/training-courses/{id}/enrollments
- [ ] PUT /api/training-courses/{id}/enrollments/{enrollmentId}
- [ ] GET /api/users/my-enrollments

## 7. Security Considerations
- [ ] Ensure proper RLS policies for enrollment data
- [ ] Validate user permissions for course management
- [ ] Implement rate limiting for enrollment requests
- [ ] Add spam prevention for enrollment messages

## Migration Strategy
1. Create new Features/Education directory
2. Move existing components one at a time
3. Test each component after moving
4. Apply database migrations
5. Implement new features iteratively

## Dependencies
- Supabase for database and authentication
- Email notification system
- UI components from shadcn/ui
- React Router for navigation

## Testing Plan
- [ ] Unit tests for enrollment logic
- [ ] Integration tests for database operations
- [ ] E2E tests for enrollment flow
- [ ] Security testing for RLS policies
- [ ] UI testing for responsive design

## Future Enhancements
- Course waitlist management
- Automated enrollment approvals
- Calendar integration for course dates
- Payment integration for paid courses
- Course analytics dashboard
