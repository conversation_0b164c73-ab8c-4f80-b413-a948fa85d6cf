-- Remove duplicate email_notifications column from user_consent_settings
-- Email notifications are now handled exclusively in notification_preferences table
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: BACKUP DATA FOR AUDIT PURPOSES
-- ================================================================

-- Create a backup table to track email notification settings before removal
CREATE TABLE IF NOT EXISTS public.removed_email_notifications_backup AS
SELECT 
    user_id,
    email_notifications,
    updated_at as email_notifications_last_updated,
    NOW() as backup_created_at,
    'Moved to notification_preferences.email_notifications for consolidation' as removal_reason
FROM public.user_consent_settings
WHERE email_notifications IS NOT NULL;

-- ================================================================
-- PART 2: MIGRATE DATA TO NOTIFICATION_PREFERENCES
-- ================================================================

-- Ensure all users have notification_preferences records
-- and sync the email_notifications setting from user_consent_settings
DO $$
DECLARE
    user_record RECORD;
    migrated_count INTEGER := 0;
BEGIN
    -- Loop through all users with email_notifications settings
    FOR user_record IN 
        SELECT ucs.user_id, ucs.email_notifications
        FROM public.user_consent_settings ucs
        WHERE ucs.email_notifications IS NOT NULL
    LOOP
        -- Insert or update notification_preferences with the email_notifications value
        INSERT INTO public.notification_preferences (
            profile_id,
            email_notifications,
            post_likes,
            post_comments,
            comment_replies,
            comment_likes,
            connection_requests,
            connection_accepted,
            event_signups,
            event_updates,
            system_notifications,
            created_at,
            updated_at
        )
        VALUES (
            user_record.user_id,
            user_record.email_notifications, -- Migrate the setting
            true, -- Default values for other preferences
            true,
            true,
            true,
            true,
            true,
            true,
            true,
            true,
            NOW(),
            NOW()
        )
        ON CONFLICT (profile_id) 
        DO UPDATE SET
            email_notifications = user_record.email_notifications,
            updated_at = NOW();
        
        migrated_count := migrated_count + 1;
    END LOOP;
    
    RAISE NOTICE 'Migrated email_notifications setting for % users to notification_preferences', migrated_count;
END $$;

-- ================================================================
-- PART 3: DROP DEPENDENT VIEWS BEFORE REMOVING COLUMN
-- ================================================================

-- Drop views that might depend on the email_notifications column
DROP VIEW IF EXISTS public.user_privacy_settings_summary CASCADE;

-- ================================================================
-- PART 4: REMOVE THE DUPLICATE COLUMN
-- ================================================================

-- Remove the email_notifications column from user_consent_settings
ALTER TABLE public.user_consent_settings 
DROP COLUMN IF EXISTS email_notifications;

-- ================================================================
-- PART 5: RECREATE VIEWS WITHOUT THE REMOVED COLUMN
-- ================================================================

-- Recreate user_privacy_settings_summary view without email_notifications
CREATE OR REPLACE VIEW public.user_privacy_settings_summary AS
SELECT 
    p.id as profile_id,
    p.first_name,
    p.last_name,
    p.email,
    -- Consent settings (remaining columns)
    ucs.profile_visibility,
    ucs.newsletter_subscription,
    ucs.show_businesses,
    ucs.show_events,
    ucs.show_connections,
    ucs.consent_updated_at,
    ucs.created_at as consent_created_at,
    ucs.updated_at as consent_last_updated,
    -- Notification preferences (including email_notifications)
    np.email_notifications,
    np.post_likes,
    np.post_comments,
    np.comment_replies,
    np.comment_likes,
    np.connection_requests,
    np.connection_accepted,
    np.event_signups,
    np.event_updates,
    np.system_notifications,
    -- GDPR consent summary from event signups
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id) as total_event_signups,
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id AND es.gdpr_consent = true) as gdpr_consents_given
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
LEFT JOIN public.notification_preferences np ON p.id = np.profile_id;

-- Grant access to the updated view
GRANT SELECT ON public.user_privacy_settings_summary TO authenticated;

-- ================================================================
-- PART 6: UPDATE FUNCTIONS IF NEEDED
-- ================================================================

-- Update get_user_consent_settings function to remove email_notifications
DROP FUNCTION IF EXISTS public.get_user_consent_settings();

CREATE OR REPLACE FUNCTION public.get_user_consent_settings()
RETURNS TABLE (
    user_id UUID,
    profile_visibility BOOLEAN,
    newsletter_subscription BOOLEAN,
    show_businesses BOOLEAN,
    show_events BOOLEAN,
    show_connections BOOLEAN,
    consent_updated_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ucs.user_id,
        ucs.profile_visibility,
        ucs.newsletter_subscription,
        ucs.show_businesses,
        ucs.show_events,
        ucs.show_connections,
        ucs.consent_updated_at,
        ucs.created_at,
        ucs.updated_at
    FROM public.user_consent_settings ucs
    WHERE ucs.user_id = auth.uid();
    
    -- If no record exists, return defaults
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            auth.uid(), 
            true::BOOLEAN,  -- profile_visibility default
            false::BOOLEAN, -- newsletter_subscription default
            true::BOOLEAN,  -- show_businesses default
            true::BOOLEAN,  -- show_events default
            true::BOOLEAN,  -- show_connections default
            NULL::TIMESTAMPTZ, 
            NULL::TIMESTAMPTZ, 
            NULL::TIMESTAMPTZ;
    END IF;
END;
$$;

COMMIT;

-- ================================================================
-- VERIFICATION AND REPORTING
-- ================================================================

-- Report what was migrated
SELECT 'Email notifications migration summary:' as info;
SELECT 
    COUNT(*) as total_users_migrated,
    COUNT(CASE WHEN email_notifications = true THEN 1 END) as had_email_notifications_enabled,
    COUNT(CASE WHEN email_notifications = false THEN 1 END) as had_email_notifications_disabled
FROM public.removed_email_notifications_backup;

-- Show the consolidated notification system
SELECT 'Consolidated notification preferences:' as info;
SELECT 
    COUNT(*) as total_users_with_preferences,
    COUNT(CASE WHEN email_notifications = true THEN 1 END) as email_notifications_enabled,
    COUNT(CASE WHEN email_notifications = false THEN 1 END) as email_notifications_disabled
FROM public.notification_preferences;

-- Show updated user consent settings structure
SELECT 'Updated user consent settings (no email_notifications):' as info;
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN profile_visibility = true THEN 1 END) as profile_visibility_enabled,
    COUNT(CASE WHEN newsletter_subscription = true THEN 1 END) as newsletter_subscription_enabled
FROM public.user_consent_settings;
