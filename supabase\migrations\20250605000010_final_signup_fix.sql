-- Final fix for signup issue - simplified and bulletproof
-- Date: 2025-06-05

BEGIN;

-- Create a completely bulletproof handle_new_user function
-- This version will work step by step and handle any errors gracefully
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
  error_context TEXT;
BEGIN
  -- Step 1: Create profile record (minimal first, then update)
  BEGIN
    INSERT INTO public.profiles (id, created_at, updated_at)
    VALUES (NEW.id, NEW.created_at, NEW.created_at)
    RETURNING id INTO profile_id;
    
    -- Update with additional fields if they exist
    UPDATE public.profiles 
    SET 
      first_name = NEW.raw_user_meta_data->>'first_name',
      last_name = NEW.raw_user_meta_data->>'last_name',
      email = NEW.email,
      social_visibility = 'public',
      subscription_tier = 'none',
      subscription_status = 'trial'
    WHERE id = profile_id;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- If profile creation fails, log but don't fail the signup
      error_context := 'Profile creation failed: ' || SQLERRM;
      RAISE WARNING '%', error_context;
      -- Set profile_id to NEW.id so other operations can still work
      profile_id := NEW.id;
  END;

  -- Step 2: Create user consent settings (only if table exists)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'user_consent_settings'
    ) THEN
      INSERT INTO public.user_consent_settings (
        user_id,
        profile_visibility,
        newsletter_subscription,
        show_businesses,
        show_events,
        show_connections,
        created_at,
        updated_at
      )
      VALUES (
        NEW.id,
        true, false, true, true, true,
        NEW.created_at, NEW.created_at
      )
      ON CONFLICT (user_id) DO NOTHING;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      error_context := 'User consent settings creation failed: ' || SQLERRM;
      RAISE WARNING '%', error_context;
  END;

  -- Step 3: Create notification preferences (only if table exists)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'notification_preferences'
    ) THEN
      -- Check if table has the expected structure
      IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'notification_preferences' 
        AND column_name = 'post_likes'
      ) THEN
        INSERT INTO public.notification_preferences (
          profile_id,
          email_notifications,
          post_likes,
          post_comments,
          comment_replies,
          comment_likes,
          connection_requests,
          connection_accepted,
          event_signups,
          event_updates,
          system_notifications,
          created_at,
          updated_at
        )
        VALUES (
          profile_id,
          true, true, true, true, true, true, true, true, true, true,
          NEW.created_at, NEW.created_at
        )
        ON CONFLICT (profile_id) DO NOTHING;
      END IF;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      error_context := 'Notification preferences creation failed: ' || SQLERRM;
      RAISE WARNING '%', error_context;
  END;

  -- Always return NEW to ensure signup succeeds even if some steps fail
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates profile, user consent settings, and notification preferences for new users - bulletproof version';

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

COMMIT;
