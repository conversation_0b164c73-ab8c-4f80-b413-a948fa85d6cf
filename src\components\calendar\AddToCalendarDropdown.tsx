import React from 'react';
import { Calendar, ChevronDown, Download, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Event } from '@/types/events';
import { generateICSFile } from '@/lib/events';
import { useToast } from '@/hooks/use-toast';

interface AddToCalendarDropdownProps {
  event: Event;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  isUserRegistered?: boolean;
}

export const AddToCalendarDropdown: React.FC<AddToCalendarDropdownProps> = ({
  event,
  variant = 'outline',
  size = 'sm',
  className = '',
  isUserRegistered = false
}) => {
  const { toast } = useToast();

  // Format date for URL parameters (YYYYMMDDTHHMMSSZ)
  const formatDateForUrl = (date: string, time: string): string => {
    try {
      const dt = new Date(`${date}T${time}`);
      return dt.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    } catch {
      return new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    }
  };

  // Generate calendar URLs
  const getCalendarUrls = () => {
    const startDateTime = formatDateForUrl(event.start_date, event.start_time);
    const endDateTime = event.end_date && event.end_time
      ? formatDateForUrl(event.end_date, event.end_time)
      : formatDateForUrl(event.start_date, 
          // Add 1 hour if no end time
          new Date(new Date(`${event.start_date}T${event.start_time}`).getTime() + 60 * 60 * 1000)
            .toTimeString().slice(0, 5)
        );

    const title = encodeURIComponent(event.title);
    const description = encodeURIComponent(event.description || '');
    
    // Create location string - since this component only shows for registered users, include meeting URLs
    const location = event.event_type === 'remote'
      ? encodeURIComponent(event.meeting_url || 'Remote Event')
      : event.event_type === 'hybrid'
        ? encodeURIComponent(`${event.physical_location || ''} / ${event.meeting_url || 'Remote Access Available'}`)
        : encodeURIComponent(event.physical_location || '');

    return {
      google: `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startDateTime}/${endDateTime}&details=${description}&location=${location}&sf=true&output=xml`,
      
      outlook: `https://outlook.live.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startDateTime}&enddt=${endDateTime}&body=${description}&location=${location}`,
      
      outlookOffice: `https://outlook.office.com/calendar/0/deeplink/compose?subject=${title}&startdt=${startDateTime}&enddt=${endDateTime}&body=${description}&location=${location}`,
      
      yahoo: `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${title}&st=${startDateTime}&et=${endDateTime}&desc=${description}&in_loc=${location}`,
    };
  };

  const handleCalendarClick = (provider: string, url: string) => {
    try {
      window.open(url, '_blank', 'noopener,noreferrer');
      toast({
        title: "Opening calendar",
        description: `Redirecting to ${provider}...`,
      });
    } catch (error) {
      console.error('Error opening calendar:', error);
      toast({
        title: "Error",
        description: "Failed to open calendar. Please try downloading the file instead.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadICS = () => {
    try {
      const icsContent = generateICSFile(event);
      const blob = new Blob([icsContent], {
        type: 'text/calendar;charset=utf-8'
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${event.title.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}.ics`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Calendar file downloaded",
        description: "Open the file to add the event to your calendar app.",
      });
    } catch (error) {
      console.error('Error downloading ICS:', error);
      toast({
        title: "Download failed",
        description: "Failed to generate calendar file. Please try again.",
        variant: "destructive",
      });
    }
  };

  const urls = getCalendarUrls();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Calendar className="w-4 h-4 mr-2" />
          Add to Calendar
          <ChevronDown className="w-4 h-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem 
          onClick={() => handleCalendarClick('Google Calendar', urls.google)}
          className="cursor-pointer"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Google Calendar
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => handleCalendarClick('Outlook Online', urls.outlook)}
          className="cursor-pointer"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Outlook Online
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => handleCalendarClick('Outlook Office 365', urls.outlookOffice)}
          className="cursor-pointer"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Outlook Office 365
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => handleCalendarClick('Yahoo Calendar', urls.yahoo)}
          className="cursor-pointer"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Yahoo Calendar
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleDownloadICS}
          className="cursor-pointer"
        >
          <Download className="w-4 h-4 mr-2" />
          Download .ics file
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AddToCalendarDropdown;
