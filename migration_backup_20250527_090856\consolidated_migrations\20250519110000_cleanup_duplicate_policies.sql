-- Clean up duplicate policies and standardize access
BEGIN;

-- Clean up business_relevant_industries
DROP POLICY IF EXISTS "Users can view all business industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable nested queries for business_relevant_industries" ON public.business_relevant_industries;
CREATE POLICY "Enable read access for all users"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

-- Clean up business_service_locations
DROP POLICY IF EXISTS "Enable read access for all" ON public.business_service_locations;
DROP POLICY IF EXISTS "Enable nested queries for business_service_locations" ON public.business_service_locations;
CREATE POLICY "Enable read access for all users"
    ON public.business_service_locations
    FOR SELECT
    TO authenticated
    USING (true);

-- Clean up industries
DROP POLICY IF EXISTS "Users can view all industries" ON public.industries;
DROP POLICY IF EXISTS "Enable nested queries for industries" ON public.industries;
CREATE POLICY "Enable read access for all users"
    ON public.industries
    FOR SELECT
    TO authenticated
    USING (true);

-- Clean up netzero_categories
DROP POLICY IF EXISTS "Users can view all categories" ON public.netzero_categories;
DROP POLICY IF EXISTS "Enable nested queries for netzero_categories" ON public.netzero_categories;
CREATE POLICY "Enable read access for all users"
    ON public.netzero_categories
    FOR SELECT
    TO authenticated
    USING (true);

-- Update locations table to be consistent with other tables
DROP POLICY IF EXISTS "Enable read access for all users" ON public.locations;
DROP POLICY IF EXISTS "Enable nested queries for locations" ON public.locations;
CREATE POLICY "Enable read access for all users"
    ON public.locations
    FOR SELECT
    TO authenticated
    USING (true);

-- Verify policies
SELECT
    schemaname,
    tablename,
    policyname,
    roles,
    cmd
FROM
    pg_policies
WHERE
    tablename IN (
        'business_relevant_industries',
        'business_service_locations',
        'industries',
        'locations',
        'netzero_categories'
    )
ORDER BY
    tablename,
    policyname;

COMMIT;
