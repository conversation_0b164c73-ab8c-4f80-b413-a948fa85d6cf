-- Check if current user has any categories
SELECT 
    'My User ID' as info,
    auth.uid() as user_id;

-- Check my categories
SELECT 
    'My Categories' as info,
    uc.id,
    uc.user_id,
    uc.category_id,
    nc.name as category_name
FROM public.user_categories uc
LEFT JOIN public.netzero_categories nc ON uc.category_id = nc.id
WHERE uc.user_id = auth.uid();

-- Check if I have any categories at all
SELECT 
    'My Category Count' as info,
    COUNT(*) as count
FROM public.user_categories 
WHERE user_id = auth.uid();
