-- migration: 20250603_fix_enrollment_profile_id.sql
-- Fix RLS and view to use profile_id instead of user_id for enrollments

-- 1. Update RLS policy for viewing own enrollments
DROP POLICY IF EXISTS "Users can view own enrollments" ON public.training_course_enrollments;
CREATE POLICY "Users can view own enrollments"
  ON public.training_course_enrollments
  FOR SELECT
  USING (auth.uid() = profile_id);

-- 2. Update RLS policy for course creators to view enrollments
DROP POLICY IF EXISTS "Creators can view course enrollments" ON public.training_course_enrollments;
CREATE POLICY "Creators can view course enrollments"
  ON public.training_course_enrollments
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT creator_id FROM training_courses WHERE id = course_id
    )
  );

-- 3. Update the enrollments view to use profile_id
DROP VIEW IF EXISTS public.training_course_enrollments_view;
CREATE OR REPLACE VIEW public.training_course_enrollments_view AS
SELECT
  e.id,
  e.course_id,
  e.profile_id,
  e.gdpr_consent, -- add this line
  e.status,
  e.request_message,
  e.created_at,
  e.updated_at,
  CONCAT(p.first_name, ' ', p.last_name) as user_name,
  p.avatar_url,
  p.title as user_title,
  p.organization as user_organization,
  p.email as user_email,
  t.title as course_title,
  t.organization_name as course_provider
FROM
  training_course_enrollments e
JOIN
  profiles p ON e.profile_id = p.id
JOIN
  training_courses t ON e.course_id = t.id;

-- 4. Grant select on the view
GRANT SELECT ON public.training_course_enrollments_view TO authenticated;
