-- Functions for managing comment replies and nested comments
-- This file adds support for comment replies in the social feed

-- Function to count direct replies to a comment
CREATE OR REPLACE FUNCTION public.get_comment_replies_count(comment_id_param UUID)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  reply_count INTEGER;
BEGIN
  SELECT COUNT(*)
  INTO reply_count
  FROM post_comments
  WHERE parent_comment_id = comment_id_param;
  
  RETURN reply_count;
END;
$$;

-- Function to get all replies for a post, organized by parent
CREATE OR REPLACE FUNCTION public.get_post_comment_tree(post_id_param UUID)
RETURNS TABLE (
  id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  user_id UUID,
  parent_comment_id UUID,
  likes_count INTEGER,
  depth INTEGER,
  path UUID[]
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  WITH RECURSIVE comment_tree AS (
    -- Base case: top-level comments (no parent)
    SELECT 
      pc.id,
      pc.content,
      pc.created_at,
      pc.user_id,
      pc.parent_comment_id,
      pc.likes_count,
      0 AS depth,
      ARRAY[pc.id] AS path
    FROM post_comments pc
    WHERE pc.post_id = post_id_param AND pc.parent_comment_id IS NULL
    
    UNION ALL
    
    -- Recursive case: comments that have parents
    SELECT
      c.id,
      c.content,
      c.created_at,
      c.user_id,
      c.parent_comment_id,
      c.likes_count,
      ct.depth + 1,
      ct.path || c.id
    FROM post_comments c
    JOIN comment_tree ct ON c.parent_comment_id = ct.id
    WHERE c.post_id = post_id_param
  )
  SELECT * FROM comment_tree
  ORDER BY path, created_at;
END;
$$;

-- Function to count all replies (recursive) for a comment
CREATE OR REPLACE FUNCTION public.get_total_comment_replies_count(comment_id_param UUID)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  total_count INTEGER;
BEGIN
  WITH RECURSIVE reply_tree AS (
    -- Base case: direct replies
    SELECT 
      id,
      parent_comment_id
    FROM post_comments
    WHERE parent_comment_id = comment_id_param
    
    UNION ALL
    
    -- Recursive case: replies to replies
    SELECT
      c.id,
      c.parent_comment_id
    FROM post_comments c
    JOIN reply_tree rt ON c.parent_comment_id = rt.id
  )
  SELECT COUNT(*) INTO total_count FROM reply_tree;
  
  RETURN total_count;
END;
$$;
