-- Test notification preferences system
-- Create test functions to verify notifications respect user preferences
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: CREATE TEST FUNCTIONS
-- ================================================================

-- Function to test if notification preferences are working
CREATE OR REPLACE FUNCTION public.test_notification_preferences()
RETURNS TABLE (
    test_name TEXT,
    status TEXT,
    details TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    test_user_id UUID;
    test_profile_id UUID;
    notification_count INTEGER;
BEGIN
    -- Test 1: Check if notification_preferences table structure is correct
    RETURN QUERY
    SELECT 
        'Table Structure'::TEXT,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'notification_preferences' 
                AND column_name = 'push_notifications'
                AND table_schema = 'public'
            ) THEN 'FAIL'::TEXT
            ELSE 'PASS'::TEXT
        END,
        'push_notifications column should be removed'::TEXT;

    -- Test 2: Check if all required columns exist
    RETURN QUERY
    SELECT 
        'Required Columns'::TEXT,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'notification_preferences' 
                AND column_name IN ('post_likes', 'post_comments', 'comment_replies', 'comment_likes', 'connection_requests', 'connection_accepted', 'event_signups', 'event_updates', 'system_notifications')
                AND table_schema = 'public'
                GROUP BY table_name
                HAVING COUNT(*) = 9
            ) THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'All notification preference columns should exist'::TEXT;

    -- Test 3: Check if notification triggers exist
    RETURN QUERY
    SELECT 
        'Notification Triggers'::TEXT,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM pg_trigger 
                WHERE tgname IN ('notify_post_like_trigger', 'notify_post_comment_trigger', 'notify_comment_like_trigger', 'notify_connection_request_trigger', 'notify_event_signup_trigger')
                GROUP BY 1
                HAVING COUNT(*) >= 5
            ) THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'All notification triggers should exist'::TEXT;

    -- Test 4: Check user_consent_settings structure
    RETURN QUERY
    SELECT 
        'Consent Settings Structure'::TEXT,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'user_consent_settings' 
                AND column_name IN ('share_email_with_event_creators', 'share_email_with_attendees')
                AND table_schema = 'public'
                GROUP BY table_name
                HAVING COUNT(*) = 2
            ) THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'Email consent columns should exist and be used'::TEXT;

    -- Test 5: Check if RLS policies exist
    RETURN QUERY
    SELECT 
        'RLS Policies'::TEXT,
        CASE 
            WHEN EXISTS (
                SELECT 1 FROM pg_policies 
                WHERE tablename IN ('notification_preferences', 'user_consent_settings')
                AND schemaname = 'public'
                GROUP BY 1
                HAVING COUNT(*) >= 2
            ) THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'RLS policies should exist for both tables'::TEXT;
END;
$$;

-- Function to simulate notification preference testing
CREATE OR REPLACE FUNCTION public.simulate_notification_test(
    p_test_profile_id UUID,
    p_notification_type TEXT,
    p_preference_enabled BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    should_notify BOOLEAN;
    preference_column TEXT;
BEGIN
    -- Map notification type to preference column
    preference_column := CASE p_notification_type
        WHEN 'post_like' THEN 'post_likes'
        WHEN 'post_comment' THEN 'post_comments'
        WHEN 'comment_reply' THEN 'comment_replies'
        WHEN 'comment_like' THEN 'comment_likes'
        WHEN 'connection_request' THEN 'connection_requests'
        WHEN 'connection_accepted' THEN 'connection_accepted'
        WHEN 'event_signup' THEN 'event_signups'
        WHEN 'event_update' THEN 'event_updates'
        WHEN 'system' THEN 'system_notifications'
        ELSE NULL
    END;
    
    IF preference_column IS NULL THEN
        RAISE EXCEPTION 'Invalid notification type: %', p_notification_type;
    END IF;
    
    -- Get the preference value using dynamic SQL
    EXECUTE format(
        'SELECT %I FROM public.notification_preferences WHERE profile_id = $1',
        preference_column
    ) INTO should_notify USING p_test_profile_id;
    
    -- Return whether the preference matches expected value
    RETURN (should_notify = p_preference_enabled);
END;
$$;

-- ================================================================
-- PART 2: CREATE DIAGNOSTIC VIEWS
-- ================================================================

-- View to show notification system health
CREATE OR REPLACE VIEW public.notification_system_health AS
SELECT
    'Notification Preferences' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN email_notifications = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN post_comments = false THEN 1 END) as post_comments_disabled_count,
    COUNT(CASE WHEN event_signups = false THEN 1 END) as event_signups_disabled_count
FROM public.notification_preferences
UNION ALL
SELECT
    'User Consent Settings' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN share_email_with_event_creators = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as post_comments_disabled_count,
    COUNT(CASE WHEN share_contact_details = true THEN 1 END) as event_signups_disabled_count
FROM public.user_consent_settings;

-- Grant access to the diagnostic views
GRANT SELECT ON public.notification_system_health TO authenticated;

-- ================================================================
-- PART 3: CREATE HELPER FUNCTION FOR PREFERENCE UPDATES
-- ================================================================

-- Function to safely update notification preferences with validation
CREATE OR REPLACE FUNCTION public.safe_update_notification_preference(
    p_preference_type TEXT,
    p_enabled BOOLEAN
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    valid_types TEXT[] := ARRAY[
        'email_notifications', 'post_likes', 'post_comments', 'comment_replies',
        'comment_likes', 'connection_requests', 'connection_accepted',
        'event_signups', 'event_updates', 'system_notifications'
    ];
    updated_rows INTEGER;
    result JSONB;
BEGIN
    -- Validate preference type
    IF NOT (p_preference_type = ANY(valid_types)) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid preference type: ' || p_preference_type,
            'valid_types', array_to_json(valid_types)
        );
    END IF;

    -- Ensure user has notification preferences record
    INSERT INTO public.notification_preferences (profile_id)
    VALUES (auth.uid())
    ON CONFLICT (profile_id) DO NOTHING;

    -- Update the preference using dynamic SQL
    EXECUTE format(
        'UPDATE public.notification_preferences SET %I = $1, updated_at = NOW() WHERE profile_id = $2',
        p_preference_type
    ) USING p_enabled, auth.uid();

    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    IF updated_rows > 0 THEN
        result := jsonb_build_object(
            'success', true,
            'preference_type', p_preference_type,
            'enabled', p_enabled,
            'updated_at', NOW()
        );
    ELSE
        result := jsonb_build_object(
            'success', false,
            'error', 'No rows updated - user may not exist or preference already set'
        );
    END IF;

    RETURN result;
END;
$$;

COMMIT;

-- Run the test function to verify everything is working
SELECT * FROM public.test_notification_preferences();

-- Show system health
SELECT 'Notification system health check:' as info;
SELECT * FROM public.notification_system_health;
