-- Check if tables are accessible via Supabase REST API

-- Check table permissions for anon and authenticated roles
SELECT 
    table_name,
    privilege_type,
    grantee
FROM information_schema.table_privileges 
WHERE table_schema = 'public'
AND table_name IN ('post_likes', 'post_comments', 'comment_likes', 'social_posts')
AND grantee IN ('anon', 'authenticated', 'service_role')
ORDER BY table_name, grantee, privilege_type;

-- Check if <PERSON><PERSON> is properly configured
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN ('post_likes', 'post_comments', 'comment_likes', 'social_posts')
ORDER BY tablename;

-- Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN ('post_likes', 'post_comments', 'comment_likes', 'social_posts')
ORDER BY tablename, policyname;

-- Verify tables actually exist and their structure
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public'
AND table_name = 'post_likes'
ORDER BY ordinal_position;
