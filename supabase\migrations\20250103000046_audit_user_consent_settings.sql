-- Audit user_consent_settings table and clean up unused columns
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: ANALYZE CURRENT USAGE OF CONSENT SETTINGS
-- ================================================================

-- Check if share_contact_details is actually used anywhere
-- Based on codebase analysis, this column appears to be unused

DO $$
DECLARE
    contact_details_used BOOLEAN := false;
    function_count INTEGER := 0;
    view_count INTEGER := 0;
    function_rec RECORD;
BEGIN
    -- Check if share_contact_details is referenced in any functions
    -- Use a safer approach to avoid conflicts with aggregate functions
    FOR function_rec IN
        SELECT p.proname, n.nspname
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname NOT LIKE '%agg%'  -- Exclude aggregate functions
    LOOP
        BEGIN
            -- Safely check function definition
            IF pg_get_functiondef(function_rec.proname::regproc) ILIKE '%share_contact_details%' THEN
                function_count := function_count + 1;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                -- Skip functions that cause errors (like aggregate functions)
                CONTINUE;
        END;
    END LOOP;

    -- Check if share_contact_details is referenced in any views
    SELECT COUNT(*) INTO view_count
    FROM pg_views
    WHERE schemaname = 'public'
    AND definition ILIKE '%share_contact_details%';

    IF function_count > 0 OR view_count > 0 THEN
        contact_details_used := true;
        RAISE NOTICE 'share_contact_details is used in % functions and % views', function_count, view_count;
    ELSE
        RAISE NOTICE 'share_contact_details appears to be unused - consider removing';
    END IF;
END $$;

-- ================================================================
-- PART 2: VERIFY EMAIL CONSENT COLUMNS ARE PROPERLY USED
-- ================================================================

-- Verify that share_email_with_event_creators is used (it should be)
DO $$
DECLARE
    creator_email_usage INTEGER := 0;
    view_count INTEGER := 0;
    function_rec RECORD;
BEGIN
    -- Check usage in views
    SELECT COUNT(*) INTO view_count
    FROM pg_views
    WHERE schemaname = 'public'
    AND definition ILIKE '%share_email_with_event_creators%';

    -- Check usage in functions (safely)
    FOR function_rec IN
        SELECT p.proname, n.nspname
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname NOT LIKE '%agg%'
    LOOP
        BEGIN
            IF pg_get_functiondef(function_rec.proname::regproc) ILIKE '%share_email_with_event_creators%' THEN
                creator_email_usage := creator_email_usage + 1;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                CONTINUE;
        END;
    END LOOP;

    creator_email_usage := creator_email_usage + view_count;

    IF creator_email_usage > 0 THEN
        RAISE NOTICE 'share_email_with_event_creators is properly used in % places', creator_email_usage;
    ELSE
        RAISE WARNING 'share_email_with_event_creators does not appear to be used!';
    END IF;
END $$;

-- Verify that share_email_with_attendees is used (it should be)
DO $$
DECLARE
    attendee_email_usage INTEGER := 0;
    view_count INTEGER := 0;
    function_rec RECORD;
BEGIN
    -- Check usage in views
    SELECT COUNT(*) INTO view_count
    FROM pg_views
    WHERE schemaname = 'public'
    AND definition ILIKE '%share_email_with_attendees%';

    -- Check usage in functions (safely)
    FOR function_rec IN
        SELECT p.proname, n.nspname
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname NOT LIKE '%agg%'
    LOOP
        BEGIN
            IF pg_get_functiondef(function_rec.proname::regproc) ILIKE '%share_email_with_attendees%' THEN
                attendee_email_usage := attendee_email_usage + 1;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                CONTINUE;
        END;
    END LOOP;

    attendee_email_usage := attendee_email_usage + view_count;

    IF attendee_email_usage > 0 THEN
        RAISE NOTICE 'share_email_with_attendees is properly used in % places', attendee_email_usage;
    ELSE
        RAISE WARNING 'share_email_with_attendees does not appear to be used!';
    END IF;
END $$;

-- ================================================================
-- PART 3: CREATE HELPER FUNCTIONS FOR CONSENT MANAGEMENT
-- ================================================================

-- Create or update the function to update user consent settings
CREATE OR REPLACE FUNCTION public.update_user_consent_settings(
    p_share_email_with_event_creators BOOLEAN,
    p_share_email_with_attendees BOOLEAN,
    p_share_contact_details BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    updated_rows INTEGER;
BEGIN
    -- Update or insert consent settings
    INSERT INTO public.user_consent_settings (
        user_id,
        share_email_with_event_creators,
        share_email_with_attendees,
        share_contact_details,
        consent_updated_at,
        updated_at
    )
    VALUES (
        auth.uid(),
        p_share_email_with_event_creators,
        p_share_email_with_attendees,
        p_share_contact_details,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET
        share_email_with_event_creators = p_share_email_with_event_creators,
        share_email_with_attendees = p_share_email_with_attendees,
        share_contact_details = p_share_contact_details,
        consent_updated_at = NOW(),
        updated_at = NOW();
    
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    RETURN updated_rows > 0;
END;
$$;

-- Drop existing function if it exists with different signature
DROP FUNCTION IF EXISTS public.get_user_consent_settings();

-- Create function to get user consent settings
CREATE OR REPLACE FUNCTION public.get_user_consent_settings()
RETURNS TABLE (
    share_email_with_event_creators BOOLEAN,
    share_email_with_attendees BOOLEAN,
    share_contact_details BOOLEAN,
    consent_updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        ucs.share_email_with_event_creators,
        ucs.share_email_with_attendees,
        ucs.share_contact_details,
        ucs.consent_updated_at
    FROM public.user_consent_settings ucs
    WHERE ucs.user_id = auth.uid();

    -- If no record exists, return defaults
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT false, false, false, NULL::TIMESTAMPTZ;
    END IF;
END;
$$;

-- ================================================================
-- PART 4: ENSURE PROPER RLS POLICIES ON USER_CONSENT_SETTINGS
-- ================================================================

-- Ensure RLS is enabled
ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies and recreate them
DROP POLICY IF EXISTS "Users can view their own consent settings" ON public.user_consent_settings;
DROP POLICY IF EXISTS "Users can insert their own consent settings" ON public.user_consent_settings;
DROP POLICY IF EXISTS "Users can update their own consent settings" ON public.user_consent_settings;
DROP POLICY IF EXISTS "Users can delete their own consent settings" ON public.user_consent_settings;

-- Create comprehensive RLS policies
CREATE POLICY "Users can view their own consent settings"
    ON public.user_consent_settings
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own consent settings"
    ON public.user_consent_settings
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own consent settings"
    ON public.user_consent_settings
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own consent settings"
    ON public.user_consent_settings
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- ================================================================
-- PART 5: CREATE SUMMARY VIEW FOR DEBUGGING
-- ================================================================

-- Create a view to help debug consent and notification settings
CREATE OR REPLACE VIEW public.user_privacy_settings_summary AS
SELECT 
    p.id as profile_id,
    p.first_name,
    p.last_name,
    p.email,
    -- Consent settings
    ucs.share_email_with_event_creators,
    ucs.share_email_with_attendees,
    ucs.share_contact_details,
    ucs.consent_updated_at,
    -- Notification preferences
    np.email_notifications,
    np.post_likes,
    np.post_comments,
    np.comment_replies,
    np.comment_likes,
    np.connection_requests,
    np.connection_accepted,
    np.event_signups,
    np.event_updates,
    np.system_notifications
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
LEFT JOIN public.notification_preferences np ON p.id = np.profile_id;

-- Grant access to authenticated users
-- Note: The view will automatically respect RLS policies of the underlying tables
GRANT SELECT ON public.user_privacy_settings_summary TO authenticated;

COMMIT;

-- Report completion
SELECT 'User consent settings audit completed - email consent columns are properly used' as status;
