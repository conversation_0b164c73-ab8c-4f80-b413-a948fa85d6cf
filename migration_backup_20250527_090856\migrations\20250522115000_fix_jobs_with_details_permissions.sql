-- This migration fixes the permissions for the jobs_with_details view

-- Step 0: Make sure all required tables exist
DO $$
BEGIN
    -- Check if the jobs table exists, if not create it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'jobs'
    ) THEN
        CREATE TABLE public.jobs (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            title TEXT NOT NULL,
            company_name TEXT NOT NULL,
            description TEXT NOT NULL,
            requirements TEXT,
            responsibilities TEXT,
            salary_range TEXT,
            job_type TEXT,
            date_posted TIMESTAMP WITH TIME ZONE DEFAULT now(),
            closing_date TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            status TEXT DEFAULT 'active'::text
        );
    END IF;

    -- Check if the job_locations table exists, if not create it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'job_locations'
    ) THEN
        CREATE TABLE public.job_locations (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE,
            location_id UUID REFERENCES public.locations(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            UNIQUE(job_id, location_id)
        );
    END IF;

    -- Check if the job_industries table exists, if not create it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'job_industries'
    ) THEN
        CREATE TABLE public.job_industries (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE,
            industry_id UUID REFERENCES public.industries(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            UNIQUE(job_id, industry_id)
        );
    END IF;

    -- Check if the job_categories table exists, if not create it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'job_categories'
    ) THEN
        CREATE TABLE public.job_categories (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            job_id UUID REFERENCES public.jobs(id) ON DELETE CASCADE,
            category_id UUID REFERENCES public.netzero_categories(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            UNIQUE(job_id, category_id)
        );
    END IF;
END $$;

-- Step 1: Make sure the view exists
DO $$
BEGIN
    -- Check if view exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.views 
        WHERE table_schema = 'public' AND table_name = 'jobs_with_details'
    ) THEN
        -- Create the view if it doesn't exist        CREATE OR REPLACE VIEW public.jobs_with_details AS
        SELECT 
            j.*,
            array_agg(DISTINCT l.location_id) as location_ids,
            array_agg(DISTINCT loc.name) as locations,
            array_agg(DISTINCT i.industry_id) as industry_ids,
            array_agg(DISTINCT ind.name) as industries,
            array_agg(DISTINCT c.category_id) as category_ids,
            array_agg(DISTINCT cat.name) as categories
        FROM public.jobs j
        LEFT JOIN public.job_locations l ON j.id = l.job_id
        LEFT JOIN public.locations loc ON l.location_id = loc.id
        LEFT JOIN public.job_industries i ON j.id = i.job_id
        LEFT JOIN public.industries ind ON i.industry_id = ind.id
        LEFT JOIN public.job_categories c ON j.id = c.job_id
        LEFT JOIN public.netzero_categories cat ON c.category_id = cat.id
        GROUP BY j.id;
    END IF;
END $$;

-- Step 2: Skip SECURITY INVOKER as it's not supported in this PostgreSQL version
-- Instead, we'll rely on permissions to control access

-- Step 3: Grant permissions to allow both authenticated and anon users to see the view
GRANT SELECT ON public.jobs_with_details TO anon;
GRANT SELECT ON public.jobs_with_details TO authenticated;
GRANT SELECT ON public.jobs_with_details TO service_role;

-- Grant permissions for job management to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.jobs TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.job_locations TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.job_industries TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.job_categories TO authenticated;

-- Step 4: Create RLS policies for underlying tables to ensure view works properly
-- These policies ensure the view can access all the necessary data

-- Make sure we have a policy allowing public read access to locations
DROP POLICY IF EXISTS "Anyone can read locations" ON public.locations;
CREATE POLICY "Anyone can read locations" ON public.locations
    FOR SELECT TO PUBLIC USING (true);

-- Make sure we have a policy allowing public read access to industries
DROP POLICY IF EXISTS "Anyone can read industries" ON public.industries;
CREATE POLICY "Anyone can read industries" ON public.industries
    FOR SELECT TO PUBLIC USING (true);

-- Make sure we have a policy allowing public read access to netzero_categories
DROP POLICY IF EXISTS "Anyone can read netzero_categories" ON public.netzero_categories;
CREATE POLICY "Anyone can read netzero_categories" ON public.netzero_categories
    FOR SELECT TO PUBLIC USING (true);

-- Make sure we have policies for jobs and related junction tables
DROP POLICY IF EXISTS "Anyone can read jobs" ON public.jobs;
CREATE POLICY "Anyone can read jobs" ON public.jobs
    FOR SELECT TO PUBLIC USING (true);

DROP POLICY IF EXISTS "Anyone can read job_locations" ON public.job_locations;
CREATE POLICY "Anyone can read job_locations" ON public.job_locations
    FOR SELECT TO PUBLIC USING (true);

DROP POLICY IF EXISTS "Anyone can read job_industries" ON public.job_industries;
CREATE POLICY "Anyone can read job_industries" ON public.job_industries
    FOR SELECT TO PUBLIC USING (true);

DROP POLICY IF EXISTS "Anyone can read job_categories" ON public.job_categories;
CREATE POLICY "Anyone can read job_categories" ON public.job_categories
    FOR SELECT TO PUBLIC USING (true);

-- Step 5: Fix any sequence permission issues
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- Step 6: Final permission check
DO $$
BEGIN
    RAISE NOTICE 'Jobs with details view should now be accessible via:';
    RAISE NOTICE '/rest/v1/jobs_with_details?select=*';
END $$;

-- Step 7: Enable row level security on all job-related tables
ALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_categories ENABLE ROW LEVEL SECURITY;

-- Step 8: Create policies to ensure job posters can manage their jobs
DROP POLICY IF EXISTS "Job posters can manage their own jobs" ON public.jobs;
CREATE POLICY "Job posters can manage their own jobs"
    ON public.jobs
    FOR ALL
    TO authenticated
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policies for job_locations
DROP POLICY IF EXISTS "Job owners can manage job locations" ON public.job_locations;
CREATE POLICY "Job owners can manage job locations"
    ON public.job_locations
    FOR ALL
    TO authenticated
    USING (job_id IN (SELECT id FROM public.jobs WHERE created_by = auth.uid()))
    WITH CHECK (job_id IN (SELECT id FROM public.jobs WHERE created_by = auth.uid()));

-- Policies for job_industries
DROP POLICY IF EXISTS "Job owners can manage job industries" ON public.job_industries;
CREATE POLICY "Job owners can manage job industries"
    ON public.job_industries
    FOR ALL
    TO authenticated
    USING (job_id IN (SELECT id FROM public.jobs WHERE created_by = auth.uid()))
    WITH CHECK (job_id IN (SELECT id FROM public.jobs WHERE created_by = auth.uid()));

-- Policies for job_categories
DROP POLICY IF EXISTS "Job owners can manage job categories" ON public.job_categories;
CREATE POLICY "Job owners can manage job categories"
    ON public.job_categories
    FOR ALL
    TO authenticated
    USING (job_id IN (SELECT id FROM public.jobs WHERE created_by = auth.uid()))
    WITH CHECK (job_id IN (SELECT id FROM public.jobs WHERE created_by = auth.uid()));

-- Make sure service_role has full access
DROP POLICY IF EXISTS "Service role has full access to jobs" ON public.jobs;
CREATE POLICY "Service role has full access to jobs"
    ON public.jobs
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);
