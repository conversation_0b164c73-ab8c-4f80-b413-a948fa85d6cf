-- Create the training course enrollments table if it doesn't exist
CREATE TABLE IF NOT EXISTS training_course_enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID REFERENCES training_courses(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'waitlisted')) DEFAULT 'pending',
    request_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    response_message TEXT,
    response_at TIMESTAMPTZ,
    UNIQUE(course_id, user_id)
);

-- Add RLS policies
ALTER TABLE training_course_enrollments ENABLE ROW LEVEL SECURITY;

-- Grant base table permissions
GRANT ALL ON training_course_enrollments TO authenticated;

-- Policy to allow users to view their own enrollments
CREATE POLICY "Users can view own enrollments"
    ON training_course_enrollments
    FOR SELECT
    USING (auth.uid() = user_id);

-- Policy to allow course creators to view enrollments for their courses
CREATE POLICY "Creators can view course enrollments"
    ON training_course_enrollments
    FOR SELECT
    USING (
        auth.uid() IN (
            SELECT creator_id 
            FROM training_courses 
            WHERE id = course_id
        )
    );

-- Policy to allow users to create enrollment requests
CREATE POLICY "Users can create enrollment requests"
    ON training_course_enrollments
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Policy to allow course creators to update enrollment statuses
CREATE POLICY "Creators can update enrollment statuses"
    ON training_course_enrollments
    FOR UPDATE
    USING (
        auth.uid() IN (
            SELECT creator_id 
            FROM training_courses 
            WHERE id = course_id
        )
    );

-- Create function to update timestamp on update
CREATE OR REPLACE FUNCTION update_enrollment_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update timestamp on update
CREATE TRIGGER set_enrollment_timestamp
BEFORE UPDATE ON training_course_enrollments
FOR EACH ROW
EXECUTE FUNCTION update_enrollment_timestamp();

-- Add indexes for performance
CREATE INDEX training_course_enrollments_course_id_idx ON training_course_enrollments(course_id);
CREATE INDEX training_course_enrollments_user_id_idx ON training_course_enrollments(user_id);
CREATE INDEX training_course_enrollments_status_idx ON training_course_enrollments(status);

-- Create a view for enrollments with user info
CREATE OR REPLACE VIEW training_course_enrollments_view AS
SELECT 
    e.id,
    e.course_id,
    e.user_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.avatar_url,
    p.title as user_title,
    p.organization as user_organization,
    p.email as user_email,
    t.title as course_title,
    t.organization_name as course_provider
FROM 
    training_course_enrollments e
JOIN 
    profiles p ON e.profile_id = p.id
JOIN 
    training_courses t ON e.course_id = t.id;

-- Grant permissions on the view
GRANT SELECT ON training_course_enrollments_view TO authenticated;

-- Add comment for the table and view
COMMENT ON TABLE training_course_enrollments IS 'Stores enrollment requests and statuses for training courses';
COMMENT ON VIEW training_course_enrollments_view IS 'Enhanced view of training course enrollments with user and course information';
