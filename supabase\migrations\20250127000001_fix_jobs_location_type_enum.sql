-- Fix jobs location_type enum values and add proper constraints
-- This migration fixes the mismatch between frontend and database enum values

-- First, check if we need to update existing data
-- Update any existing jobs with lowercase values to capitalized values
UPDATE jobs 
SET location_type = CASE 
    WHEN location_type = 'remote' THEN 'Remote'
    WHEN location_type = 'hybrid' THEN 'Hybrid' 
    WHEN location_type = 'on-site' THEN 'Office'
    ELSE location_type
END
WHERE location_type IN ('remote', 'hybrid', 'on-site');

-- Drop the existing enum if it exists and recreate with correct values
DO $$ 
BEGIN
    -- Drop the constraint if it exists
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'valid_office_location' 
               AND table_name = 'jobs') THEN
        ALTER TABLE jobs DROP CONSTRAINT valid_office_location;
    END IF;

    -- Check if the old enum type exists and drop it
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'location_type') THEN
        -- First change the column to text temporarily
        ALTER TABLE jobs ALTER COLUMN location_type TYPE TEXT;
        
        -- Drop the old enum
        DROP TYPE location_type;
    END IF;

    -- Create the new enum with correct values
    CREATE TYPE location_type AS ENUM ('Remote', 'Hybrid', 'Office');
    
    -- Change the column back to use the enum
    ALTER TABLE jobs ALTER COLUMN location_type TYPE location_type USING location_type::location_type;

    -- Add the proper constraint
    ALTER TABLE jobs ADD CONSTRAINT valid_office_location CHECK (
        (location_type = 'Remote' AND office_location IS NULL) OR
        (location_type IN ('Hybrid', 'Office') AND office_location IS NOT NULL)
    );

EXCEPTION WHEN OTHERS THEN
    -- If there's an error, try alternative approach
    RAISE NOTICE 'Error occurred, trying alternative approach: %', SQLERRM;
    
    -- Alternative: Just ensure the constraint exists with correct values
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'valid_office_location' 
                   AND table_name = 'jobs') THEN
        ALTER TABLE jobs ADD CONSTRAINT valid_office_location CHECK (
            (location_type = 'Remote' AND office_location IS NULL) OR
            (location_type IN ('Hybrid', 'Office') AND office_location IS NOT NULL)
        );
    END IF;
END $$;

-- Update user_job_preferences table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_job_preferences') THEN
        -- Update existing data
        UPDATE user_job_preferences 
        SET location_type = CASE 
            WHEN location_type = 'remote' THEN 'Remote'
            WHEN location_type = 'hybrid' THEN 'Hybrid' 
            WHEN location_type = 'on-site' THEN 'Office'
            ELSE location_type
        END
        WHERE location_type IN ('remote', 'hybrid', 'on-site');
    END IF;
END $$;

-- Also fix salary_bracket enum if needed
DO $$
BEGIN
    -- Check if we need to update salary brackets
    IF EXISTS (SELECT 1 FROM jobs WHERE salary_bracket LIKE '£%-%') THEN
        -- Update existing salary bracket data to match frontend expectations
        UPDATE jobs 
        SET salary_bracket = CASE 
            WHEN salary_bracket = '£0-£25k' THEN 'Under £25,000'
            WHEN salary_bracket = '£25k-£35k' THEN '£25,000 - £35,000'
            WHEN salary_bracket = '£35k-£50k' THEN '£35,000 - £45,000'
            WHEN salary_bracket = '£50k-£75k' THEN '£55,000 - £65,000'
            WHEN salary_bracket = '£75k-£100k' THEN '£75,000 - £85,000'
            WHEN salary_bracket = '£100k-£150k' THEN '£95,000 - £105,000'
            WHEN salary_bracket = '£150k+' THEN 'Above £105,000'
            ELSE salary_bracket
        END
        WHERE salary_bracket LIKE '£%-%' OR salary_bracket LIKE '£%+';
    END IF;
END $$;

-- Fix contract_type enum if needed
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM jobs WHERE contract_type IN ('permanent', 'contract', 'freelance')) THEN
        UPDATE jobs 
        SET contract_type = CASE 
            WHEN contract_type = 'permanent' THEN 'Permanent'
            WHEN contract_type = 'contract' THEN 'Contract'
            WHEN contract_type = 'freelance' THEN 'Freelance'
            ELSE contract_type
        END
        WHERE contract_type IN ('permanent', 'contract', 'freelance');
    END IF;
END $$;

-- Fix hours_type enum if needed  
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM jobs WHERE hours_type IN ('full-time', 'part-time', 'flexible')) THEN
        UPDATE jobs 
        SET hours_type = CASE 
            WHEN hours_type = 'full-time' THEN 'Full Time'
            WHEN hours_type = 'part-time' THEN 'Part Time'
            WHEN hours_type = 'flexible' THEN 'Job Share'
            ELSE hours_type
        END
        WHERE hours_type IN ('full-time', 'part-time', 'flexible');
    END IF;
END $$;

-- Fix job_function enum if needed
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM jobs WHERE job_function IN ('sustainability', 'engineering', 'finance', 'marketing', 'operations', 'management', 'consulting', 'research', 'policy', 'other')) THEN
        UPDATE jobs 
        SET job_function = CASE 
            WHEN job_function = 'sustainability' THEN 'Sustainability'
            WHEN job_function = 'engineering' THEN 'Engineering'
            WHEN job_function = 'finance' THEN 'Finance'
            WHEN job_function = 'marketing' THEN 'Marketing'
            WHEN job_function = 'operations' THEN 'Operations'
            WHEN job_function = 'management' THEN 'Management'
            WHEN job_function = 'consulting' THEN 'Consulting'
            WHEN job_function = 'research' THEN 'Research'
            WHEN job_function = 'policy' THEN 'Policy'
            WHEN job_function = 'other' THEN 'Other'
            ELSE job_function
        END
        WHERE job_function IN ('sustainability', 'engineering', 'finance', 'marketing', 'operations', 'management', 'consulting', 'research', 'policy', 'other');
    END IF;
END $$;
