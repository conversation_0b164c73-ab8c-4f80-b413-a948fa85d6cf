-- Final verification that account deletion setup is complete
-- Run this after applying all migrations and deploying edge functions

-- 1. Check that profiles table has foreign key to auth.users
SELECT 
    'profiles → auth.users constraint' as check_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.table_constraints tc
            WHERE tc.table_name = 'profiles' 
            AND tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
        ) THEN 'FOUND'
        ELSE 'MISSING'
    END as status;

-- 2. Check key cascade constraints
SELECT 
    'Key CASCADE constraints' as check_name,
    tc.table_name,
    tc.constraint_name,
    rc.delete_rule,
    CASE 
        WHEN rc.delete_rule = 'CASCADE' THEN '✅ GOOD'
        WHEN rc.delete_rule = 'NO ACTION' AND tc.table_name = 'profiles' AND kcu.column_name = 'location_id' THEN '✅ OK (location reference)'
        ELSE '⚠️ CHECK'
    END as assessment
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.referential_constraints rc ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name IN (
        'profiles', 'notification_preferences', 'user_consent_settings',
        'event_signups', 'training_course_enrollments', 'social_posts',
        'post_comments', 'post_likes', 'businesses', 'user_connections'
    )
ORDER BY tc.table_name;

-- 3. Check that cleanup functions exist
SELECT 
    'Cleanup functions' as check_name,
    proname as function_name,
    CASE 
        WHEN proname IN ('cleanup_user_data_safe', 'diagnose_user_deletion_issues') THEN '✅ FOUND'
        ELSE '⚠️ UNEXPECTED'
    END as status
FROM pg_proc 
WHERE proname IN ('cleanup_user_data_safe', 'diagnose_user_deletion_issues')
    AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- 4. Test data preview (safe - doesn't delete anything)
-- Pick a user with some data to test the preview function
WITH test_user AS (
    SELECT p.id 
    FROM profiles p 
    WHERE EXISTS (SELECT 1 FROM auth.users au WHERE au.id = p.id)
    LIMIT 1
)
SELECT 
    'Preview deletion test' as check_name,
    tu.id as test_user_id,
    'Run: SELECT * FROM public.preview_user_deletion(''' || tu.id || ''');' as next_step
FROM test_user tu;

-- 5. Summary
SELECT 
    'Setup Status' as check_name,
    'Account deletion setup appears complete' as status,
    'Ready for testing' as next_action;
