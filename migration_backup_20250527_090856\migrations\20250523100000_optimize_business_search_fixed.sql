-- Drop the view if it exists
DROP VIEW IF EXISTS business_directory_view;

-- Add text search extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Create immutable functions for text search
CREATE OR R<PERSON>LACE FUNCTION immutable_unaccent(text)
R<PERSON><PERSON>NS text AS $$
BEGIN
    RETURN unaccent($1);
END;
$$ LANGUAGE plpgsql IMMUTABLE PARALLEL SAFE;

-- Create indexes for text search on businesses table
CREATE INDEX IF NOT EXISTS idx_businesses_name_search ON businesses USING gin (lower(immutable_unaccent(name)) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_businesses_description_search ON businesses USING gin (lower(immutable_unaccent(description)) gin_trgm_ops) WHERE description IS NOT NULL;

-- Create a view that includes all necessary data for the business directory
CREATE OR REPLACE VIEW business_directory_view AS
SELECT 
    b.id,
    b.name,
    b.description,
    b.logo_url,
    b.website,
    b.email,
    b.tlr_level,
    b.hq_location_id,
    b.main_category_id,
    b.owner_id,
    b.created_at,
    b.updated_at,
    b.is_verified,
    b.verification_date,
    b.sponsorship_tier,
    b.sponsorship_start_date,
    b.sponsorship_end_date,
    l.name as hq_location_name,
    c.name as main_category_name,
    (
        SELECT array_agg(i.name)
        FROM business_relevant_industries bri
        JOIN industries i ON i.id = bri.industry_id
        WHERE bri.business_id = b.id
    ) as industry_names,
    (
        SELECT array_agg(loc.name)
        FROM business_service_locations bsl
        JOIN locations loc ON loc.id = bsl.location_id
        WHERE bsl.business_id = b.id
    ) as location_names
FROM businesses b
LEFT JOIN locations l ON l.id = b.hq_location_id
LEFT JOIN netzero_categories c ON c.id = b.main_category_id;

-- Grant necessary permissions
GRANT SELECT ON business_directory_view TO authenticated;
GRANT SELECT ON business_directory_view TO anon;
