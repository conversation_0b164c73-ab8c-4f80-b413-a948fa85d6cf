-- Add email synchronization after profile creation
-- This ensures email gets populated in profiles table
-- Date: 2025-01-03

BEGIN;

-- Create a function to sync email from auth.users to profiles
CREATE OR REPLACE FUNCTION public.sync_email_to_profile()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the profile with email after it's created
  UPDATE public.profiles 
  SET email = NEW.email,
      updated_at = NOW()
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to sync email after user creation
-- This runs after the handle_new_user trigger
DROP TRIGGER IF EXISTS sync_email_after_signup ON auth.users;
CREATE TRIGGER sync_email_after_signup
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_email_to_profile();

-- Set the trigger to run after the handle_new_user trigger
-- by giving it a name that comes alphabetically after "on_auth_user_created"
DROP TRIGGER IF EXISTS sync_email_after_signup ON auth.users;
CREATE TRIGGER z_sync_email_after_signup
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_email_to_profile();

COMMIT;

-- Verify the trigger was created
SELECT 'Email sync trigger created successfully' as status;
