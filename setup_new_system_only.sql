-- =====================================================
-- SETUP NEW SYSTEM ONLY (No Migration Needed)
-- =====================================================
-- Since no old user_connections table exists, just set up the new system

-- =====================================================
-- STEP 1: VERIFY NEW SYSTEM IS READY
-- =====================================================

-- Check that new tables exist
SELECT 
    '=== NEW SYSTEM TABLES CHECK ===' as phase,
    table_name,
    'EXISTS' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('social_connections', 'connection_activities', 'connection_notifications', 'social_feed_preferences')
ORDER BY table_name;

-- Check current data in new system
SELECT 
    '=== NEW SYSTEM DATA ===' as phase,
    'social_connections' as table_name,
    COUNT(*) as record_count
FROM social_connections
UNION ALL
SELECT 
    '=== NEW SYSTEM DATA ===',
    'connection_activities',
    COUNT(*)
FROM connection_activities
UNION ALL
SELECT 
    '=== NEW SYSTEM DATA ===',
    'connection_notifications',
    COUNT(*)
FROM connection_notifications;

-- =====================================================
-- STEP 2: INTEGRATE WITH NOTIFICATIONS SYSTEM
-- =====================================================

-- Check if notifications table exists and its structure
SELECT 
    '=== NOTIFICATIONS INTEGRATION CHECK ===' as phase,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'notifications'
ORDER BY ordinal_position;

-- Create connection notification integration function
CREATE OR REPLACE FUNCTION create_connection_notification(
    recipient_user_id UUID,
    actor_user_id UUID,
    notification_type TEXT,
    message_text TEXT,
    reference_id UUID DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if notifications table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
        -- Insert into existing notifications table
        INSERT INTO notifications (
            user_id,
            type,
            title,
            message,
            reference_id,
            created_at,
            read
        ) VALUES (
            recipient_user_id,
            notification_type,
            CASE 
                WHEN notification_type = 'connection_request' THEN 'New Connection Request'
                WHEN notification_type = 'connection_accepted' THEN 'Connection Accepted'
                ELSE 'Connection Update'
            END,
            message_text,
            reference_id,
            now(),
            false
        );
    ELSE
        -- Fallback to connection_notifications table
        INSERT INTO connection_notifications (
            recipient_id,
            actor_id,
            notification_type,
            message,
            reference_id,
            created_at,
            is_read
        ) VALUES (
            recipient_user_id,
            actor_user_id,
            notification_type,
            message_text,
            reference_id,
            now(),
            false
        );
    END IF;
END;
$$;

-- Update the connection functions to use the integrated notification system
CREATE OR REPLACE FUNCTION send_connection_request_integrated(recipient_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    existing_connection RECORD;
    new_connection_id UUID;
    requester_name TEXT;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Authentication required');
    END IF;
    
    IF current_user_id = recipient_user_id THEN
        RETURN jsonb_build_object('success', false, 'error', 'Cannot connect to yourself');
    END IF;
    
    -- Check for existing connection
    SELECT * INTO existing_connection
    FROM social_connections 
    WHERE (requester_id = current_user_id AND recipient_id = recipient_user_id)
       OR (requester_id = recipient_user_id AND recipient_id = current_user_id);
    
    IF FOUND THEN
        RETURN jsonb_build_object(
            'success', false, 
            'error', 'Connection already exists',
            'status', existing_connection.status
        );
    END IF;
    
    -- Get requester name for notification
    SELECT COALESCE(first_name || ' ' || last_name, 'Someone') INTO requester_name
    FROM profiles WHERE id = current_user_id;
    
    -- Create new connection request
    INSERT INTO social_connections (requester_id, recipient_id, status)
    VALUES (current_user_id, recipient_user_id, 'pending')
    RETURNING id INTO new_connection_id;
    
    -- Create notification using integrated system
    PERFORM create_connection_notification(
        recipient_user_id, 
        current_user_id, 
        'connection_request',
        requester_name || ' sent you a connection request',
        new_connection_id
    );
    
    RETURN jsonb_build_object(
        'success', true, 
        'connection_id', new_connection_id,
        'status', 'pending'
    );
END;
$$;

-- =====================================================
-- STEP 3: ENSURE CASCADE DELETES WORK PROPERLY
-- =====================================================

-- Check current foreign key constraints
SELECT 
    '=== FOREIGN KEY CONSTRAINTS CHECK ===' as phase,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name IN ('social_connections', 'connection_activities', 'connection_notifications', 'social_feed_preferences')
ORDER BY tc.table_name, kcu.column_name;

-- Ensure all foreign key constraints have CASCADE deletes
DO $$
BEGIN
    -- Fix social_connections constraints if needed
    IF EXISTS (
        SELECT 1 FROM information_schema.referential_constraints rc
        JOIN information_schema.table_constraints tc ON rc.constraint_name = tc.constraint_name
        WHERE tc.table_name = 'social_connections' 
        AND rc.delete_rule != 'CASCADE'
    ) THEN
        -- Fix the constraints
        ALTER TABLE social_connections DROP CONSTRAINT IF EXISTS social_connections_requester_id_fkey;
        ALTER TABLE social_connections DROP CONSTRAINT IF EXISTS social_connections_recipient_id_fkey;
        
        ALTER TABLE social_connections 
        ADD CONSTRAINT social_connections_requester_id_fkey 
        FOREIGN KEY (requester_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        
        ALTER TABLE social_connections 
        ADD CONSTRAINT social_connections_recipient_id_fkey 
        FOREIGN KEY (recipient_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed social_connections foreign key constraints';
    END IF;
END $$;

-- =====================================================
-- STEP 4: VERIFICATION & FINAL SETUP
-- =====================================================

-- Verify cascade delete configuration
SELECT 
    '=== CASCADE DELETE VERIFICATION ===' as phase,
    tc.table_name,
    kcu.column_name,
    rc.delete_rule,
    CASE 
        WHEN rc.delete_rule = 'CASCADE' THEN '✅ CORRECT'
        ELSE '❌ NEEDS FIX'
    END as status
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name IN ('social_connections', 'connection_activities', 'connection_notifications', 'social_feed_preferences')
ORDER BY tc.table_name, kcu.column_name;

-- Check that all RPC functions exist
SELECT 
    '=== RPC FUNCTIONS CHECK ===' as phase,
    routine_name,
    'EXISTS' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'send_connection_request',
    'accept_connection_request', 
    'decline_connection_request',
    'cancel_connection_request',
    'remove_connection',
    'get_user_connections',
    'get_connection_requests',
    'get_sent_requests',
    'get_suggested_connections_v2'
)
ORDER BY routine_name;

-- =====================================================
-- STEP 5: GRANT PERMISSIONS
-- =====================================================

-- Grant permissions for new integrated functions
GRANT EXECUTE ON FUNCTION create_connection_notification(UUID, UUID, TEXT, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION send_connection_request_integrated(UUID) TO authenticated;

-- =====================================================
-- STEP 6: FINAL SUCCESS MESSAGE
-- =====================================================

SELECT 
    '🎉 NEW SYSTEM SETUP COMPLETE!' as status,
    'Social Network V2 system is ready' as message,
    'No migration needed - starting fresh' as migration_status,
    'Cascade deletes configured' as safety_status,
    'Notifications integrated' as integration_status,
    'Ready to use at /connections' as usage_info;

-- Show system summary
SELECT 
    '=== SYSTEM SUMMARY ===' as summary,
    COUNT(*) as total_connections,
    COUNT(CASE WHEN status = 'connected' THEN 1 END) as connected,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined
FROM social_connections;
