-- UPDATES TO THE NOTIFICATION SYSTEM TO IMPROVE MESSAGE DISPLAY

-- First, recreate the view with proper column mappings
DROP VIEW IF EXISTS user_notifications_with_details;

-- Create the view with proper column mappings to match the application expectations
CREATE OR REPLACE VIEW user_notifications_with_details AS
SELECT 
    n.id,
    n.user_id,
    n.type,
    n.related_id AS related_entity_id,
    n.related_type AS related_entity_type,
    n.content AS message,
    -- Parse content as JSON in case it contains JSON data
    CASE 
      WHEN n.content ~ '^\\s*\\{.*\\}\\s*$' THEN n.content::jsonb
      ELSE NULL::jsonb
    END AS data,
    n.is_read,
    n.created_at,
    n.updated_at,
    NULL AS actor_first_name,
    NULL AS actor_last_name,
    NULL AS actor_avatar_url
FROM notifications n;

-- Grant permissions to the view
GRANT SELECT ON user_notifications_with_details TO authenticated;

-- Add some sample notifications for testing
-- Only run this if you need test data
/*
DO $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get a user ID for the test notifications
    SELECT id INTO v_user_id FROM auth.users LIMIT 1;
    
    IF v_user_id IS NOT NULL THEN
        -- Insert test notifications
        INSERT INTO notifications (user_id, type, content, related_id, related_type, is_read)
        VALUES
            (v_user_id, 'system', 'Welcome to the NetZero Platform!', NULL, NULL, false),
            (v_user_id, 'post_like', 'Someone liked your post', '00000000-0000-0000-0000-000000000001', 'posts', false),
            (v_user_id, 'event_signup', 'Someone signed up for your event', '00000000-0000-0000-0000-000000000002', 'events', false),
            (v_user_id, 'connection_request', 'You received a new connection request', '00000000-0000-0000-0000-000000000003', 'profiles', false);
    END IF;
END $$;
*/
