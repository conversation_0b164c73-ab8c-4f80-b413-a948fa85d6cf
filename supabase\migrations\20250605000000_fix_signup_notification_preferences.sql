-- Fix signup notification preferences error
-- Remove push_notifications column and update handle_new_user function
-- Date: 2025-06-05

BEGIN;

-- Step 1: Remove push_notifications column if it exists
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification_preferences' 
        AND column_name = 'push_notifications'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notification_preferences DROP COLUMN push_notifications;
        RAISE NOTICE 'Removed push_notifications column from notification_preferences';
    ELSE
        RAISE NOTICE 'push_notifications column does not exist, skipping removal';
    END IF;
END $$;

-- Step 2: Ensure notification_preferences table has the correct structure
-- Check current structure and log it
DO $$
DECLARE
    col_count INTEGER;
    col_names TEXT;
BEGIN
    -- Count columns in notification_preferences
    SELECT COUNT(*), string_agg(column_name, ', ' ORDER BY ordinal_position)
    INTO col_count, col_names
    FROM information_schema.columns 
    WHERE table_name = 'notification_preferences' 
    AND table_schema = 'public';
    
    RAISE NOTICE 'notification_preferences table has % columns: %', col_count, col_names;
END $$;

-- Step 3: Update handle_new_user function to work with current table structure
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
BEGIN
  -- Insert a new profile record for the new user
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    email, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  )
  RETURNING id INTO profile_id;

  -- Create default user_consent_settings for the new user
  INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    email_notifications,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    true,  -- profile_visibility - default to public
    true,  -- email_notifications - default to enabled
    false, -- newsletter_subscription - default to disabled
    true,  -- show_businesses - default to visible
    true,  -- show_events - default to visible
    true,  -- show_connections - default to visible
    false, -- share_email_with_event_creators - default to private
    false, -- share_email_with_attendees - default to private
    false, -- share_contact_details - default to private
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (user_id) DO NOTHING;

  -- Create notification preferences (without push_notifications column)
  -- Only insert if the table exists and has the expected structure
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'notification_preferences'
  ) THEN
    -- Check if table has individual boolean columns structure (not the type/email_enabled structure)
    IF EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'notification_preferences' 
      AND column_name = 'post_likes'
    ) THEN
      -- Insert with individual boolean columns (excluding push_notifications)
      INSERT INTO public.notification_preferences (
        profile_id,
        email_notifications,
        post_likes,
        post_comments,
        comment_replies,
        comment_likes,
        connection_requests,
        connection_accepted,
        event_signups,
        event_updates,
        system_notifications,
        created_at,
        updated_at
      )
      VALUES (
        profile_id,
        true, -- email_notifications
        true, -- post_likes
        true, -- post_comments
        true, -- comment_replies
        true, -- comment_likes
        true, -- connection_requests
        true, -- connection_accepted
        true, -- event_signups
        true, -- event_updates
        true, -- system_notifications
        NOW(),
        NOW()
      )
      ON CONFLICT (profile_id) DO NOTHING;
    END IF;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the signup
    RAISE WARNING 'Error in handle_new_user function: %', SQLERRM;
    -- Still return NEW so the user signup doesn't fail
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment to reflect the current functionality
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates a profile record, user consent settings, and notification preferences when a new user signs up (without push_notifications)';

COMMIT;
