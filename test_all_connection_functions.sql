-- Test all connection RPC functions to see which ones work
-- Run this in Supabase SQL Editor

-- 1. Test request_connection (should return auth error)
SELECT 
    'request_connection test' as function_name,
    public.request_connection(
        '00000000-0000-0000-0000-000000000001'::uuid,
        '00000000-0000-0000-0000-000000000002'::uuid
    ) as result;

-- 2. Test respond_to_connection_request (should return auth error)
SELECT 
    'respond_to_connection_request test' as function_name,
    public.respond_to_connection_request(
        '00000000-0000-0000-0000-000000000000'::uuid,
        'accepted'
    ) as result;

-- 3. Test cancel_connection_request (should return auth error)
SELECT 
    'cancel_connection_request test' as function_name,
    public.cancel_connection_request('00000000-0000-0000-0000-000000000000'::uuid) as result;

-- 4. Test remove_connection (should return auth error)
SELECT 
    'remove_connection test' as function_name,
    public.remove_connection('00000000-0000-0000-0000-000000000000'::uuid) as result;

-- 5. Check if all functions exist
SELECT 
    'Function existence check' as section,
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_name IN (
    'request_connection',
    'respond_to_connection_request',
    'cancel_connection_request',
    'remove_connection'
)
AND routine_schema = 'public'
ORDER BY routine_name;
