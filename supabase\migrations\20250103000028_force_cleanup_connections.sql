-- Force cleanup of all connection-related functions and recreate them
-- This will aggressively remove any old functions that reference mv_active_connections

BEGIN;

-- First, let's see what functions exist
SELECT 
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name LIKE '%connection%'
AND routine_schema = 'public';

-- Drop ALL possible variations of connection functions
DROP FUNCTION IF EXISTS public.request_connection(UUID, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.respond_to_connection_request(UUID, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.cancel_connection_request(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.remove_connection(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.send_connection_request(UUID, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.accept_connection_request(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.reject_connection_request(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.delete_connection(UUID) CASCADE;

-- Drop any functions that might reference materialized views
DROP FUNCTION IF EXISTS public.refresh_connection_views() CASCADE;
DROP FUNCTION IF EXISTS public.get_user_connections(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_pending_requests(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_sent_requests(UUID) CASCADE;

-- Drop any remaining materialized views if they exist
DROP MATERIALIZED VIEW IF EXISTS public.mv_active_connections CASCADE;
DROP MATERIALIZED VIEW IF EXISTS public.mv_pending_connection_requests CASCADE;
DROP MATERIALIZED VIEW IF EXISTS public.mv_sent_connection_requests CASCADE;

-- Now recreate the functions with clean implementations
CREATE OR REPLACE FUNCTION public.request_connection(
    user_id_param UUID,
    connection_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_connection_id UUID;
    current_user_id UUID;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Verify the requesting user is authenticated
    IF current_user_id != user_id_param THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Unauthorized: You can only request connections for yourself'
        );
    END IF;
    
    -- Check if connection already exists
    IF EXISTS (
        SELECT 1 FROM public.user_connections 
        WHERE (user_id = user_id_param AND connection_id = connection_id_param)
           OR (user_id = connection_id_param AND connection_id = user_id_param)
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection already exists or request already sent'
        );
    END IF;
    
    -- Create the connection request
    INSERT INTO public.user_connections (user_id, connection_id, status)
    VALUES (user_id_param, connection_id_param, 'pending')
    RETURNING id INTO new_connection_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'id', new_connection_id,
            'user_id', user_id_param,
            'connection_id', connection_id_param,
            'status', 'pending'
        )
    );
END;
$$;

CREATE OR REPLACE FUNCTION public.respond_to_connection_request(
    request_id_param UUID,
    status_param TEXT
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    connection_record RECORD;
    current_user_id UUID;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Validate status
    IF status_param NOT IN ('accepted', 'rejected') THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid status. Must be accepted or rejected'
        );
    END IF;
    
    -- Get the connection request and verify user is the recipient
    SELECT * INTO connection_record
    FROM public.user_connections
    WHERE id = request_id_param 
      AND connection_id = current_user_id 
      AND status = 'pending';
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found or you are not authorized to respond'
        );
    END IF;
    
    -- Update the connection status
    UPDATE public.user_connections
    SET status = status_param, updated_at = now()
    WHERE id = request_id_param;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'id', request_id_param,
            'status', status_param
        )
    );
END;
$$;

CREATE OR REPLACE FUNCTION public.cancel_connection_request(
    request_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Delete the connection request (only if user is the sender and status is pending)
    DELETE FROM public.user_connections
    WHERE id = request_id_param 
      AND user_id = current_user_id 
      AND status = 'pending';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found or cannot be cancelled'
        );
    END IF;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object('id', request_id_param)
    );
END;
$$;

CREATE OR REPLACE FUNCTION public.remove_connection(
    connection_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Delete the connection (user must be involved in the connection)
    DELETE FROM public.user_connections
    WHERE id = connection_id_param 
      AND (user_id = current_user_id OR connection_id = current_user_id);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection not found or you are not authorized to remove it'
        );
    END IF;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object('id', connection_id_param)
    );
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.request_connection TO authenticated;
GRANT EXECUTE ON FUNCTION public.respond_to_connection_request TO authenticated;
GRANT EXECUTE ON FUNCTION public.cancel_connection_request TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_connection TO authenticated;

COMMIT;

-- Verify the functions were created and show their definitions
SELECT 
    routine_name,
    routine_type,
    security_type,
    LEFT(routine_definition, 100) as definition_start
FROM information_schema.routines 
WHERE routine_name IN (
    'request_connection',
    'respond_to_connection_request',
    'cancel_connection_request',
    'remove_connection'
)
AND routine_schema = 'public'
ORDER BY routine_name;
