-- Update get_post_comment_tree function to include required fields
CREATE OR R<PERSON>LACE FUNCTION public.get_post_comment_tree(post_id_param UUID)
RETURNS TABLE (
  id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_id UUID,
  parent_comment_id UUID,
  likes_count INTEGER,
  is_edited BOOLEAN,
  depth INTEGER,
  path UUID[]
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  WITH RECURSIVE comment_tree AS (
    -- Base case: top-level comments (no parent)
    SELECT 
      pc.id,
      pc.content,
      pc.created_at,
      pc.updated_at,
      pc.user_id,
      pc.parent_comment_id,
      pc.likes_count,
      pc.updated_at > pc.created_at as is_edited,
      0 AS depth,
      ARRAY[pc.id] AS path
    FROM post_comments pc
    WHERE pc.post_id = post_id_param AND pc.parent_comment_id IS NULL
    
    UNION ALL
    
    -- Recursive case: comments that have parents
    SELECT
      c.id,
      c.content,
      c.created_at,
      c.updated_at,
      c.user_id,
      c.parent_comment_id,
      c.likes_count,
      c.updated_at > c.created_at as is_edited,
      ct.depth + 1,
      ct.path || c.id
    FROM post_comments c
    JOIN comment_tree ct ON c.parent_comment_id = ct.id
    WHERE c.post_id = post_id_param
  )
  SELECT * FROM comment_tree
  ORDER BY path, created_at;
END;
$$;
