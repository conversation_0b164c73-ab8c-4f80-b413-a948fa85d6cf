
-- Add email column to profiles if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'profiles' 
        AND column_name = 'email'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN email TEXT;
        
        -- Copy existing emails from auth.users to profiles
        UPDATE public.profiles p
        SET email = au.email
        FROM auth.users au
        WHERE p.id = au.id;
    END IF;
END $$;

-- Ensure RLS is enabled on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Update the view to use profile emails instead of auth.users
DROP VIEW IF EXISTS public.event_signups_with_users;

CREATE VIEW public.event_signups_with_users AS
  SELECT 
    es.*,
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    p.avatar_url,
    p.title,
    p.organization
  FROM 
    public.event_signups es
  LEFT JOIN 
    public.profiles p ON es.user_id = p.id;

-- Create policies to protect email access
CREATE POLICY "Users can see their own email"
    ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

-- Update the function to use profile emails instead of auth.users
CREATE OR REPLACE FUNCTION get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT
) 
SECURITY DEFINER
AS $$
BEGIN
    -- Only allow event creators to access emails
    IF EXISTS (
        SELECT 1 FROM events 
        WHERE id = event_id 
        AND created_by = auth.uid()
    ) THEN
        RETURN QUERY
        SELECT 
            es.user_id,
            p.email
        FROM 
            event_signups es
        JOIN 
            public.profiles p ON es.user_id = p.id
        WHERE 
            es.event_id = event_id
            AND es.gdpr_consent = true;  -- Only include users who have given GDPR consent
    ELSE
        RAISE EXCEPTION 'Access denied: You must be the event creator to view emails';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Add a trigger to sync email changes from auth.users to profiles
CREATE OR REPLACE FUNCTION sync_auth_email_to_profile()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.profiles
    SET email = NEW.email,
        updated_at = now()
    WHERE id = NEW.id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_auth_email_updated ON auth.users;
CREATE TRIGGER on_auth_email_updated
    AFTER UPDATE OF email ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION sync_auth_email_to_profile();

-- Grant appropriate permissions
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.event_signups_with_users TO service_role;
GRANT EXECUTE ON FUNCTION get_event_attendee_emails TO authenticated;
