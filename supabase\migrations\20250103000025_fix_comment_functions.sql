-- Fix comment-related functions that might be referencing "posts" instead of "social_posts"

BEGIN;

-- Drop all comment-related triggers and functions that might be problematic
DROP TRIGGER IF EXISTS notify_post_comment_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS notify_comment_reply_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS notify_comment_like_trigger ON public.comment_likes;

DROP FUNCTION IF EXISTS public.notify_post_comment() CASCADE;
DROP FUNCTION IF EXISTS public.notify_comment_reply() CASCADE;
DROP FUNCTION IF EXISTS public.notify_comment_like() CASCADE;

-- Recreate notify_post_comment function with correct table reference
CREATE OR REPLACE FUNCTION public.notify_post_comment()
RETURNS TRIGGER AS $$
DECLARE
    post_owner_id UUID;
BEGIN
    -- Get the post owner from social_posts table (not "posts")
    SELECT user_id INTO post_owner_id
    FROM public.social_posts
    WHERE id = NEW.post_id;
    
    -- Don't notify if user comments on their own post
    IF post_owner_id = NEW.user_id THEN
        RETURN NEW;
    END IF;
    
    -- Only create notification if notifications table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications' AND table_schema = 'public') THEN
        INSERT INTO public.notifications (user_id, type, content, reference_id, actor_id)
        VALUES (
            post_owner_id,
            'post_comment',
            'commented on your post',
            NEW.post_id,
            NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate notify_comment_reply function
CREATE OR REPLACE FUNCTION public.notify_comment_reply()
RETURNS TRIGGER AS $$
DECLARE
    parent_comment_owner_id UUID;
BEGIN
    -- Only process if this is a reply (has parent_comment_id)
    IF NEW.parent_comment_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Get the parent comment owner
    SELECT user_id INTO parent_comment_owner_id
    FROM public.post_comments
    WHERE id = NEW.parent_comment_id;
    
    -- Don't notify if user replies to their own comment
    IF parent_comment_owner_id = NEW.user_id THEN
        RETURN NEW;
    END IF;
    
    -- Only create notification if notifications table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications' AND table_schema = 'public') THEN
        INSERT INTO public.notifications (user_id, type, content, reference_id, actor_id)
        VALUES (
            parent_comment_owner_id,
            'comment_reply',
            'replied to your comment',
            NEW.id,
            NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate notify_comment_like function
CREATE OR REPLACE FUNCTION public.notify_comment_like()
RETURNS TRIGGER AS $$
DECLARE
    comment_owner_id UUID;
BEGIN
    -- Get the comment owner
    SELECT user_id INTO comment_owner_id
    FROM public.post_comments
    WHERE id = NEW.comment_id;
    
    -- Don't notify if user likes their own comment
    IF comment_owner_id = NEW.user_id THEN
        RETURN NEW;
    END IF;
    
    -- Only create notification if notifications table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications' AND table_schema = 'public') THEN
        INSERT INTO public.notifications (user_id, type, content, reference_id, actor_id)
        VALUES (
            comment_owner_id,
            'comment_like',
            'liked your comment',
            NEW.comment_id,
            NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate triggers
CREATE TRIGGER notify_post_comment_trigger
    AFTER INSERT ON public.post_comments
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_post_comment();

CREATE TRIGGER notify_comment_reply_trigger
    AFTER INSERT ON public.post_comments
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_comment_reply();

CREATE TRIGGER notify_comment_like_trigger
    AFTER INSERT ON public.comment_likes
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_comment_like();

-- Create/Replace social_engagement_stats view for platform-wide social media statistics
-- This view provides the aggregated counts that the SocialFeed component expects
DROP VIEW IF EXISTS public.social_engagement_stats CASCADE;

CREATE OR REPLACE VIEW public.social_engagement_stats
WITH (security_invoker = on) AS
SELECT
    (SELECT COUNT(*) FROM public.social_posts) as post_count,
    (SELECT COUNT(*) FROM public.post_comments) as comment_count,
    (SELECT COUNT(*) FROM public.post_likes) as like_count,
    (SELECT COUNT(*) FROM public.comment_likes) as comment_like_count;

-- Grant access to all users (including anonymous) since social feed can be viewed without login
GRANT SELECT ON public.social_engagement_stats TO anon, authenticated, service_role;

COMMIT;

-- Test that functions were created
SELECT
    routine_name,
    routine_type
FROM information_schema.routines
WHERE routine_name IN ('notify_post_comment', 'notify_comment_reply', 'notify_comment_like')
AND routine_schema = 'public'
ORDER BY routine_name;

-- Test that the social_engagement_stats view works
SELECT * FROM public.social_engagement_stats;
