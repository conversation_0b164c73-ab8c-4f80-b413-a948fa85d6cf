-- Drop and recreate the view with proper RLS
DROP VIEW IF EXISTS training_course_enrollments_view;

-- First create the view
CREATE VIEW training_course_enrollments_view AS
SELECT 
    e.id,
    e.course_id,
    e.user_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.avatar_url,
    p.title as user_title,
    p.organization as user_organization,
    p.email as user_email,
    t.title as course_title,
    t.organization_name as course_provider
FROM 
    training_course_enrollments e
JOIN 
    profiles p ON e.profile_id = p.id
JOIN 
    training_courses t ON e.course_id = t.id;

-- Enable RLS on the view
ALTER VIEW training_course_enrollments_view SET (security_invoker = on);

-- Grant access to authenticated users
GRANT SELECT ON training_course_enrollments_view TO authenticated;

-- Create RLS policy for the view
CREATE POLICY "Users can view their own enrollments"
    ON training_course_enrollments_view
    FOR SELECT
    USING (
        -- User can see their own enrollments
        auth.uid() = user_id 
        OR 
        -- Course creators can see enrollments for their courses
        auth.uid() IN (
            SELECT creator_id 
            FROM training_courses 
            WHERE id = course_id
        )
    );
