# Migration Plan: Replace Old Connections with New Social Network V2

## 🎯 **Migration Strategy**

Since the old system isn't working properly and the new system is fast and functional, let's implement a clean migration:

### **Phase 1: Database Migration** ✅ *ALREADY DONE*
- ✅ New tables created (`social_connections`, `connection_activities`, etc.)
- ✅ Performance indexes in place
- ✅ RPC functions working
- ✅ Profile visibility sync system active

### **Phase 2: Frontend Migration** 🔄 *IN PROGRESS*
- 🔄 Replace old components with new ones
- 🔄 Update routes and navigation
- 🔄 Remove old files and dependencies

### **Phase 3: Data Migration** 📋 *OPTIONAL*
- 📋 Migrate existing connections from `user_connections` to `social_connections`
- 📋 Preserve user relationships

### **Phase 4: Cleanup** 🧹 *FINAL STEP*
- 🧹 Remove old database tables
- 🧹 Remove old components and hooks
- 🧹 Clean up unused imports

## 📋 **Detailed Migration Steps**

### **Step 1: Replace Components**
1. Replace `ConnectionsDrawer` with `SocialNetworkV2`
2. Update `ConnectionsPage` to use new component
3. Replace `useUserConnections` with `useSocialNetworkV2`
4. Update any components that use connection data

### **Step 2: Update Routes**
1. Keep `/connections` route but use new component
2. Remove `/social-network-v2` test route
3. Update navigation links

### **Step 3: Data Migration (Optional)**
1. Create script to migrate existing `user_connections` data
2. Map old statuses to new statuses
3. Preserve timestamps and relationships

### **Step 4: Remove Old System**
1. Drop old tables: `user_connections`, old RPC functions
2. Remove old components and hooks
3. Clean up imports and dependencies

## 🛠 **Implementation Order**

### **Immediate (Today)**
1. ✅ Replace ConnectionsPage component
2. ✅ Update routes to use new system
3. ✅ Test functionality

### **Next (This Week)**
1. 📋 Migrate existing connection data (if any)
2. 🧹 Remove old components
3. 🧹 Clean up database

### **Final (When Ready)**
1. 🧹 Drop old tables
2. 🧹 Remove test routes
3. 🧹 Production deployment

## 📁 **Files to Modify**

### **Replace These Files:**
- `src/components/social/ConnectionsDrawer.tsx` → Use `SocialNetworkV2.tsx`
- `src/hooks/social/useUserConnections.tsx` → Use `useSocialNetworkV2.ts`
- `src/pages/ConnectionsPage.tsx` → Update to use new component

### **Remove These Files (Later):**
- `src/hooks/social/useUserConnections.ts` (old version)
- `src/lib/social-utils.ts` (old filtering logic)
- Old RPC functions and database objects

### **Keep These Files:**
- `src/components/social/SocialNetworkV2.tsx`
- `src/hooks/social/useSocialNetworkV2.ts`
- `social_network_v2_schema.sql`
- `social_network_v2_functions.sql`

## 🎨 **Production-Ready Changes Needed**

### **Remove Test/Demo Content:**
1. Remove instructional text and setup guides
2. Remove "NEW" badges and test messaging
3. Simplify the interface for production use
4. Remove performance comparison cards

### **Polish for Production:**
1. Add proper error boundaries
2. Add loading skeletons
3. Improve mobile responsiveness
4. Add proper accessibility attributes

## 🔄 **Data Migration Script**

```sql
-- Migrate existing user_connections to social_connections
INSERT INTO social_connections (requester_id, recipient_id, status, created_at, updated_at, connected_at)
SELECT 
    user_id as requester_id,
    connection_id as recipient_id,
    CASE 
        WHEN status = 'accepted' THEN 'connected'
        WHEN status = 'pending' THEN 'pending'
        WHEN status = 'rejected' THEN 'declined'
        WHEN status = 'blocked' THEN 'blocked'
        ELSE 'pending'
    END as status,
    created_at,
    updated_at,
    CASE WHEN status = 'accepted' THEN updated_at ELSE NULL END as connected_at
FROM user_connections
WHERE NOT EXISTS (
    SELECT 1 FROM social_connections sc 
    WHERE sc.requester_id = user_connections.user_id 
    AND sc.recipient_id = user_connections.connection_id
);
```

## ✅ **Success Criteria**

### **Migration Complete When:**
- ✅ `/connections` route uses new system
- ✅ All connection functionality works
- ✅ Performance is fast (< 50ms for suggestions)
- ✅ UI is clean and production-ready
- ✅ No old system references remain

### **User Experience Goals:**
- ✅ Familiar `/connections` URL still works
- ✅ Faster loading and interactions
- ✅ Better error handling
- ✅ Modern, clean interface

## 🚨 **Rollback Plan**

If issues arise:
1. Keep old database tables until migration is proven
2. Can quickly revert routes to old components
3. New system is separate, so no data loss risk

## 🎯 **Next Immediate Actions**

1. **Replace ConnectionsPage** with production-ready version
2. **Update routes** to use new system
3. **Test thoroughly** with real user accounts
4. **Polish UI** for production use

Ready to start the migration? Let's begin with replacing the ConnectionsPage component!
