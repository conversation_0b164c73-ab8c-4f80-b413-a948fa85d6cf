-- Update configuration for image deletion
-- Drop and recreate the app_settings table to ensure clean state
DROP TABLE IF EXISTS app_settings;
CREATE TABLE app_settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Insert base settings
INSERT INTO app_settings (key, value)
VALUES 
  ('edge_function_base_url', 'YOUR_PROJECT_REF.functions.supabase.co'),
  ('service_role_key', 'YOUR_SERVICE_ROLE_KEY');

-- Create an updated version of the function that includes proper error handling and logging
CREATE OR REPLACE FUNCTION handle_deleted_post_media()
RETURNS TRIGGER AS $$
DECLARE
  v_edge_function_url TEXT;
  v_service_role_key TEXT;
  v_response_status INTEGER;
  v_response_body JSONB;
BEGIN
  -- Only proceed if we have an image to delete
  IF OLD.media_type = 'image' AND OLD.media_url IS NOT NULL THEN
    -- Get configuration values
    SELECT value INTO v_edge_function_url FROM app_settings WHERE key = 'edge_function_base_url';
    SELECT value INTO v_service_role_key FROM app_settings WHERE key = 'service_role_key';
    
    IF v_edge_function_url IS NULL OR v_service_role_key IS NULL THEN
      RAISE WARNING 'Missing required configuration in app_settings table';
      RETURN OLD;
    END IF;

    -- Call edge function to delete the image
    SELECT
      status,
      CASE 
        WHEN status < 300 THEN content::jsonb
        ELSE NULL
      END
    INTO v_response_status, v_response_body
    FROM net.http_post(
      url := 'https://' || v_edge_function_url || '/delete-cloudflare-image',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || v_service_role_key
      ),
      body := jsonb_build_object(
        'record', jsonb_build_object(
          'media_url', OLD.media_url,
          'media_type', OLD.media_type
        )
      )
    );

    -- Log the response
    IF v_response_status >= 300 THEN
      RAISE WARNING 'Failed to delete image. Status: %, URL: %', 
        v_response_status, OLD.media_url;
    ELSE
      RAISE NOTICE 'Successfully deleted image. URL: %', OLD.media_url;
    END IF;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;
