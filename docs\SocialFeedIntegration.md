# Social Feed Integration Guide

This guide provides instructions on integrating the components and hooks to build a fully functioning social feed for the NetZero Platform.

## Key Components

### Page Component

- `SocialFeed.tsx`: The main page component that orchestrates all components and hooks

### Core Components

- `SocialFeedPost.tsx`: Displays a single post with all its functionality
- `PostCreator.tsx`: Allows users to create new posts
- `PostComment.tsx`: Displays and manages comments on posts
- `CategoryFilter.tsx`: Filters posts by category

### Supporting Components

- `PostCategoriesDisplay.tsx`: Shows categories on a post
- `ConnectionsDrawer.tsx`: Manages user connections
- `TrendingSidebar.tsx`: Shows trending topics and categories

## Key Hooks

- `useSocialFeed.tsx`: Main hook for handling posts and interactions
- `usePostComments.tsx`: Manages comment functionality
- `usePostLikes.tsx`: Handles post likes
- `useUserConnections.tsx`: Manages user connections

## Integration Steps

### 1. Clean Up Redundant Files

We identified the following redundant files that should be removed:

```powershell
# Remove duplicate component
Remove-Item -Path "src\components\social\Post.tsx"

# Remove duplicate hooks (keeping .tsx versions)
Remove-Item -Path "src\hooks\social\usePostComments.ts"
Remove-Item -Path "src\hooks\social\usePostLikes.ts"
Remove-Item -Path "src\hooks\social\useUserConnections.ts"
```

### 2. Update Imports

Make sure all imports point to the correct components and hooks:

```tsx
// In SocialFeed.tsx
import SocialFeedPost from "@/components/social/SocialFeedPost";
import { PostCreator } from "@/components/social/PostCreator";
import { TrendingSidebar } from "@/components/social/TrendingSidebar";
import { ConnectionsDrawer } from "@/components/social/ConnectionsDrawer";
import { CategoryFilter } from "@/components/social/CategoryFilter";

// In components
import { usePostComments } from "@/hooks/social/usePostComments";
import { usePostLikes } from "@/hooks/social/usePostLikes";
import { useUserConnections } from "@/hooks/social/useUserConnections";
```

### 3. Database Connections

Make sure the components connect to the database correctly:

1. `useSocialFeed` - Uses Supabase to fetch and manage posts
2. `usePostComments` - Manages comments through Supabase
3. `usePostLikes` - Handles likes through Supabase
4. `useUserConnections` - Manages user connections

### 4. Component Integration

#### Social Feed Page

The `SocialFeed` page should:

1. Use the `useSocialFeed` hook to get posts and functions
2. Pass posts to `SocialFeedPost` components
3. Use `CategoryFilter` for filtering
4. Include `PostCreator` for creating new posts
5. Include sidebars for trending topics and connections

#### Post Display and Interaction

1. `SocialFeedPost` should:
   - Display post content
   - Show like count and allow liking
   - Show comments and allow commenting
   - Show post categories

2. `PostComment` should:
   - Display comment content
   - Show like count and allow liking
   - Allow replying to comments
   - Show nested replies

### 5. Testing

Test the integration thoroughly as outlined in the testing plan document.

## Complete Data Flow

1. User loads Social Feed page
   - `useSocialFeed` fetches posts from Supabase
   - Posts are displayed using `SocialFeedPost` components

2. User creates a post
   - `PostCreator` collects post data
   - `useSocialFeed.handleCreatePost` sends data to Supabase
   - New post appears in feed

3. User interacts with posts
   - Likes: `handleToggleLike` updates Supabase
   - Comments: `handleAddComment` adds comment to Supabase
   - Categories: Clicking filters posts

4. Real-time updates
   - Comments update in real-time using Supabase subscriptions
   - Like counts update in real-time

## Next Steps

After integration:
1. Test thoroughly according to the testing plan
2. Address any performance issues
3. Consider adding additional features like infinite scroll
4. Implement real-time updates for more aspects of the feed
