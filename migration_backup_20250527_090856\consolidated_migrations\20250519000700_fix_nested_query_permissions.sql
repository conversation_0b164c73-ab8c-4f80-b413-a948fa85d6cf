-- Drop any existing policies that might conflict
DROP POLICY IF EXISTS "Enable nested queries for authenticated users on businesses" ON public.businesses;
DROP POLICY IF EXISTS "Enable nested queries for authenticated users on business_relevant_industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable nested queries for authenticated users on industries" ON public.industries;

-- Add policies specifically for nested queries
CREATE POLICY "Enable nested queries for authenticated users on businesses"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable nested queries for authenticated users on business_relevant_industries"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable nested queries for authenticated users on industries"
    ON public.industries
    FOR SELECT
    TO authenticated
    USING (true);

-- Grant necessary permissions for nested queries
GRANT SELECT ON public.businesses TO authenticated;
GRANT SELECT ON public.business_relevant_industries TO authenticated;
GRANT SELECT ON public.industries TO authenticated;

-- Add indexes to improve nested query performance
CREATE INDEX IF NOT EXISTS idx_business_relevant_industries_business_id_industry_id 
    ON public.business_relevant_industries(business_id, industry_id);
