-- Diagnose the original notification_preferences issue
-- Let's see exactly what went wrong with the foreign key constraint
-- Date: 2025-06-05

-- Check the exact foreign key constraint details
SELECT 
    'foreign_key_info' as check_type,
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule,
    rc.update_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'notification_preferences'
    AND tc.table_schema = 'public';

-- Test the exact sequence that happens during signup
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_metadata JSONB := '{"first_name": "Test", "last_name": "User"}';
    profile_exists BOOLEAN;
    error_msg TEXT;
BEGIN
    RAISE NOTICE '=== TESTING ORIGINAL APPROACH ===';
    RAISE NOTICE 'Test user ID: %', test_user_id;
    
    -- Step 1: Create profile (exactly as the trigger does)
    RAISE NOTICE 'Step 1: Creating profile...';
    BEGIN
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            email, 
            social_visibility, 
            subscription_tier, 
            subscription_status, 
            created_at, 
            updated_at
        )
        VALUES (
            test_user_id,
            test_metadata->>'first_name',
            test_metadata->>'last_name',
            test_email,
            'public',
            'none',
            'trial',
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Step 1: SUCCESS - Profile created';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Step 1: FAILED - %', error_msg;
            RETURN;
    END;
    
    -- Step 2: Verify profile exists before creating notification preferences
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = test_user_id) INTO profile_exists;
    RAISE NOTICE 'Step 2: Profile exists check: %', profile_exists;
    
    -- Step 3: Create notification preferences (exactly as the trigger would)
    RAISE NOTICE 'Step 3: Creating notification preferences...';
    BEGIN
        INSERT INTO public.notification_preferences (
            profile_id,
            email_notifications,
            post_likes,
            post_comments,
            comment_replies,
            comment_likes,
            connection_requests,
            connection_accepted,
            event_signups,
            event_updates,
            system_notifications,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,  -- This should be the profile_id
            true, true, true, true, true, true, true, true, true, true,
            NOW(), NOW()
        );
        RAISE NOTICE 'Step 3: SUCCESS - Notification preferences created';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Step 3: FAILED - %', error_msg;
            RAISE NOTICE 'This tells us exactly what the issue is!';
    END;
    
    -- Clean up
    DELETE FROM public.notification_preferences WHERE profile_id = test_user_id;
    DELETE FROM public.profiles WHERE id = test_user_id;
    
    RAISE NOTICE '=== TEST COMPLETED ===';
END $$;
