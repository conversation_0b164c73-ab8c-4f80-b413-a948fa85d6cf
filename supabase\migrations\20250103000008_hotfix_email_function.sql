-- HOTFIX: Fix the get_event_attendee_emails function immediately
-- Run this directly in Supabase SQL Editor

-- Drop all possible versions of the function
DROP FUNCTION IF EXISTS public.get_event_attendee_emails(UUID);
DROP FUNCTION IF EXISTS public.get_event_attendee_emails(event_id UUID);
DROP FUNCTION IF EXISTS get_event_attendee_emails(UUID);
DROP FUNCTION IF EXISTS get_event_attendee_emails(event_id UUID);

-- Create the correct function with proper column names
CREATE OR REPLACE FUNCTION public.get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow event creators to access emails
  IF NOT EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND creator_user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Access denied: You must be the event creator to view attendee emails';
  END IF;

  -- Return emails only for users who have given GDPR consent
  RETURN QUERY
  SELECT 
    es.user_id,
    au.email
  FROM 
    event_signups es
  JOIN 
    auth.users au ON es.user_id = au.id
  WHERE 
    es.event_id = get_event_attendee_emails.event_id
    AND es.gdpr_consent = true;
END;
$$;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION public.get_event_attendee_emails(UUID) TO authenticated;

-- Test the function works
SELECT 'Email function hotfix applied successfully' as status;
