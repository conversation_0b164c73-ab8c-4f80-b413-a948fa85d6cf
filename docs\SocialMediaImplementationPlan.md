# Social Media Feature Implementation Plan

## Overview

This document outlines the approach to building a fully functioning social media feature for the NetZero platform. The feature will include posting, commenting, liking, connections between users, and trending topics. All data will be stored in Supabase, with media files (images and videos) hosted on Cloudflare.

## Current Status

- We have mock data and UI components for the social feed
- Basic UI structure is in place (SocialFeed.tsx)
- Some basic components exist (Post, PostCreator, etc.)
- The `useSocialFeed` hook is using mock data

## Database Schema

### Tables to Create in Supabase

#### 1. `social_posts`
```sql
CREATE TABLE public.social_posts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  content TEXT NOT NULL,
  media_url TEXT,
  media_type TEXT CHECK (media_type IN ('image', 'video') OR media_type IS NULL),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  is_pinned BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  visibility TEXT DEFAULT 'public' CHECK (visibility IN ('public', 'connections', 'private')),
  hashtags TEXT[]
);

-- Add RLS policies
ALTER TABLE public.social_posts ENABLE ROW LEVEL SECURITY;

-- Everyone can read public posts
CREATE POLICY "Public posts are viewable by everyone" 
  ON public.social_posts 
  FOR SELECT 
  USING (visibility = 'public');

-- Users can read posts from their connections
CREATE POLICY "Connection posts are viewable by connections" 
  ON public.social_posts 
  FOR SELECT 
  USING (
    visibility = 'connections' AND 
    EXISTS (
      SELECT 1 FROM user_connections 
      WHERE (user_connections.user_id = auth.uid() AND user_connections.connection_id = social_posts.user_id) OR
            (user_connections.connection_id = auth.uid() AND user_connections.user_id = social_posts.user_id)
    )
  );

-- Authors can read their own posts
CREATE POLICY "Users can view their own posts" 
  ON public.social_posts 
  FOR SELECT 
  USING (user_id = auth.uid());

-- Authors can insert their own posts
CREATE POLICY "Users can create their own posts" 
  ON public.social_posts 
  FOR INSERT 
  WITH CHECK (user_id = auth.uid());

-- Authors can update their own posts
CREATE POLICY "Users can update their own posts" 
  ON public.social_posts 
  FOR UPDATE 
  USING (user_id = auth.uid());

-- Authors can delete their own posts
CREATE POLICY "Users can delete their own posts" 
  ON public.social_posts 
  FOR DELETE 
  USING (user_id = auth.uid());
```

#### 2. `post_comments`
```sql
CREATE TABLE public.post_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID REFERENCES public.social_posts(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  parent_comment_id UUID REFERENCES public.post_comments(id) ON DELETE CASCADE
);

-- Add RLS policies
ALTER TABLE public.post_comments ENABLE ROW LEVEL SECURITY;

-- Everyone can read comments on posts they can see
CREATE POLICY "Comments are viewable by everyone who can see the post" 
  ON public.post_comments 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM social_posts 
      WHERE social_posts.id = post_comments.post_id AND (
        social_posts.visibility = 'public' OR
        (social_posts.visibility = 'connections' AND 
         EXISTS (
           SELECT 1 FROM user_connections 
           WHERE (user_connections.user_id = auth.uid() AND user_connections.connection_id = social_posts.user_id) OR
                 (user_connections.connection_id = auth.uid() AND user_connections.user_id = social_posts.user_id)
         )
        ) OR
        social_posts.user_id = auth.uid()
      )
    )
  );

-- Authors can create comments on posts they can see
CREATE POLICY "Users can comment on visible posts" 
  ON public.post_comments 
  FOR INSERT 
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM social_posts 
      WHERE social_posts.id = post_comments.post_id AND (
        social_posts.visibility = 'public' OR
        (social_posts.visibility = 'connections' AND 
         EXISTS (
           SELECT 1 FROM user_connections 
           WHERE (user_connections.user_id = auth.uid() AND user_connections.connection_id = social_posts.user_id) OR
                 (user_connections.connection_id = auth.uid() AND user_connections.user_id = social_posts.user_id)
         )
        ) OR
        social_posts.user_id = auth.uid()
      )
    )
  );

-- Authors can update their own comments
CREATE POLICY "Users can update their own comments" 
  ON public.post_comments 
  FOR UPDATE 
  USING (user_id = auth.uid());

-- Authors can delete their own comments
CREATE POLICY "Users can delete their own comments" 
  ON public.post_comments 
  FOR DELETE 
  USING (user_id = auth.uid());
```

#### 3. `post_likes`
```sql
CREATE TABLE public.post_likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID REFERENCES public.social_posts(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (post_id, user_id)
);

-- Add RLS policies
ALTER TABLE public.post_likes ENABLE ROW LEVEL SECURITY;

-- Everyone can see likes on posts they can see
CREATE POLICY "Likes are viewable by everyone who can see the post" 
  ON public.post_likes 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM social_posts 
      WHERE social_posts.id = post_likes.post_id AND (
        social_posts.visibility = 'public' OR
        (social_posts.visibility = 'connections' AND 
         EXISTS (
           SELECT 1 FROM user_connections 
           WHERE (user_connections.user_id = auth.uid() AND user_connections.connection_id = social_posts.user_id) OR
                 (user_connections.connection_id = auth.uid() AND user_connections.user_id = social_posts.user_id)
         )
        ) OR
        social_posts.user_id = auth.uid()
      )
    )
  );

-- Users can like posts they can see
CREATE POLICY "Users can like visible posts" 
  ON public.post_likes 
  FOR INSERT 
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM social_posts 
      WHERE social_posts.id = post_likes.post_id AND (
        social_posts.visibility = 'public' OR
        (social_posts.visibility = 'connections' AND 
         EXISTS (
           SELECT 1 FROM user_connections 
           WHERE (user_connections.user_id = auth.uid() AND user_connections.connection_id = social_posts.user_id) OR
                 (user_connections.connection_id = auth.uid() AND user_connections.user_id = social_posts.user_id)
         )
        ) OR
        social_posts.user_id = auth.uid()
      )
    )
  );

-- Users can delete their own likes
CREATE POLICY "Users can unlike posts" 
  ON public.post_likes 
  FOR DELETE 
  USING (user_id = auth.uid());
```

#### 4. `user_connections`
```sql
CREATE TABLE public.user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  connection_id UUID REFERENCES auth.users(id) NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'accepted', 'rejected', 'blocked')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (user_id, connection_id)
);

-- Add RLS policies
ALTER TABLE public.user_connections ENABLE ROW LEVEL SECURITY;

-- Users can see their own connections
CREATE POLICY "Users can see their own connections" 
  ON public.user_connections 
  FOR SELECT 
  USING (user_id = auth.uid() OR connection_id = auth.uid());

-- Users can create connection requests
CREATE POLICY "Users can create connection requests" 
  ON public.user_connections 
  FOR INSERT 
  WITH CHECK (
    user_id = auth.uid() AND 
    status = 'pending' AND
    user_id != connection_id
  );

-- Users can update connection status only if they are the recipient
CREATE POLICY "Recipients can update connection status" 
  ON public.user_connections 
  FOR UPDATE 
  USING (
    connection_id = auth.uid() AND
    (OLD.status = 'pending' OR OLD.status = 'accepted')
  );

-- Users can delete their own connections
CREATE POLICY "Users can delete their connections" 
  ON public.user_connections 
  FOR DELETE 
  USING (user_id = auth.uid() OR connection_id = auth.uid());
```

#### 5. `hashtags`
```sql
CREATE TABLE public.hashtags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT UNIQUE NOT NULL,
  post_count INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies
ALTER TABLE public.hashtags ENABLE ROW LEVEL SECURITY;

-- Everyone can read hashtags
CREATE POLICY "Hashtags are viewable by everyone" 
  ON public.hashtags 
  FOR SELECT 
  USING (true);

-- Hashtags are managed by triggers, not directly by users
```

#### 6. `post_hashtags`
```sql
CREATE TABLE public.post_hashtags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  post_id UUID REFERENCES public.social_posts(id) ON DELETE CASCADE NOT NULL,
  hashtag_id UUID REFERENCES public.hashtags(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE (post_id, hashtag_id)
);

-- Add RLS policies
ALTER TABLE public.post_hashtags ENABLE ROW LEVEL SECURITY;

-- Everyone can read post_hashtags
CREATE POLICY "Post hashtags are viewable by everyone" 
  ON public.post_hashtags 
  FOR SELECT 
  USING (true);

-- Post hashtags are managed by triggers, not directly by users
```

#### 7. Update `profiles` table
```sql
-- Add social-related columns to the profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS post_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS connection_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS social_visibility TEXT DEFAULT 'public' CHECK (social_visibility IN ('public', 'connections', 'private'));
```

### Database Functions and Triggers

#### 1. Parse hashtags from post content
```sql
CREATE OR REPLACE FUNCTION public.extract_hashtags() 
RETURNS TRIGGER AS $$
DECLARE
  hashtag TEXT;
  hashtag_id UUID;
  hashtags_array TEXT[];
BEGIN
  -- Extract hashtags from content
  SELECT ARRAY(
    SELECT DISTINCT SUBSTRING(word FROM 2)
    FROM regexp_matches(NEW.content, '#([a-zA-Z0-9_]+)', 'g') AS match(word)
  ) INTO hashtags_array;
  
  -- Store hashtags in the post
  NEW.hashtags := hashtags_array;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to extract hashtags before insert or update
CREATE TRIGGER extract_hashtags_trigger
BEFORE INSERT OR UPDATE OF content ON public.social_posts
FOR EACH ROW
EXECUTE FUNCTION public.extract_hashtags();
```

#### 2. Process hashtags after post insertion
```sql
CREATE OR REPLACE FUNCTION public.process_hashtags() 
RETURNS TRIGGER AS $$
DECLARE
  hashtag TEXT;
  hashtag_id UUID;
BEGIN
  -- Process each hashtag
  FOREACH hashtag IN ARRAY NEW.hashtags
  LOOP
    -- Insert or update the hashtag
    INSERT INTO public.hashtags (name)
    VALUES (LOWER(hashtag))
    ON CONFLICT (name) 
    DO UPDATE SET post_count = hashtags.post_count + 1, updated_at = now()
    RETURNING id INTO hashtag_id;
    
    -- Link hashtag to post
    INSERT INTO public.post_hashtags (post_id, hashtag_id)
    VALUES (NEW.id, hashtag_id);
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to process hashtags after insert
CREATE TRIGGER process_hashtags_trigger
AFTER INSERT ON public.social_posts
FOR EACH ROW
EXECUTE FUNCTION public.process_hashtags();
```

#### 3. Update post count in profiles
```sql
CREATE OR REPLACE FUNCTION public.update_post_count() 
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.profiles
    SET post_count = post_count + 1
    WHERE id = NEW.user_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.profiles
    SET post_count = GREATEST(0, post_count - 1)
    WHERE id = OLD.user_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update post count
CREATE TRIGGER update_post_count_trigger
AFTER INSERT OR DELETE ON public.social_posts
FOR EACH ROW
EXECUTE FUNCTION public.update_post_count();
```

#### 4. Update connection count in profiles
```sql
CREATE OR REPLACE FUNCTION public.update_connection_count() 
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.status = 'accepted' THEN
      UPDATE public.profiles
      SET connection_count = connection_count + 1
      WHERE id IN (NEW.user_id, NEW.connection_id);
    END IF;
  ELSIF TG_OP = 'UPDATE' THEN
    IF NEW.status = 'accepted' AND OLD.status != 'accepted' THEN
      UPDATE public.profiles
      SET connection_count = connection_count + 1
      WHERE id IN (NEW.user_id, NEW.connection_id);
    ELSIF NEW.status != 'accepted' AND OLD.status = 'accepted' THEN
      UPDATE public.profiles
      SET connection_count = GREATEST(0, connection_count - 1)
      WHERE id IN (NEW.user_id, NEW.connection_id);
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.status = 'accepted' THEN
      UPDATE public.profiles
      SET connection_count = GREATEST(0, connection_count - 1)
      WHERE id IN (OLD.user_id, OLD.connection_id);
    END IF;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update connection count
CREATE TRIGGER update_connection_count_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.user_connections
FOR EACH ROW
EXECUTE FUNCTION public.update_connection_count();
```

## Frontend Implementation

### Core Components to Implement or Update

#### 1. Update `useSocialFeed` Hook
- Replace mock data with real data from Supabase
- Implement pagination and infinite scrolling
- Add real-time updates using Supabase subscriptions
- Handle error states and loading states

#### 2. Update Post Component
- Add real-time like count updates
- Implement post deletion
- Add post editing
- Add sharing functionality
- Parse hashtags in content to make them clickable/searchable
- Add proper timestamp formatting with relative time

#### 3. Update PostCreator Component
- Handle media uploads to Cloudflare
- Implement post visibility options
- Add hashtag suggestions
- Add mention functionality (@username)
- Implement post preview

#### 4. Update ConnectionsSidebar Component
- Fetch real connection suggestions
- Implement connection request functionality
- Show pending connections
- Add option to accept/reject connection requests

#### 5. Add Notifications Component
- Create notifications for new connection requests
- Create notifications for likes and comments on posts
- Create notifications for mentions

#### 6. Add Explore/Discover Page
- Show trending posts
- Filter by hashtags
- Filter by category/industry

#### 7. Add User Social Profile Page
- Show user's posts
- Show user's connections
- Allow users to customize their social profile

## Implementation Tasks

### Phase 1: Database Setup and Basic Backend Integration

1. Create all the necessary Supabase tables, functions, and triggers
2. Set up appropriate RLS policies
3. Test database operations manually using the Supabase dashboard
4. Update existing TypeScript interfaces to match the new database schema
5. Create Supabase helper functions for common operations

### Phase 2: Frontend Integration

1. Update the `useSocialFeed` hook to fetch real data
2. Update the Post component to work with real data
3. Update the PostCreator component to create real posts
4. Implement Cloudflare image uploading integration for post media
5. Update ConnectionsSidebar to work with real connections

### Phase 3: Advanced Features

1. Implement real-time updates using Supabase subscriptions
2. Add infinite scrolling for the feed
3. Implement hashtag search and filtering
4. Add notifications system
5. Create Explore/Discover page
6. Add user social profile page

### Phase 4: Polish and Optimization

1. Add loading states and error handling
2. Optimize database queries
3. Add caching for frequently accessed data
4. Implement analytics for post engagement
5. Add comprehensive test coverage
6. Review and update accessibility features

## Cloudflare Integration for Media

1. Create separate upload functions for post media
2. Set up appropriate access controls
3. Implement media optimization
4. Add support for different media types (images and videos)
5. Implement media preview in post creator

## Implementation Timeline

- **Phase 1**: 1-2 weeks
- **Phase 2**: 2-3 weeks
- **Phase 3**: 2-3 weeks
- **Phase 4**: 1-2 weeks

**Total**: 6-10 weeks depending on development resources and complexity

## Required Files to Create or Update

### New Files

1. `src/hooks/useConnections.ts` - For managing user connections
2. `src/hooks/useHashtags.ts` - For fetching and managing hashtags
3. `src/components/social/HashtagLink.tsx` - For clickable hashtags
4. `src/components/social/SocialNotifications.tsx` - For social notifications
5. `src/components/social/ConnectionRequestCard.tsx` - For connection requests
6. `src/pages/social/Explore.tsx` - For exploring content
7. `src/pages/social/HashtagFeed.tsx` - For viewing posts by hashtag
8. `src/pages/social/UserSocialProfile.tsx` - For viewing user's social profile
9. `supabase/migrations/[timestamp]_create_social_tables.sql` - For database migrations

### Files to Update

1. `src/hooks/useSocialFeed.tsx` - Update to use real data
2. `src/components/social/Post.tsx` - Update for real-time functionality
3. `src/components/social/PostCreator.tsx` - Update for real media uploads
4. `src/components/social/ConnectionsSidebar.tsx` - Update for real connections
5. `src/components/social/TrendingSidebar.tsx` - Update for real trending topics
6. `src/lib/cloudflare.ts` - Add functions for social media uploads
7. `src/pages/SocialFeed.tsx` - Update for full functionality

## Next Steps

1. Create the database migration files
2. Test database operations
3. Update TypeScript interfaces
4. Begin updating the frontend components

This implementation plan provides a structured approach to build out the social media feature in the NetZero platform. The focus is on creating a robust database schema with appropriate security policies, implementing real-time functionality, and ensuring the user experience is smooth and responsive.
