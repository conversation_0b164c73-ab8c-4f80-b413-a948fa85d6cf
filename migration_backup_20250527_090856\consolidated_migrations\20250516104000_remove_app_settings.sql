-- Drop the get_app_setting function first as it depends on the table
DROP FUNCTION IF EXISTS get_app_setting;

-- Drop the app_settings table
DROP TABLE IF EXISTS app_settings;

-- Update the handle_deleted_post_media function to use native configuration
CREATE OR REPLACE FUNCTION handle_deleted_post_media()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the deleted post had an image
  IF OLD.media_type = 'image' AND OLD.media_url IS NOT NULL THEN
    -- Call edge function to delete the image from Cloudflare using native config
    PERFORM pg_net.http_post(
      url := 'https://' || current_database() || '.supabase.co/functions/v1/cloudflare-images',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || NULLIF(current_setting('app.settings.service_role_key', true), '')::text
      ),
      body := jsonb_build_object(
        'method', 'delete',
        'record', jsonb_build_object(
          'media_url', OLD.media_url,
          'media_type', OLD.media_type
        )
      )
    );
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;
