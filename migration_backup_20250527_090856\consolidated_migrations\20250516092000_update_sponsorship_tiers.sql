-- Migration: Update sponsorship tiers to match subscription tiers
-- This migration updates the sponsorship tiers from bronze/silver/gold to seedling/sapling/woodland

-- First, update existing sponsorships to new tiers
UPDATE public.businesses
SET sponsorship_tier = 
  CASE sponsorship_tier
    WHEN 'bronze' THEN 'seedling'
    WHEN 'silver' THEN 'sapling'
    WHEN 'gold' THEN 'woodland'
    ELSE sponsorship_tier
  END;

-- Drop the existing check constraint
ALTER TABLE public.businesses
  DROP CONSTRAINT IF EXISTS businesses_sponsorship_tier_check;

-- Add new check constraint with updated tiers
ALTER TABLE public.businesses
  ADD CONSTRAINT businesses_sponsorship_tier_check 
  CHECK (sponsorship_tier IN ('none', 'seedling', 'sapling', 'woodland'));

-- Update the featured businesses view
DROP VIEW IF EXISTS public.featured_businesses;
CREATE OR REPLACE VIEW public.featured_businesses AS
SELECT 
  *,
  CASE 
    WHEN sponsorship_tier = 'woodland' THEN 3
    WHEN sponsorship_tier = 'sapling' THEN 2 
    WHEN sponsorship_tier = 'seedling' THEN 1
    ELSE 0
  END as sponsorship_level
FROM 
  public.businesses
WHERE 
  sponsorship_tier IS NOT NULL 
  AND sponsorship_tier != 'none'
  AND sponsorship_start_date <= NOW()
  AND (sponsorship_end_date IS NULL OR sponsorship_end_date > NOW())
ORDER BY
  sponsorship_level DESC,
  sponsorship_start_date DESC;

COMMENT ON COLUMN public.businesses.sponsorship_tier IS 'The sponsorship tier of the business (none, seedling, sapling, woodland)';
