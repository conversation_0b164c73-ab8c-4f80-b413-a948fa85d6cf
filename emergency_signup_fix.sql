-- EMERGENCY SIGNUP FIX - Based on diagnostic results
-- Date: 2025-01-06
-- CRITICAL: notification_preferences table missing or wrong structure

BEGIN;

-- =====================================================
-- 1. CREATE/FIX NOTIFICATION_PREFERENCES TABLE
-- =====================================================

-- Check if notification_preferences table exists with correct structure
DO $$
BEGIN
  -- Check if table exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'notification_preferences'
  ) THEN
    -- Create the table if it doesn't exist
    CREATE TABLE public.notification_preferences (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
      email_notifications B<PERSON><PERSON><PERSON>N DEFAULT true,
      post_likes BOOLEAN DEFAULT true,
      post_comments BOOLEAN DEFAULT true,
      comment_replies BOOLEAN DEFAULT true,
      comment_likes BOOLEAN DEFAULT true,
      connection_requests BOOLEAN DEFAULT true,
      connection_accepted BOOLEAN DEFAULT true,
      event_signups BOOLEAN DEFAULT true,
      event_updates BOOLEAN DEFAULT true,
      system_notifications BOOLEAN DEFAULT true,
      created_at TIMESTAMPTZ DEFAULT now(),
      updated_at TIMESTAMPTZ DEFAULT now(),
      UNIQUE(profile_id)
    );
    
    -- Enable RLS
    ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;
    
    -- Create RLS policy
    CREATE POLICY "Users can manage their own notification preferences" 
    ON public.notification_preferences
    FOR ALL TO authenticated
    USING (profile_id = auth.uid())
    WITH CHECK (profile_id = auth.uid());
    
    RAISE NOTICE 'Created notification_preferences table';
  ELSE
    -- Table exists, check if it has the required columns
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'notification_preferences' 
      AND column_name = 'post_likes'
    ) THEN
      -- Add missing columns to existing table
      ALTER TABLE public.notification_preferences 
      ADD COLUMN IF NOT EXISTS email_notifications BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS post_likes BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS post_comments BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS comment_replies BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS comment_likes BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS connection_requests BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS connection_accepted BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS event_signups BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS event_updates BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS system_notifications BOOLEAN DEFAULT true;
      
      RAISE NOTICE 'Added missing columns to notification_preferences table';
    END IF;
  END IF;
END $$;

-- =====================================================
-- 2. FIX HANDLE_NEW_USER FUNCTION
-- =====================================================

-- Update the handle_new_user function to work with the correct table structure
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
BEGIN
  -- Insert a new profile record for the new user
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    email, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  )
  RETURNING id INTO profile_id;

  -- Create default user_consent_settings for the new user
  INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    true,  -- profile_visibility - default to public
    true,  -- newsletter_subscription - default to ENABLED
    true,  -- show_businesses - default to visible
    true,  -- show_events - default to visible
    true,  -- show_connections - default to visible
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (user_id) DO NOTHING;

  -- Create notification preferences (now that table exists)
  INSERT INTO public.notification_preferences (
    profile_id,
    email_notifications,
    post_likes,
    post_comments,
    comment_replies,
    comment_likes,
    connection_requests,
    connection_accepted,
    event_signups,
    event_updates,
    system_notifications,
    created_at,
    updated_at
  )
  VALUES (
    profile_id,
    true, true, true, true, true, true, true, true, true, true,
    NEW.created_at, NEW.created_at
  )
  ON CONFLICT (profile_id) DO NOTHING;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the signup
    RAISE WARNING 'Error in handle_new_user function: %', SQLERRM;
    -- Still return NEW so the user signup doesn't fail
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update function comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates profile, user consent settings, and notification preferences for new users';

-- =====================================================
-- 3. REPAIR EXISTING USERS
-- =====================================================

-- Create function to repair missing records for existing users
CREATE OR REPLACE FUNCTION public.repair_existing_users()
RETURNS TABLE(
  user_id UUID,
  profile_exists BOOLEAN,
  consent_created BOOLEAN,
  notification_prefs_created BOOLEAN,
  errors TEXT
) AS $$
DECLARE
  user_record RECORD;
  profile_exists BOOLEAN;
  consent_created BOOLEAN := FALSE;
  notification_prefs_created BOOLEAN := FALSE;
  error_msg TEXT := '';
BEGIN
  -- Loop through users who might be missing records
  FOR user_record IN 
    SELECT u.id, u.email, u.created_at, u.raw_user_meta_data
    FROM auth.users u
    WHERE u.created_at > '2025-01-01'  -- Recent users only
  LOOP
    -- Check if profile exists
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = user_record.id) INTO profile_exists;
    
    -- Create missing consent settings
    IF NOT EXISTS (SELECT 1 FROM public.user_consent_settings WHERE user_id = user_record.id) THEN
      BEGIN
        INSERT INTO public.user_consent_settings (
          user_id, profile_visibility, newsletter_subscription, 
          show_businesses, show_events, show_connections,
          created_at, updated_at
        )
        VALUES (
          user_record.id, true, true, true, true, true,
          user_record.created_at, user_record.created_at
        );
        consent_created := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          error_msg := error_msg || 'Consent settings failed: ' || SQLERRM || '; ';
      END;
    END IF;
    
    -- Create missing notification preferences
    IF profile_exists AND NOT EXISTS (SELECT 1 FROM public.notification_preferences WHERE profile_id = user_record.id) THEN
      BEGIN
        INSERT INTO public.notification_preferences (
          profile_id, email_notifications, post_likes, post_comments,
          comment_replies, comment_likes, connection_requests, connection_accepted,
          event_signups, event_updates, system_notifications,
          created_at, updated_at
        )
        VALUES (
          user_record.id, true, true, true, true, true, true, true, true, true, true,
          user_record.created_at, user_record.created_at
        );
        notification_prefs_created := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          error_msg := error_msg || 'Notification prefs failed: ' || SQLERRM || '; ';
      END;
    END IF;
    
    -- Return results for this user
    RETURN QUERY SELECT 
      user_record.id,
      profile_exists,
      consent_created,
      notification_prefs_created,
      CASE WHEN error_msg = '' THEN NULL ELSE error_msg END;
      
    -- Reset for next iteration
    consent_created := FALSE;
    notification_prefs_created := FALSE;
    error_msg := '';
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.repair_existing_users() TO authenticated;

COMMIT;

-- =====================================================
-- 4. INSTRUCTIONS FOR NEXT STEPS
-- =====================================================

-- After running this migration, execute:
-- SELECT * FROM repair_existing_users();
