-- Check if the user_consent_settings table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_consent_settings') THEN
        -- Create the user consent settings table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.user_consent_settings (
            user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            share_email_with_event_creators BOOLEAN NOT NULL DEFAULT FALSE,
            share_email_with_attendees BOOLEAN NOT NULL DEFAULT FALSE,
            share_contact_details BOOLEAN NOT NULL DEFAULT FALSE,
            consent_updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Add RLS to the consent settings table
        ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

        -- Users can only view and edit their own consent settings
        CREATE POLICY view_own_consent_settings 
            ON public.user_consent_settings 
            FOR SELECT
            TO authenticated
            USING (auth.uid() = user_id);

        CREATE POLICY update_own_consent_settings 
            ON public.user_consent_settings 
            FOR UPDATE
            TO authenticated
            USING (auth.uid() = user_id);

        -- Create index for user_id to speed up joins
        CREATE INDEX IF NOT EXISTS idx_user_consent_settings_user_id ON public.user_consent_settings(user_id);

        -- Add a trigger to automatically create default consent settings for new users
        CREATE OR REPLACE FUNCTION create_default_user_consent_settings()
        RETURNS TRIGGER AS $$
        BEGIN
            INSERT INTO public.user_consent_settings (user_id)
            VALUES (NEW.id)
            ON CONFLICT (user_id) DO NOTHING;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Add trigger to create default consent settings whenever a profile is created
        DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
        CREATE TRIGGER on_auth_user_created
            AFTER INSERT ON auth.users
            FOR EACH ROW
            EXECUTE FUNCTION create_default_user_consent_settings();
    END IF;
END
$$;

-- Now create the secure profile view, checking for the existence of the table
CREATE OR REPLACE VIEW public_professional_profiles AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    -- Only expose email if user has explicitly consented (with null check for missing consent settings)
    CASE 
        WHEN (
            EXISTS (
                SELECT 1 FROM user_consent_settings ucs 
                WHERE ucs.user_id = p.id AND 
                (ucs.share_email_with_event_creators = true OR 
                 ucs.share_email_with_attendees = true OR 
                 ucs.share_contact_details = true)
            )
        ) THEN p.email 
        ELSE NULL 
    END as email,
    -- Only expose phone if user has explicitly consented (with null check for missing consent settings)
    CASE 
        WHEN (
            EXISTS (
                SELECT 1 FROM user_consent_settings ucs 
                WHERE ucs.user_id = p.id AND ucs.share_contact_details = true
            )
        ) THEN p.phone
        ELSE NULL 
    END as phone,
    -- Only show social links if profile is public
    CASE 
        WHEN p.profile_visibility = true THEN p.linkedin
        ELSE NULL 
    END as linkedin,
    CASE 
        WHEN p.profile_visibility = true THEN p.twitter
        ELSE NULL 
    END as twitter,
    CASE 
        WHEN p.profile_visibility = true THEN p.website
        ELSE NULL 
    END as website,
    p.location_id,
    p.main_industry_id,
    p.profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    l.name as location_name,
    i.name as industry_name,
    -- Include category names but not IDs
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) as category_names,
    -- Include professional type names but not IDs
    (
        SELECT array_agg(pt.name)
        FROM profile_professional_types ppt
        JOIN sustainability_professional_types pt ON pt.id = ppt.professional_type_id
        WHERE ppt.profile_id = p.id
    ) as professional_types,
    -- Create a text search vector excluding sensitive data
    to_tsvector('english', 
        coalesce(p.first_name, '') || ' ' || 
        coalesce(p.last_name, '') || ' ' || 
        coalesce(p.bio, '') || ' ' || 
        coalesce(p.title, '') || ' ' ||
        coalesce(p.organization, '')
    ) as text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    p.profile_visibility = true
    -- Exclude deleted or inactive profiles
    AND (p.deleted_at IS NULL OR p.deleted_at > now())
    AND (p.is_active IS NULL OR p.is_active = true);

-- Create indexes for efficient searching
CREATE INDEX IF NOT EXISTS idx_public_professionals_name 
    ON profiles USING gin((first_name || ' ' || last_name) gin_trgm_ops);

CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Set up Row Level Security for the view
ALTER VIEW public_professional_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy that only allows reading public data
CREATE POLICY "Allow reading public professional profiles"
    ON public_professional_profiles
    FOR SELECT
    TO authenticated
    USING (true);  -- Since we filtered in the view, we can allow all rows

-- Grant access to the view
GRANT SELECT ON public_professional_profiles TO authenticated;
GRANT SELECT ON public_professional_profiles TO anon;
