-- Check for function overloads that might be causing issues
-- Run this in Supabase SQL Editor

-- 1. Show ALL remove_connection function variations
SELECT 
    'remove_connection overloads' as section,
    p.proname as function_name,
    pg_get_function_identity_arguments(p.oid) as arguments,
    pg_get_functiondef(p.oid) as full_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'remove_connection'
AND n.nspname = 'public';

-- 2. Show ALL cancel_connection_request function variations  
SELECT 
    'cancel_connection_request overloads' as section,
    p.proname as function_name,
    pg_get_function_identity_arguments(p.oid) as arguments,
    pg_get_functiondef(p.oid) as full_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'cancel_connection_request'
AND n.nspname = 'public';

-- 3. Show ALL request_connection function variations
SELECT 
    'request_connection overloads' as section,
    p.proname as function_name,
    pg_get_function_identity_arguments(p.oid) as arguments,
    pg_get_functiondef(p.oid) as full_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'request_connection'
AND n.nspname = 'public';

-- 4. Show ALL respond_to_connection_request function variations
SELECT 
    'respond_to_connection_request overloads' as section,
    p.proname as function_name,
    pg_get_function_identity_arguments(p.oid) as arguments,
    pg_get_functiondef(p.oid) as full_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'respond_to_connection_request'
AND n.nspname = 'public';
