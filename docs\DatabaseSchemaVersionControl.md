# Database Schema Version Control

Last Updated: May 22, 2025

## Schema Management Policy

### Current Approach
- All database changes are managed through migrations
- Migrations are stored in `/supabase/migrations/`
- Each migration is timestamped and includes a descriptive name
- RLS policies and permissions are defined in migrations

### Deprecated
The following files are deprecated and should not be used:
- `scripts/database/schema/NetZeroHub_Complete_Schema.sql.deprecated`

### Migration Naming Convention
Format: `YYYYMMDDHHMMSS_descriptive_name.sql`

Example: `20250522100000_implement_event_email_consent_system.sql`

### Creating New Migrations

1. Create a new file in `/supabase/migrations/`
2. Use the correct timestamp format
3. Include a descriptive name
4. Document any breaking changes
5. Test in development before deploying

### Best Practices

1. One conceptual change per migration
2. Include both `up` and `down` migrations where possible
3. Document any required data migrations
4. Test migrations in development first
5. Always backup production data before applying migrations

### Schema Documentation

The current schema can be viewed:
1. In the Supabase Dashboard
2. By examining the migration files in chronological order
3. By connecting to the database and using schema inspection queries
