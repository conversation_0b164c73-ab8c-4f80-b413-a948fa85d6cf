import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { SocialNetworkV2 } from '@/components/social/SocialNetworkV2Production';

export default function ConnectionsPage() {
  const [searchParams] = useSearchParams();
  const activeTab = searchParams.get('tab') || 'connections';

  return (
    <Layout>
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold mb-4">Professional Connections</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Build your professional network in the sustainability community.
              Connect with peers, discover opportunities, and stay updated on industry activities.
            </p>
          </div>

          <SocialNetworkV2
            defaultTab={activeTab}
            showStats={true}
          />
        </div>
      </div>
    </Layout>
  );
}
