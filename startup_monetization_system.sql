-- Startup-Friendly Monetization System for NetZero Platform
-- Date: 2025-01-06
-- Simple, practical approach for launch

BEGIN;

-- =====================================================
-- 1. CREATE SUBSCRIPTION PLANS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL CHECK (type IN ('user', 'business')),
    price_monthly DECIMAL(10,2) NOT NULL,
    stripe_price_id TEXT,
    stripe_product_id TEXT,
    features JSONB NOT NULL DEFAULT '{}',
    limits JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Insert user subscription plans
INSERT INTO public.subscription_plans (name, type, price_monthly, features, limits) VALUES
('seed', 'user', 0.00, 
 '{"posts": true, "businesses": true, "jobs_events": true, "connections": true, "view_all": true}',
 '{"posts_per_week": 5, "post_types": ["image"], "businesses_total": 1, "jobs_events_per_month": 1, "connections_per_week": 5}'
),
('sapling', 'user', 5.99,
 '{"unlimited_posts": true, "video_posts": true, "multiple_listings": true, "priority_support": true}',
 '{"post_types": ["image", "video"], "jobs_events_per_month": 5, "connections_per_week": 10}'
),
('woodland', 'user', 10.99,
 '{"everything_unlimited": true, "multiple_businesses": true, "premium_features": true}',
 '{"unlimited": true}'
);

-- Insert business advertising plans
INSERT INTO public.subscription_plans (name, type, price_monthly, features, limits) VALUES
('small_ad', 'business', 29.99,
 '{"sidebar_banner": true, "basic_analytics": true}',
 '{"ad_size": "small", "placement": "sidebar"}'
),
('medium_ad', 'business', 59.99,
 '{"content_banner": true, "advanced_analytics": true, "priority_placement": true}',
 '{"ad_size": "medium", "placement": "content_area"}'
),
('large_ad', 'business', 99.99,
 '{"top_banner": true, "premium_analytics": true, "guaranteed_visibility": true}',
 '{"ad_size": "large", "placement": "top_page"}'
);

-- =====================================================
-- 2. CREATE USAGE TRACKING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.user_usage_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    week_year TEXT NOT NULL, -- Format: 'YYYY-WW' for weekly tracking
    month_year TEXT NOT NULL, -- Format: 'YYYY-MM' for monthly tracking
    
    -- Weekly counters
    posts_this_week INTEGER DEFAULT 0,
    connections_this_week INTEGER DEFAULT 0,
    
    -- Monthly counters
    businesses_this_month INTEGER DEFAULT 0,
    jobs_events_this_month INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(user_id, week_year, month_year)
);

-- Enable RLS
ALTER TABLE public.user_usage_tracking ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Users can view their own usage" ON public.user_usage_tracking
    FOR ALL TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- =====================================================
-- 3. UPDATE PROFILES TABLE CONSTRAINTS
-- =====================================================

-- First, fix existing data to match new constraints
UPDATE public.profiles
SET subscription_tier = CASE
    WHEN subscription_tier IN ('none', 'trial', 'free', '') OR subscription_tier IS NULL THEN 'seed'
    WHEN subscription_tier = 'basic' THEN 'sapling'
    WHEN subscription_tier = 'premium' THEN 'woodland'
    WHEN subscription_tier IN ('seed', 'sapling', 'woodland') THEN subscription_tier
    ELSE 'seed'  -- Default fallback
END;

-- Fix subscription_status values
UPDATE public.profiles
SET subscription_status = CASE
    WHEN subscription_status IN ('active', 'cancelled', 'past_due', 'trial', 'incomplete') THEN subscription_status
    WHEN subscription_status IS NULL OR subscription_status = '' THEN 'active'
    ELSE 'active'  -- Default fallback
END;

-- Now drop and recreate constraints
ALTER TABLE public.profiles
DROP CONSTRAINT IF EXISTS profiles_subscription_tier_check;

ALTER TABLE public.profiles
ADD CONSTRAINT profiles_subscription_tier_check
CHECK (subscription_tier IN ('seed', 'sapling', 'woodland'));

-- Add subscription_status constraint if it doesn't exist
ALTER TABLE public.profiles
DROP CONSTRAINT IF EXISTS profiles_subscription_status_check;

ALTER TABLE public.profiles
ADD CONSTRAINT profiles_subscription_status_check
CHECK (subscription_status IN ('active', 'cancelled', 'past_due', 'trial', 'incomplete'));

-- =====================================================
-- 4. CREATE BUSINESS ADVERTISEMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.business_advertisements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    ad_plan TEXT NOT NULL REFERENCES public.subscription_plans(name),
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    link_url TEXT,
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMPTZ DEFAULT now(),
    end_date TIMESTAMPTZ,
    clicks_count INTEGER DEFAULT 0,
    impressions_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.business_advertisements ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Business owners can manage their ads" ON public.business_advertisements
    FOR ALL TO authenticated
    USING (business_id IN (SELECT id FROM businesses WHERE owner_id = auth.uid()))
    WITH CHECK (business_id IN (SELECT id FROM businesses WHERE owner_id = auth.uid()));

CREATE POLICY "Everyone can view active ads" ON public.business_advertisements
    FOR SELECT TO authenticated
    USING (is_active = true AND (end_date IS NULL OR end_date > now()));

-- =====================================================
-- 5. CREATE TIER CHECKING FUNCTIONS
-- =====================================================

-- Function to check if user can perform action based on their tier
CREATE OR REPLACE FUNCTION public.can_user_perform_action(
    action_type TEXT,
    content_type TEXT DEFAULT NULL,
    user_id UUID DEFAULT auth.uid()
)
RETURNS JSONB AS $$
DECLARE
    user_tier TEXT;
    current_week TEXT;
    current_month TEXT;
    usage_record RECORD;
    plan_limits JSONB;
    result JSONB;
BEGIN
    -- Get user's subscription tier
    SELECT subscription_tier INTO user_tier
    FROM public.profiles
    WHERE id = user_id;
    
    IF user_tier IS NULL THEN
        RETURN jsonb_build_object('allowed', false, 'reason', 'No subscription tier found');
    END IF;
    
    -- Get plan limits
    SELECT limits INTO plan_limits
    FROM public.subscription_plans
    WHERE name = user_tier AND type = 'user';
    
    -- Get current week and month
    current_week := TO_CHAR(NOW(), 'YYYY-IW');
    current_month := TO_CHAR(NOW(), 'YYYY-MM');
    
    -- Get or create usage record
    SELECT * INTO usage_record
    FROM public.user_usage_tracking
    WHERE user_usage_tracking.user_id = can_user_perform_action.user_id 
    AND week_year = current_week 
    AND month_year = current_month;
    
    IF usage_record IS NULL THEN
        INSERT INTO public.user_usage_tracking (user_id, week_year, month_year)
        VALUES (can_user_perform_action.user_id, current_week, current_month);
        
        SELECT * INTO usage_record
        FROM public.user_usage_tracking
        WHERE user_usage_tracking.user_id = can_user_perform_action.user_id 
        AND week_year = current_week 
        AND month_year = current_month;
    END IF;
    
    -- Check specific action limits
    CASE action_type
        WHEN 'post' THEN
            IF user_tier = 'seed' THEN
                -- Check weekly post limit and content type
                IF usage_record.posts_this_week >= (plan_limits->>'posts_per_week')::INTEGER THEN
                    RETURN jsonb_build_object('allowed', false, 'reason', 'Weekly post limit reached', 'limit', plan_limits->>'posts_per_week');
                END IF;
                
                -- Check content type (seed can only post images)
                IF content_type = 'video' THEN
                    RETURN jsonb_build_object('allowed', false, 'reason', 'Video posts require Sapling tier or higher');
                END IF;
                
                RETURN jsonb_build_object('allowed', true);
            ELSE
                -- Sapling and Woodland have unlimited posts
                RETURN jsonb_build_object('allowed', true);
            END IF;
            
        WHEN 'business' THEN
            IF user_tier = 'seed' THEN
                -- Check if user already has a business
                IF EXISTS (SELECT 1 FROM businesses WHERE owner_id = user_id) THEN
                    RETURN jsonb_build_object('allowed', false, 'reason', 'Free tier allows only 1 business listing');
                END IF;
                RETURN jsonb_build_object('allowed', true);
            ELSE
                -- Paid tiers can have multiple businesses
                RETURN jsonb_build_object('allowed', true);
            END IF;
            
        WHEN 'job_event' THEN
            IF user_tier = 'seed' THEN
                IF usage_record.jobs_events_this_month >= (plan_limits->>'jobs_events_per_month')::INTEGER THEN
                    RETURN jsonb_build_object('allowed', false, 'reason', 'Monthly job/event limit reached', 'limit', plan_limits->>'jobs_events_per_month');
                END IF;
                RETURN jsonb_build_object('allowed', true);
            ELSIF user_tier = 'sapling' THEN
                IF usage_record.jobs_events_this_month >= 5 THEN
                    RETURN jsonb_build_object('allowed', false, 'reason', 'Monthly job/event limit reached', 'limit', 5);
                END IF;
                RETURN jsonb_build_object('allowed', true);
            ELSE -- woodland
                RETURN jsonb_build_object('allowed', true);
            END IF;
            
        WHEN 'connect' THEN
            IF user_tier = 'seed' THEN
                IF usage_record.connections_this_week >= (plan_limits->>'connections_per_week')::INTEGER THEN
                    RETURN jsonb_build_object('allowed', false, 'reason', 'Weekly connection limit reached', 'limit', plan_limits->>'connections_per_week');
                END IF;
                RETURN jsonb_build_object('allowed', true);
            ELSIF user_tier = 'sapling' THEN
                IF usage_record.connections_this_week >= 10 THEN
                    RETURN jsonb_build_object('allowed', false, 'reason', 'Weekly connection limit reached', 'limit', 10);
                END IF;
                RETURN jsonb_build_object('allowed', true);
            ELSE -- woodland
                RETURN jsonb_build_object('allowed', true);
            END IF;
            
        ELSE
            RETURN jsonb_build_object('allowed', true);
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment usage counter
CREATE OR REPLACE FUNCTION public.increment_usage_counter(
    action_type TEXT,
    user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
DECLARE
    current_week TEXT;
    current_month TEXT;
BEGIN
    current_week := TO_CHAR(NOW(), 'YYYY-IW');
    current_month := TO_CHAR(NOW(), 'YYYY-MM');
    
    -- Insert or update usage record
    INSERT INTO public.user_usage_tracking (user_id, week_year, month_year)
    VALUES (user_id, current_week, current_month)
    ON CONFLICT (user_id, week_year, month_year) DO NOTHING;
    
    -- Increment the appropriate counter
    CASE action_type
        WHEN 'post' THEN
            UPDATE public.user_usage_tracking
            SET posts_this_week = posts_this_week + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND week_year = current_week AND month_year = current_month;
            
        WHEN 'job_event' THEN
            UPDATE public.user_usage_tracking
            SET jobs_events_this_month = jobs_events_this_month + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND week_year = current_week AND month_year = current_month;
            
        WHEN 'connect' THEN
            UPDATE public.user_usage_tracking
            SET connections_this_week = connections_this_week + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND week_year = current_week AND month_year = current_month;
    END CASE;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's current usage and limits
CREATE OR REPLACE FUNCTION public.get_user_usage_summary(user_id UUID DEFAULT auth.uid())
RETURNS JSONB AS $$
DECLARE
    user_tier TEXT;
    current_week TEXT;
    current_month TEXT;
    usage_record RECORD;
    plan_limits JSONB;
    result JSONB;
BEGIN
    -- Get user's subscription tier
    SELECT subscription_tier INTO user_tier
    FROM public.profiles
    WHERE id = user_id;
    
    -- Get plan limits
    SELECT limits INTO plan_limits
    FROM public.subscription_plans
    WHERE name = user_tier AND type = 'user';
    
    -- Get current usage
    current_week := TO_CHAR(NOW(), 'YYYY-IW');
    current_month := TO_CHAR(NOW(), 'YYYY-MM');
    
    SELECT * INTO usage_record
    FROM public.user_usage_tracking
    WHERE user_usage_tracking.user_id = get_user_usage_summary.user_id 
    AND week_year = current_week 
    AND month_year = current_month;
    
    IF usage_record IS NULL THEN
        usage_record.posts_this_week := 0;
        usage_record.connections_this_week := 0;
        usage_record.jobs_events_this_month := 0;
    END IF;
    
    -- Build result
    result := jsonb_build_object(
        'tier', user_tier,
        'current_usage', jsonb_build_object(
            'posts_this_week', usage_record.posts_this_week,
            'connections_this_week', usage_record.connections_this_week,
            'jobs_events_this_month', usage_record.jobs_events_this_month
        ),
        'limits', plan_limits
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.can_user_perform_action(TEXT, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_user_perform_action(TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_user_perform_action(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.increment_usage_counter(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.increment_usage_counter(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_usage_summary(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_usage_summary() TO authenticated;

COMMIT;
