-- Enable RLS on both tables if not already enabled
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_relevant_industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.industries ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.businesses;
DROP POLICY IF EXISTS "Enable read access for service role" ON public.businesses;
DROP POLICY IF EXISTS "Enable write access for service role" ON public.businesses;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable write access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.industries;
DROP POLICY IF EXISTS "Enable read access for service role" ON public.industries;

-- Create policies for businesses table
CREATE POLICY "Enable read access for authenticated users"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable read access for service role"
    ON public.businesses
    FOR SELECT
    TO service_role
    USING (true);

CREATE POLICY "Enable write access for service role"
    ON public.businesses
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Create policies for business_relevant_industries table
CREATE POLICY "Enable read access for authenticated users"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable read access for service role"
    ON public.business_relevant_industries
    FOR SELECT
    TO service_role
    USING (true);

CREATE POLICY "Enable write access for service role"
    ON public.business_relevant_industries
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Create policies for industries table
CREATE POLICY "Enable read access for authenticated users"
    ON public.industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable read access for service role"
    ON public.industries
    FOR SELECT
    TO service_role
    USING (true);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;

GRANT SELECT ON public.businesses TO authenticated;
GRANT SELECT ON public.business_relevant_industries TO authenticated;
GRANT SELECT ON public.industries TO authenticated;

GRANT ALL ON public.businesses TO service_role;
GRANT ALL ON public.business_relevant_industries TO service_role;
GRANT ALL ON public.industries TO service_role;

-- Grant access to sequences
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- Verify permissions
DO $$
BEGIN
    -- Verify policies exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'businesses' 
        AND policyname = 'Enable read access for service role'
    ) THEN
        RAISE NOTICE 'Missing policy for businesses table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'business_relevant_industries' 
        AND policyname = 'Enable read access for service role'
    ) THEN
        RAISE NOTICE 'Missing policy for business_relevant_industries table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'industries' 
        AND policyname = 'Enable read access for service role'
    ) THEN
        RAISE NOTICE 'Missing policy for industries table';
    END IF;
END $$;
