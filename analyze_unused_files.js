#!/usr/bin/env node

/**
 * Analyze unused files in the NetZero Platform codebase
 * This script identifies files that are likely safe to remove for production
 */

const fs = require('fs');
const path = require('path');

// Files and directories to analyze
const analysisTargets = {
  // Debug and test files
  debugFiles: [
    'debug_signup_current_state.sql',
    'fix_signup_comprehensive.sql', 
    'test_account_deletion.sql',
    'test_signup_function.sql',
    'test_all_connection_functions.sql',
    'social_network_v2_schema.sql',
    'social_network_v2_functions.sql'
  ],
  
  // Documentation that's no longer needed
  docFiles: [
    'SIGNUP_PROCESS_ANALYSIS.md',
    'SIGNUP_PROCESS_COMPLETE_FIX.md', 
    'CONNECTIONS_DRAWER_FIXES_COMPLETE.md',
    'MIGRATION_PLAN_TO_NEW_SYSTEM.md',
    'FILES_TO_REMOVE_ARCHIVE.md'
  ],
  
  // Backup directories
  backupDirs: [
    'migration_backup_20250527_090856',
    'supabase/migrations_temp_backup',
    'scripts/database/fixes'
  ],
  
  // Old migration files (need manual review)
  oldMigrations: [
    'supabase/migrations/20250103000001_*',
    'supabase/migrations/20250103000002_*',
    // ... (pattern for old migrations)
  ]
};

// Check if file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Get file size
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

// Get directory size recursively
function getDirSize(dirPath) {
  let totalSize = 0;
  
  try {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        totalSize += getDirSize(filePath);
      } else {
        totalSize += stats.size;
      }
    }
  } catch (error) {
    // Directory doesn't exist or can't be read
  }
  
  return totalSize;
}

// Format bytes to human readable
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Analyze files
function analyzeFiles() {
  console.log('🔍 NetZero Platform - Unused Files Analysis\n');
  console.log('=' .repeat(60));
  
  let totalSizeToRemove = 0;
  let filesToRemove = [];
  
  // Check debug files
  console.log('\n📋 DEBUG & TEST FILES:');
  console.log('-'.repeat(40));
  
  for (const file of analysisTargets.debugFiles) {
    if (fileExists(file)) {
      const size = getFileSize(file);
      totalSizeToRemove += size;
      filesToRemove.push(file);
      console.log(`✅ ${file} (${formatBytes(size)})`);
    } else {
      console.log(`❌ ${file} (not found)`);
    }
  }
  
  // Check documentation files
  console.log('\n📚 DOCUMENTATION FILES:');
  console.log('-'.repeat(40));
  
  for (const file of analysisTargets.docFiles) {
    if (fileExists(file)) {
      const size = getFileSize(file);
      totalSizeToRemove += size;
      filesToRemove.push(file);
      console.log(`✅ ${file} (${formatBytes(size)})`);
    } else {
      console.log(`❌ ${file} (not found)`);
    }
  }
  
  // Check backup directories
  console.log('\n📦 BACKUP DIRECTORIES:');
  console.log('-'.repeat(40));
  
  for (const dir of analysisTargets.backupDirs) {
    if (fileExists(dir)) {
      const size = getDirSize(dir);
      totalSizeToRemove += size;
      filesToRemove.push(dir);
      console.log(`✅ ${dir}/ (${formatBytes(size)})`);
    } else {
      console.log(`❌ ${dir}/ (not found)`);
    }
  }
  
  // Check for old migration files
  console.log('\n🗄️  OLD MIGRATION FILES:');
  console.log('-'.repeat(40));
  
  try {
    const migrationDir = 'supabase/migrations';
    if (fileExists(migrationDir)) {
      const files = fs.readdirSync(migrationDir);
      const oldMigrations = files.filter(file => 
        file.startsWith('20250103') && file.endsWith('.sql')
      );
      
      console.log(`Found ${oldMigrations.length} old migration files:`);
      
      let migrationSize = 0;
      for (const migration of oldMigrations.slice(0, 10)) { // Show first 10
        const filePath = path.join(migrationDir, migration);
        const size = getFileSize(filePath);
        migrationSize += size;
        console.log(`  📄 ${migration} (${formatBytes(size)})`);
      }
      
      if (oldMigrations.length > 10) {
        console.log(`  ... and ${oldMigrations.length - 10} more files`);
      }
      
      console.log(`  📊 Total migration files size: ${formatBytes(migrationSize)}`);
      console.log(`  ⚠️  MANUAL REVIEW REQUIRED - Don't auto-delete migrations!`);
    }
  } catch (error) {
    console.log(`❌ Could not analyze migration files: ${error.message}`);
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 SUMMARY:');
  console.log('-'.repeat(40));
  console.log(`Files to remove: ${filesToRemove.length}`);
  console.log(`Total size to free: ${formatBytes(totalSizeToRemove)}`);
  
  // Generate removal script
  console.log('\n🗑️  REMOVAL COMMANDS:');
  console.log('-'.repeat(40));
  console.log('# Run these commands to remove unused files:');
  console.log('# (Review each file before deletion!)\n');
  
  for (const file of filesToRemove) {
    if (fs.statSync(file).isDirectory()) {
      console.log(`Remove-Item -Recurse -Force "${file}"`);
    } else {
      console.log(`Remove-Item -Force "${file}"`);
    }
  }
  
  console.log('\n⚠️  WARNING: Review each file before deletion!');
  console.log('⚠️  Keep any files that are still referenced in the codebase!');
  console.log('⚠️  Always backup before mass deletion!');
}

// Run the analysis
analyzeFiles();
