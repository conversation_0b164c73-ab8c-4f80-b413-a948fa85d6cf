-- Check if the fixed function was applied
-- Date: 2025-06-05

-- Check the current function definition to see if it was updated
SELECT 
    'function_check' as check_type,
    CASE 
        WHEN routine_definition LIKE '%user_consent_settings%' THEN 'Function creates consent settings'
        ELSE 'Function does NOT create consent settings'
    END as creates_consent,
    CASE 
        WHEN routine_definition LIKE '%notification_preferences%' THEN 'Function creates notification preferences'
        ELSE 'Function does NOT create notification preferences'
    END as creates_notifications,
    CASE 
        WHEN routine_definition LIKE '%email_notifications%' AND routine_definition LIKE '%user_consent_settings%' THEN 'Function has OLD structure (will fail)'
        ELSE 'Function structure looks OK'
    END as structure_check
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- Also get the first part of the function to see what it looks like
SELECT 
    'function_content' as check_type,
    LEFT(routine_definition, 1000) as function_start
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';
