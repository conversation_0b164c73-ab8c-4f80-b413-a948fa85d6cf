# ConnectionsDrawer Database Structure

## Overview
The ConnectionsDrawer component manages user connections in the Net Zero Platform, providing functionality for viewing, sending, and managing connection requests. This document outlines the database structure and views that power this feature.

## Database Types

### 1. `connection_status` Enum
Custom enum type that defines the possible states of a connection.

```sql
CREATE TYPE connection_status AS ENUM ('pending', 'accepted', 'rejected');
```

## Database Tables

### 1. `user_connections`
The core table that stores all connection relationships between users.

```sql
CREATE TABLE user_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id),
    connection_id UUID REFERENCES profiles(id),
    status connection_status CHECK (status IN ('pending', 'accepted', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, connection_id)
);
```

**Key Fields:**
- `user_id`: The ID of the user initiating the connection
- `connection_id`: The ID of the user being connected to
- `status`: The current state of the connection (using connection_status enum)
- `created_at`: Timestamp of when the connection was created
- `updated_at`: Timestamp of the last status update

### 2. `profiles`
Stores user profile information used in connection displays.

```sql
CREATE TABLE profiles (
    id UUID PRIMARY KEY,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    title TEXT,
    organization TEXT,
    profile_visibility BOOLEAN DEFAULT true,
    subscription_tier TEXT DEFAULT 'Free'
);
```

## Materialized Views

### 1. `mv_active_connections`
Shows all accepted connections for a user.

```sql
CREATE MATERIALIZED VIEW mv_active_connections AS
SELECT 
    uc.id,
    uc.user_id,
    uc.connection_id,
    uc.status,
    uc.created_at,
    uc.updated_at,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization
FROM user_connections uc
JOIN profiles p ON p.id = uc.connection_id
WHERE uc.status = 'accepted'::connection_status
AND uc.user_id = auth.uid();
```

### 2. `mv_pending_connection_requests`
Shows pending connection requests received by the user.

```sql
CREATE MATERIALIZED VIEW mv_pending_connection_requests AS
SELECT 
    uc.id,
    uc.user_id,
    uc.connection_id,
    uc.status,
    uc.created_at,
    uc.updated_at,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization
FROM user_connections uc
JOIN profiles p ON p.id = uc.user_id
WHERE uc.status = 'pending'::connection_status
AND uc.connection_id = auth.uid();
```

### 3. `mv_sent_connection_requests`
Shows connection requests sent by the user.

```sql
CREATE MATERIALIZED VIEW mv_sent_connection_requests AS
SELECT 
    uc.id,
    uc.user_id,
    uc.connection_id,
    uc.status,
    uc.created_at,
    uc.updated_at,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization
FROM user_connections uc
JOIN profiles p ON p.id = uc.connection_id
WHERE uc.status = 'pending'::connection_status
AND uc.user_id = auth.uid();
```

## Key Functions

### 1. `refresh_connection_views()`
Refreshes all materialized views to ensure data consistency.

```sql
CREATE OR REPLACE FUNCTION refresh_connection_views()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_active_connections;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_pending_connection_requests;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_sent_connection_requests;
END;
$$;
```

### 2. `respond_to_connection_request(request_id_param UUID, status_param TEXT)`
Handles responding to connection requests with proper enum type handling.

```sql
CREATE OR REPLACE FUNCTION respond_to_connection_request(
    request_id_param UUID,
    status_param TEXT
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    connection_data jsonb;
    target_user_id UUID;
    new_status connection_status;
BEGIN
    -- Validate status and convert to enum
    IF status_param = 'accepted' THEN
        new_status := 'accepted'::connection_status;
    ELSIF status_param = 'rejected' THEN
        new_status := 'rejected'::connection_status;
    ELSE
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid status'
        );
    END IF;

    -- Get the connection request and verify the user has permission to respond
    SELECT connection_id INTO target_user_id
    FROM user_connections
    WHERE id = request_id_param AND status = 'pending'::connection_status;

    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found'
        );
    END IF;

    -- Verify the responding user is the target of the request
    IF target_user_id != auth.uid() THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Unauthorized: You can only respond to requests sent to you'
        );
    END IF;

    -- Update connection status
    UPDATE user_connections
    SET status = new_status,
        updated_at = NOW()
    WHERE id = request_id_param
    RETURNING jsonb_build_object(
        'id', id,
        'user_id', user_id,
        'connection_id', connection_id,
        'status', status,
        'updated_at', updated_at
    ) INTO connection_data;

    RETURN jsonb_build_object(
        'success', true,
        'data', connection_data
    );
END;
$$;
```

### 3. `remove_connection(connection_id_param UUID)`
Handles the removal of connections between users.

```sql
CREATE OR REPLACE FUNCTION remove_connection(
    connection_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_id UUID;
    target_user_id UUID;
    target_connection_id UUID;
BEGIN
    -- Get the connection and verify the user has permission to remove it
    SELECT user_id, connection_id INTO target_user_id, target_connection_id
    FROM user_connections
    WHERE id = connection_id_param;

    -- Verify the user is either the sender or receiver of the connection
    IF target_user_id != auth.uid() AND target_connection_id != auth.uid() THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Unauthorized: You can only remove your own connections'
        );
    END IF;

    -- Delete the connection
    DELETE FROM user_connections
    WHERE id = connection_id_param
    RETURNING id INTO deleted_id;

    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object('id', deleted_id)
    );
END;
$$;
```

## Data Flow

1. **Loading Connections:**
   - When the drawer opens, it loads data from all three materialized views
   - The views are refreshed using `refresh_connection_views()`
   - Data is transformed to match the frontend interface

2. **Suggested Connections:**
   - Fetches up to 10 profiles that are:
     - Not the current user
     - Have visible profiles
     - Not already connected
     - Not in pending requests
     - Not in sent requests

3. **Connection Management:**
   - Sending requests: Creates new records in `user_connections` with status 'pending'::connection_status
   - Accepting/Rejecting: Updates status using the connection_status enum
   - Removing: Deletes records from `user_connections`
   - After each action, views are refreshed to maintain consistency

## Security Considerations

1. **Row Level Security (RLS):**
   - All views and tables have RLS policies
   - Users can only access their own connections
   - Connection removal requires ownership verification

2. **Data Privacy:**
   - Only visible profiles are shown in suggestions
   - Connection status is only visible to involved parties

## Performance Optimizations

1. **Materialized Views:**
   - Pre-computed results for faster access
   - Concurrent refresh to prevent blocking
   - Indexed for optimal query performance

2. **Connection Filtering:**
   - Efficient exclusion of existing connections
   - Limit on suggested connections (10)
   - Optimized queries using proper indexes

3. **Type Safety:**
   - Use of connection_status enum ensures data consistency
   - Proper type casting in all functions
   - Validation of status values before updates 