-- Test profile visibility settings
-- Run this in Supabase SQL Editor

-- 1. Check current profile visibility settings
SELECT 
    id,
    first_name,
    last_name,
    profile_visibility,
    created_at
FROM profiles
ORDER BY created_at DESC
LIMIT 10;

-- 2. Check what the public_professional_profiles view returns
-- (should only show profiles with profile_visibility = true)
SELECT 
    id,
    first_name,
    last_name,
    COUNT(*) OVER() as total_public_profiles
FROM public_professional_profiles
LIMIT 5;

-- 3. Check what SimpleProfessionals query returns
-- (should only show profiles with profile_visibility = true)
SELECT 
    id,
    first_name,
    last_name,
    profile_visibility
FROM profiles
WHERE profile_visibility = true
LIMIT 5;

-- 4. Test: Temporarily set a profile to private and verify it's hidden
-- (Don't run this unless you want to test with a specific profile)
-- UPDATE profiles SET profile_visibility = false WHERE id = 'YOUR_TEST_USER_ID';

-- 5. Count public vs private profiles
SELECT 
    profile_visibility,
    COUNT(*) as count
FROM profiles
GROUP BY profile_visibility
ORDER BY profile_visibility;
