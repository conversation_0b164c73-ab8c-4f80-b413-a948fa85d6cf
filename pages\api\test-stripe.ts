import { NextApiRequest, NextApiResponse } from 'next';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Create a simple checkout session for testing
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: 'price_1RWjHn4b3VXMPXJOwm9IH38e', // Your Sapling price ID
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${req.headers.origin}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.origin}/billing/cancel`,
      metadata: {
        tier: 'sapling',
        test: 'true',
      },
    });

    res.status(200).json({ 
      sessionId: session.id, 
      url: session.url,
      message: 'Stripe integration working!' 
    });
  } catch (error) {
    console.error('Stripe test error:', error);
    res.status(500).json({ 
      error: 'Stripe test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
