-- Create netzero_standards table
CREATE TABLE IF NOT EXISTS public.netzero_standards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category TEXT NOT NULL,
    name_of_standard_initiative TEXT NOT NULL,
    brief_description TEXT,
    type_of_standard TEXT,
    scope_organizational_geographic_focus TEXT,
    key_evidence_output TEXT,
    review_update_cycle TEXT,
    governing_body_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS on netzero_standards table
ALTER TABLE public.netzero_standards ENABLE ROW LEVEL SECURITY;

-- Anyone can view standards
CREATE POLICY "Anyone can view standards"
ON public.netzero_standards
FOR SELECT
USING (true);
