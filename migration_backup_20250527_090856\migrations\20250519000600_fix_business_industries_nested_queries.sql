-- Drop existing policies first
DROP POLICY IF EXISTS "Enable read access for service role on businesses" ON public.businesses;
DROP POLICY IF EXISTS "Enable read access for service role on business_relevant_industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for service role on industries" ON public.industries;

-- Create policies that explicitly allow joined/nested queries
CREATE POLICY "Enable read access for service role on businesses"
    ON public.businesses
    FOR SELECT 
    TO service_role
    USING (true);

CREATE POLICY "Enable read access for service role on business_relevant_industries"
    ON public.business_relevant_industries
    FOR SELECT 
    TO service_role
    USING (true);

CREATE POLICY "Enable read access for service role on industries"
    ON public.industries
    FOR SELECT 
    TO service_role
    USING (true);

-- Grant explicit permissions for the service role
GRANT SELECT ON public.businesses TO service_role;
GRANT SELECT ON public.business_relevant_industries TO service_role;
GRANT SELECT ON public.industries TO service_role;

-- Add policies for authenticated users
CREATE POLICY "Users can view all businesses"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their own businesses"
    ON public.businesses
    FOR ALL
    TO authenticated
    USING (owner_id = auth.uid())
    WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can view all business industries"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their business industries"
    ON public.business_relevant_industries
    FOR ALL
    TO authenticated
    USING (
        business_id IN (
            SELECT id FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can view all industries"
    ON public.industries
    FOR SELECT
    TO authenticated
    USING (true);

-- Grant necessary permissions to authenticated users
GRANT SELECT ON public.businesses TO authenticated;
GRANT SELECT ON public.business_relevant_industries TO authenticated;
GRANT SELECT ON public.industries TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.businesses TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.business_relevant_industries TO authenticated;

-- Verify Foreign Key Relationships
DO $$
BEGIN
    -- Check if the foreign key from business_relevant_industries to businesses exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc 
        JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
        WHERE tc.table_name = 'business_relevant_industries' 
        AND tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'businesses'
    ) THEN
        RAISE NOTICE 'Foreign key from business_relevant_industries to businesses is missing';
    END IF;

    -- Check if the foreign key from business_relevant_industries to industries exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc 
        JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
        WHERE tc.table_name = 'business_relevant_industries' 
        AND tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'industries'
    ) THEN
        RAISE NOTICE 'Foreign key from business_relevant_industries to industries is missing';
    END IF;
END
$$;
