-- Migration to check and fix the training_courses table schema
-- Make sure the training_courses table is in the public schema

-- First, check if table exists in 'public' schema and create if it doesn't
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename = 'training_courses'
    ) THEN
        -- Create the table in public schema if it doesn't exist
        CREATE TABLE public.training_courses (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', NOW()),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', NOW()),
            title TEXT NOT NULL,
            description TEXT,
            organization_name TEXT NOT NULL,
            delivery_method TEXT NOT NULL CHECK (delivery_method IN ('Online', 'InPerson', 'Hybrid')),
            location_address TEXT, 
            start_date DATE,
            end_date DATE,
            website_url TEXT,
            cost DECIMAL,
            status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Draft', 'Published')),
            creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            category_id UUID REFERENCES netzero_categories(id) ON DELETE SET NULL
        );

        -- Add RLS policies
        ALTER TABLE public.training_courses ENABLE ROW LEVEL SECURITY;

        -- Grant base table permissions
        GRANT ALL ON public.training_courses TO authenticated;
        GRANT SELECT ON public.training_courses TO anon;

        -- Policy to allow anyone to view published courses
        CREATE POLICY "View published training courses"
            ON public.training_courses
            FOR SELECT
            USING (status = 'Published' OR auth.uid() = creator_id);

        -- Policy to allow authenticated users to create courses
        CREATE POLICY "Users can create training courses"
            ON public.training_courses
            FOR INSERT
            TO authenticated
            WITH CHECK (auth.uid() = creator_id);

        -- Policy to allow course creators to update their own courses
        CREATE POLICY "Creators can update own training courses"
            ON public.training_courses
            FOR UPDATE
            TO authenticated
            USING (auth.uid() = creator_id)
            WITH CHECK (auth.uid() = creator_id);
            
        -- Policy to allow course creators to delete their own courses
        CREATE POLICY "Creators can delete own training courses"
            ON public.training_courses
            FOR DELETE
            TO authenticated
            USING (auth.uid() = creator_id);

        RAISE NOTICE 'Created public.training_courses table with RLS policies';
    ELSE
        RAISE NOTICE 'public.training_courses table already exists';
    END IF;

    -- Add any missing columns if table already exists
    -- (Add more ALTER TABLE statements here if columns need to be added/modified)
END $$;
