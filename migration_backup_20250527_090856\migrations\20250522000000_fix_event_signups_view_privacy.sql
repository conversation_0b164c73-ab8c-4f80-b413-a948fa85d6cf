-- Drop and recreate event_signups_with_users view without exposing emails
DROP VIEW IF EXISTS public.event_signups_with_users;

CREATE VIEW public.event_signups_with_users AS
  SELECT 
    es.*,
    -- Remove email field for privacy
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    p.avatar_url,
    p.title,
    p.organization
  FROM 
    public.event_signups es
  JOIN 
    auth.users au ON es.user_id = au.id
  LEFT JOIN 
    public.profiles p ON es.user_id = p.id;

-- Grant appropriate permissions on the view
ALTER VIEW public.event_signups_with_users OWNER TO postgres;
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.event_signups_with_users TO service_role;

-- If you need email access for legitimate purposes (like event organizers),
-- create a separate RPC function with proper access controls:
CREATE OR REPLACE FUNCTION get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
  user_id UUID,
  email TEXT
) 
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow event creators to access emails
  IF EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND created_by = auth.uid()
  ) THEN
    RETURN QUERY
    SELECT 
      es.user_id,
      au.email
    FROM 
      event_signups es
    JOIN 
      auth.users au ON es.user_id = au.id
    WHERE 
      es.event_id = event_id
      AND es.gdpr_consent = true;  -- Only include users who have given GDPR consent
  ELSE
    RAISE EXCEPTION 'Access denied: You must be the event creator to view emails';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION get_event_attendee_emails TO authenticated;
