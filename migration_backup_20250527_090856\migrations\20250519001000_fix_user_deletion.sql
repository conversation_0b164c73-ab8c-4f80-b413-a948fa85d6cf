-- Fix account deletion related issues

-- First, ensure we have the correct cascade deletes on all tables
ALTER TABLE IF EXISTS public.post_likes
    DROP CONSTRAINT IF EXISTS post_likes_user_id_fkey,
    ADD CONSTRAINT post_likes_user_id_fkey 
        FOREIGN KEY (user_id) 
        REFERENCES auth.users(id) 
        ON DELETE CASCADE;

ALTER TABLE IF EXISTS public.post_comments
    DROP CONSTRAINT IF EXISTS post_comments_user_id_fkey,
    ADD CONSTRAINT post_comments_user_id_fkey 
        FOREIGN KEY (user_id) 
        REFERENCES auth.users(id) 
        ON DELETE CASCADE;

ALTER TABLE IF EXISTS public.social_posts
    DROP CONSTRAINT IF EXISTS social_posts_user_id_fkey,
    ADD CONSTRAINT social_posts_user_id_fkey 
        FOREIGN KEY (user_id) 
        REFERENCES auth.users(id) 
        ON DELETE CASCADE;

ALTER TABLE IF EXISTS public.businesses
    DROP CONSTRAINT IF EXISTS businesses_owner_id_fkey,
    ADD CONSTRAINT businesses_owner_id_fkey 
        FOREIG<PERSON> KEY (owner_id) 
        REFERENCES auth.users(id) 
        ON DELETE CASCADE;

ALTER TABLE IF EXISTS public.profiles
    DROP CONSTRAINT IF EXISTS profiles_id_fkey,
    ADD CONSTRAINT profiles_id_fkey 
        FOREIGN KEY (id) 
        REFERENCES auth.users(id) 
        ON DELETE CASCADE;

-- Create a function to help diagnose deletion issues
CREATE OR REPLACE FUNCTION diagnose_user_deletion(user_id uuid)
RETURNS TABLE (
    table_name text,
    constraint_name text,
    foreign_key_column text,
    referenced_table text,
    referenced_column text,
    row_count bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH fk_info AS (
        SELECT
            tc.table_schema,
            tc.table_name,
            tc.constraint_name,
            kcu.column_name as foreign_key_column,
            ccu.table_schema AS referenced_schema,
            ccu.table_name AS referenced_table,
            ccu.column_name AS referenced_column
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
    )
    SELECT 
        fk.table_name::text,
        fk.constraint_name::text,
        fk.foreign_key_column::text,
        fk.referenced_table::text,
        fk.referenced_column::text,
        count(*)::bigint as row_count    FROM fk_info fk
    CROSS JOIN LATERAL (
        SELECT count(*) as row_count
        FROM (
            SELECT format(
                'SELECT count(*) FROM %I.%I WHERE %I = %L',
                fk.table_schema,
                fk.table_name,
                fk.foreign_key_column,
                user_id
            )
        ) q,
        LATERAL execute q.format INTO row_count
    ) counts
    WHERE fk.referenced_schema = 'auth'
        AND fk.referenced_table = 'users'
    GROUP BY 
        fk.table_name,
        fk.constraint_name,
        fk.foreign_key_column,
        fk.referenced_table,
        fk.referenced_column;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION diagnose_user_deletion TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION diagnose_user_deletion IS 'Diagnoses issues preventing user deletion by checking foreign key relationships and row counts.';

-- Create a function to cleanup orphaned user data
CREATE OR REPLACE FUNCTION cleanup_user_data(user_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Delete in proper order to respect foreign keys
    DELETE FROM public.post_likes pl WHERE pl.user_id = $1;
    DELETE FROM public.post_comments pc WHERE pc.user_id = $1;
    DELETE FROM public.social_posts sp WHERE sp.user_id = $1;
    DELETE FROM public.businesses b WHERE b.owner_id = $1;
    DELETE FROM public.profiles p WHERE p.id = $1;
    
    -- Note: auth.users deletion must be handled by the Edge Function
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION cleanup_user_data TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION cleanup_user_data IS 'Cleans up all user data in the correct order to respect foreign key constraints.';

-- Create a function to show all user data references
CREATE OR REPLACE FUNCTION show_user_data(target_user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result jsonb = '{}'::jsonb;
    auth_data jsonb;
    profile_data jsonb;
    posts_count int;
    comments_count int;
    likes_count int;
    business_count int;
BEGIN
    -- Get auth user data
    SELECT row_to_json(u)::jsonb INTO auth_data
    FROM auth.users u
    WHERE u.id = target_user_id;
    
    result = jsonb_set(result, '{auth_user}', coalesce(auth_data, 'null'::jsonb));
    
    -- Get profile data
    SELECT row_to_json(p)::jsonb INTO profile_data
    FROM profiles p
    WHERE p.id = target_user_id;
    
    result = jsonb_set(result, '{profile}', coalesce(profile_data, 'null'::jsonb));
    
    -- Get counts
    SELECT count(*) INTO posts_count
    FROM social_posts sp
    WHERE sp.user_id = target_user_id;
    
    SELECT count(*) INTO comments_count
    FROM post_comments pc
    WHERE pc.user_id = target_user_id;
    
    SELECT count(*) INTO likes_count
    FROM post_likes pl
    WHERE pl.user_id = target_user_id;
    
    SELECT count(*) INTO business_count
    FROM businesses b
    WHERE b.owner_id = target_user_id;
    
    result = jsonb_set(result, '{counts}', jsonb_build_object(
        'posts', posts_count,
        'comments', comments_count,
        'likes', likes_count,
        'businesses', business_count
    ));
    
    RETURN result;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION show_user_data TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION show_user_data IS 'Shows all data associated with a user ID to help diagnose deletion issues.';
