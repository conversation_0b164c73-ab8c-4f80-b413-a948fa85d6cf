# Enhanced Calendar Integration

## Overview

The `AddToCalendarDropdown` component provides a modern, user-friendly way for users to add events to their preferred calendar applications. Instead of just downloading an iCal file, users get multiple options for seamless calendar integration.

## Features

### 🎯 Multiple Calendar Options
- **Google Calendar** - Direct browser integration
- **Outlook Online** - Web-based Outlook
- **Outlook Office 365** - Enterprise Outlook
- **Yahoo Calendar** - Yahoo's calendar service
- **iCal Download** - Universal .ics file for other calendar apps

### 🔧 Smart Integration
- **Direct Links** - Opens calendar apps directly in the browser
- **Proper URL Encoding** - Handles special characters and formatting
- **Timezone Handling** - Converts dates to proper UTC format
- **Fallback Support** - iCal download for unsupported calendar apps

### 🎨 User Experience
- **Dropdown Interface** - Clean, organized selection
- **Visual Feedback** - Toast notifications for user actions
- **Error Handling** - Graceful fallbacks when links fail
- **Responsive Design** - Works on desktop and mobile

## Usage

```tsx
import AddToCalendarDropdown from '@/components/calendar/AddToCalendarDropdown';

// Basic usage
<AddToCalendarDropdown event={event} />

// With custom styling
<AddToCalendarDropdown 
  event={event} 
  variant="outline" 
  size="sm" 
  className="w-full" 
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `event` | `Event` | Required | Event object with date, time, location, etc. |
| `variant` | `'default' \| 'outline' \| 'ghost'` | `'outline'` | Button style variant |
| `size` | `'sm' \| 'default' \| 'lg'` | `'sm'` | Button size |
| `className` | `string` | `''` | Additional CSS classes |

## Event Object Requirements

The component expects an event object with these properties:

```typescript
interface Event {
  id: string;
  title: string;
  description?: string;
  start_date: string;     // YYYY-MM-DD format
  start_time: string;     // HH:MM format
  end_date?: string;      // YYYY-MM-DD format
  end_time?: string;      // HH:MM format
  event_type: 'in-person' | 'remote' | 'hybrid';
  physical_location?: string;
  meeting_url?: string;
}
```

## How It Works

### 1. Calendar URL Generation
Each calendar service has its own URL format for adding events:

- **Google Calendar**: Uses `calendar.google.com/calendar/render` with query parameters
- **Outlook**: Uses `outlook.live.com/calendar/0/deeplink/compose`
- **Yahoo**: Uses `calendar.yahoo.com` with specific parameters

### 2. Date Formatting
Dates are converted to the iCalendar format (YYYYMMDDTHHMMSSZ) for compatibility across all calendar services.

### 3. Location Handling
- **In-person events**: Uses `physical_location`
- **Remote events**: Uses `meeting_url`
- **Hybrid events**: Combines both location and URL

### 4. Fallback Strategy
If direct calendar links fail, users can always download the .ics file which works with virtually all calendar applications.

## Benefits Over Simple iCal Download

### ✅ Better User Experience
- No file downloads required for most users
- Instant calendar integration
- Familiar calendar interfaces

### ✅ Higher Adoption Rates
- Reduces friction in the signup process
- More users actually add events to their calendars
- Better event attendance rates

### ✅ Cross-Platform Support
- Works on desktop and mobile
- Supports all major calendar services
- Graceful degradation for unsupported apps

### ✅ Modern Web Standards
- Uses proper URL schemes for calendar integration
- Follows platform-specific conventions
- Optimized for each calendar service

## Implementation Notes

- The component uses the existing `generateICSFile` function as a fallback
- All calendar URLs open in new tabs for security
- Error handling provides user feedback via toast notifications
- The dropdown menu is accessible and keyboard-navigable

This enhanced calendar integration significantly improves the user experience compared to simple iCal file downloads, leading to better event engagement and attendance rates.
