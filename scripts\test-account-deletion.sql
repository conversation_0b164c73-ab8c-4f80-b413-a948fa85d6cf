-- Test script for account deletion functionality
-- This script helps verify that the account deletion process works correctly

-- ================================================================
-- PART 1: DIAGNOSTIC QUERIES
-- ================================================================

-- Check foreign key constraints for key tables
SELECT 
    'Foreign Key Constraints Check' as test_name,
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
LEFT JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name IN (
        'profiles', 'notification_preferences', 'user_consent_settings', 
        'event_signups', 'training_course_enrollments', 'social_posts',
        'post_comments', 'post_likes', 'businesses', 'events', 
        'user_connections', 'user_categories'
    )
    AND (ccu.table_name = 'users' OR ccu.table_name = 'profiles')
ORDER BY tc.table_name, tc.constraint_name;

-- ================================================================
-- PART 2: FUNCTION EXISTENCE CHECK
-- ================================================================

-- Check if cleanup function exists
SELECT 
    'Function Existence Check' as test_name,
    proname as function_name,
    pg_get_function_identity_arguments(oid) as arguments,
    CASE 
        WHEN proname = 'cleanup_user_data_safe' THEN 'FOUND'
        ELSE 'MISSING'
    END as status
FROM pg_proc 
WHERE proname IN ('cleanup_user_data_safe', 'diagnose_user_deletion_issues')
    AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- ================================================================
-- PART 3: EDGE FUNCTION TEST QUERY
-- ================================================================

-- Query to help test edge function (replace USER_ID_HERE with actual user ID)
/*
-- To test with a specific user, uncomment and replace USER_ID_HERE:

-- First, diagnose the user
SELECT * FROM public.diagnose_user_deletion_issues('USER_ID_HERE');

-- Then test cleanup (this will actually delete data!)
-- SELECT public.cleanup_user_data_safe('USER_ID_HERE');
*/

-- ================================================================
-- PART 4: SAMPLE USER DATA CHECK
-- ================================================================

-- Check for any users with data (for testing purposes)
SELECT 
    'Sample User Data Check' as test_name,
    p.id as user_id,
    p.first_name,
    p.last_name,
    (SELECT COUNT(*) FROM public.social_posts WHERE user_id = p.id) as post_count,
    (SELECT COUNT(*) FROM public.businesses WHERE owner_id = p.id) as business_count,
    (SELECT COUNT(*) FROM public.event_signups WHERE user_id = p.id) as event_signup_count,
    (SELECT COUNT(*) FROM public.user_connections WHERE user_id = p.id OR connection_id = p.id) as connection_count
FROM public.profiles p
WHERE EXISTS (
    SELECT 1 FROM auth.users au WHERE au.id = p.id
)
LIMIT 5;

-- ================================================================
-- PART 5: CASCADE DELETE VERIFICATION
-- ================================================================

-- Check which tables would be affected by deleting a user
-- (This doesn't actually delete anything, just shows what would be deleted)
CREATE OR REPLACE FUNCTION public.preview_user_deletion(target_user_id UUID)
RETURNS TABLE (
    table_name TEXT,
    record_count BIGINT,
    deletion_method TEXT
) AS $$
BEGIN
    -- Tables that should cascade from profiles deletion
    RETURN QUERY
    SELECT 'social_posts'::TEXT, COUNT(*)::BIGINT, 'CASCADE from profiles'::TEXT
    FROM public.social_posts WHERE user_id = target_user_id;
    
    RETURN QUERY
    SELECT 'post_comments'::TEXT, COUNT(*)::BIGINT, 'CASCADE from profiles'::TEXT
    FROM public.post_comments WHERE user_id = target_user_id;
    
    RETURN QUERY
    SELECT 'post_likes'::TEXT, COUNT(*)::BIGINT, 'CASCADE from profiles'::TEXT
    FROM public.post_likes WHERE user_id = target_user_id;
    
    RETURN QUERY
    SELECT 'businesses'::TEXT, COUNT(*)::BIGINT, 'CASCADE from profiles'::TEXT
    FROM public.businesses WHERE owner_id = target_user_id;
    
    RETURN QUERY
    SELECT 'user_connections'::TEXT, COUNT(*)::BIGINT, 'CASCADE from profiles'::TEXT
    FROM public.user_connections WHERE user_id = target_user_id OR connection_id = target_user_id;
    
    RETURN QUERY
    SELECT 'user_categories'::TEXT, COUNT(*)::BIGINT, 'CASCADE from profiles'::TEXT
    FROM public.user_categories WHERE user_id = target_user_id;
    
    RETURN QUERY
    SELECT 'user_consent_settings'::TEXT, COUNT(*)::BIGINT, 'CASCADE from profiles'::TEXT
    FROM public.user_consent_settings WHERE user_id = target_user_id;
    
    -- Tables that might reference auth.users directly
    RETURN QUERY
    SELECT 'event_signups'::TEXT, COUNT(*)::BIGINT, 'Direct auth.users reference'::TEXT
    FROM public.event_signups WHERE user_id = target_user_id;
    
    RETURN QUERY
    SELECT 'notification_preferences'::TEXT, 
           COALESCE((SELECT COUNT(*) FROM public.notification_preferences WHERE user_id = target_user_id), 0)::BIGINT,
           'Direct auth.users reference'::TEXT;
    
    RETURN QUERY
    SELECT 'training_course_enrollments'::TEXT,
           COALESCE(
               (SELECT COUNT(*) FROM public.training_course_enrollments WHERE user_id = target_user_id),
               (SELECT COUNT(*) FROM public.training_course_enrollments WHERE profile_id = target_user_id),
               0
           )::BIGINT,
           'Mixed reference'::TEXT;
    
    RETURN QUERY
    SELECT 'events'::TEXT,
           COALESCE(
               (SELECT COUNT(*) FROM public.events WHERE creator_user_id = target_user_id),
               (SELECT COUNT(*) FROM public.events WHERE owner_id = target_user_id),
               0
           )::BIGINT,
           'Mixed reference'::TEXT;
    
    RETURN QUERY
    SELECT 'profiles'::TEXT, COUNT(*)::BIGINT, 'CASCADE from auth.users'::TEXT
    FROM public.profiles WHERE id = target_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================================================
-- PART 6: INSTRUCTIONS
-- ================================================================

/*
USAGE INSTRUCTIONS:

1. First, run this entire script to check the current state of foreign keys and functions.

2. To test with a specific user:
   a. Find a test user ID from the "Sample User Data Check" results
   b. Run: SELECT * FROM public.diagnose_user_deletion_issues('USER_ID_HERE');
   c. Run: SELECT * FROM public.preview_user_deletion('USER_ID_HERE');
   d. If everything looks good, test the cleanup: SELECT public.cleanup_user_data_safe('USER_ID_HERE');

3. To test the full edge function:
   a. Use the frontend delete account button
   b. Check the edge function logs in Supabase dashboard
   c. Verify the user and all related data is deleted

4. To test Cloudflare media cleanup:
   a. Create a test user with profile avatar and social posts with images
   b. Delete the account
   c. Check that the images are removed from Cloudflare

IMPORTANT: Only test with non-production data!
*/

SELECT 'Account deletion test script loaded successfully' as status;
