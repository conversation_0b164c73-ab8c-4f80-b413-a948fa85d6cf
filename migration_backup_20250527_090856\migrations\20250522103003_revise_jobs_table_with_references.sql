-- Modify the jobs table to use reference tables

-- First drop the existing jobs table
DROP TABLE IF EXISTS public.jobs CASCADE;

-- Create the revised jobs table with foreign key references
CREATE TABLE public.jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    organization TEXT NOT NULL,
    description TEXT NOT NULL,
    salary_bracket salary_bracket NOT NULL,
    location_type job_location_type NOT NULL,
    region_id UUID NOT NULL REFERENCES public.regions(id),
    industry_id UUID NOT NULL REFERENCES public.industries(id),
    net_zero_category_id UUID NOT NULL REFERENCES public.net_zero_categories(id),
    job_function job_function NOT NULL,
    contract_type job_contract_type NOT NULL,
    hours_type job_hours_type NOT NULL,
    office_location TEXT,
    application_url TEXT NOT NULL,
    date_posted TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    CONSTRAINT valid_office_location CHECK (
        (location_type = 'Remote' AND office_location IS NULL) OR
        (location_type IN ('Hybrid', 'Office') AND office_location IS NOT NULL)
    )
);

-- Create RLS policies
ALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;

-- Anyone can read jobs
CREATE POLICY "Anyone can read jobs" ON public.jobs
    FOR SELECT USING (true);

-- Only authenticated users can create jobs
CREATE POLICY "Authenticated users can create jobs" ON public.jobs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Users can see their own jobs explicitly (for the dashboard)
CREATE POLICY "Users can see their own jobs" ON public.jobs
    FOR SELECT USING (auth.uid() = created_by);

-- Only the job creator can update or delete their jobs
CREATE POLICY "Users can update their own jobs" ON public.jobs
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own jobs" ON public.jobs
    FOR DELETE USING (auth.uid() = created_by);

-- Add indexes for common search patterns
CREATE INDEX jobs_date_posted_idx ON public.jobs(date_posted DESC);
CREATE INDEX jobs_region_id_idx ON public.jobs(region_id);
CREATE INDEX jobs_industry_id_idx ON public.jobs(industry_id);
CREATE INDEX jobs_net_zero_category_id_idx ON public.jobs(net_zero_category_id);
CREATE INDEX jobs_job_function_idx ON public.jobs(job_function);
CREATE INDEX jobs_location_type_idx ON public.jobs(location_type);
CREATE INDEX jobs_contract_type_idx ON public.jobs(contract_type);
