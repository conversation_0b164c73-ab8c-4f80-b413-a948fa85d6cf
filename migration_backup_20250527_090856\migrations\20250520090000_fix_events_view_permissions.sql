-- Add security definer view to fix permission issues with events_with_creator
DROP VIEW IF EXISTS public.events_with_creator;

CREATE OR REPLACE VIEW public.events_with_creator AS
WITH filtered_events AS (
    SELECT e.*
    FROM events e
    WHERE (COALESCE(e.status, 'upcoming') <> 'cancelled' 
    AND (NOT COALESCE(e.is_private, false) OR auth.uid() = e.owner_id))
)
SELECT 
    e.id,
    e.created_at,
    e.updated_at,
    e.title,
    e.description,
    e.start_date,
    e.end_date,
    e.physical_location,
    e.owner_id,
    e.image_url,
    e.status,
    e.capacity,
    e.is_private,
    e.category,
    e.industry,
    CONCAT(p.first_name, ' ', p.last_name) AS creator_name,
    p.avatar_url AS creator_avatar,
    (SELECT COUNT(*) FROM event_signups es WHERE es.event_id = e.id) AS signup_count
FROM 
    filtered_events e
LEFT JOIN 
    profiles p ON p.id = e.owner_id;

-- Grant necessary permissions
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;
