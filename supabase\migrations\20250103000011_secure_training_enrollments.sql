-- Secure training course enrollments by removing email exposure and implementing GDPR-compliant access
-- This follows the same pattern as the secure event attendee email access

BEGIN;

-- Step 1: Remove email from the view to prevent unauthorized access
DROP VIEW IF EXISTS public.training_course_enrollments_view;

-- Create secure view WITHOUT email exposure
CREATE OR REPLACE VIEW public.training_course_enrollments_view AS
SELECT
    e.id,
    e.course_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    e.gdpr_consent,
    e.completion_date,
    e.feedback,
    e.rating,
    e.certificate_issued,
    e.notes,
    CONCAT(p.first_name, ' ', p.last_name) AS user_name,
    p.avatar_url,
    p.title AS user_title,
    p.organization AS user_organization,
    -- REMOVED: p.email AS user_email (for security)
    t.title AS course_title,
    t.organization_name AS course_provider
FROM training_course_enrollments e
JOIN profiles p ON e.profile_id = p.id
JOIN training_courses t ON e.course_id = t.id;

-- Grant access to the secure view
GRANT SELECT ON public.training_course_enrollments_view TO authenticated;

-- Step 2: Create secure RPC function for course creator email access
-- Only course creators can access emails, and only for users who gave GDPR consent
CREATE OR REPLACE FUNCTION public.get_course_enrollment_emails(course_id UUID)
RETURNS TABLE (
    profile_id UUID,
    email TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID := auth.uid();
    course_exists BOOLEAN := false;
    is_course_creator BOOLEAN := false;
BEGIN
  -- Security Check 1: Ensure user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required to access enrollment emails';
  END IF;

  -- Security Check 2: Validate course exists and get creator info
  -- Note: Need to handle both auth.users and profiles references
  SELECT 
    true,
    CASE 
      WHEN tc.creator_id = current_user_id THEN true
      WHEN EXISTS (
        SELECT 1 FROM profiles p 
        WHERE p.id = tc.creator_id 
        AND p.id = current_user_id
      ) THEN true
      ELSE false
    END
  INTO 
    course_exists,
    is_course_creator
  FROM training_courses tc
  WHERE tc.id = course_id;

  -- Security Check 3: Course must exist
  IF NOT course_exists THEN
    RAISE EXCEPTION 'Course not found or access denied';
  END IF;

  -- Security Check 4: Only course creators can access emails
  IF NOT is_course_creator THEN
    RAISE EXCEPTION 'Access denied: You must be the course creator to view enrollment emails';
  END IF;

  -- Return emails only for users who have given GDPR consent
  RETURN QUERY
  SELECT 
    e.profile_id,
    p.email
  FROM 
    training_course_enrollments e
  JOIN 
    profiles p ON e.profile_id = p.id
  WHERE 
    e.course_id = get_course_enrollment_emails.course_id
    AND e.gdpr_consent = true
    AND p.email IS NOT NULL
    AND p.email != ''
  ORDER BY e.created_at;

EXCEPTION
  WHEN OTHERS THEN
    -- Log security violations if audit table exists
    INSERT INTO security_log (
      user_id,
      action,
      resource_id,
      error_message,
      created_at
    ) VALUES (
      current_user_id,
      'enrollment_email_access_denied',
      course_id,
      SQLERRM,
      NOW()
    ) ON CONFLICT DO NOTHING;
    
    -- Re-raise the exception
    RAISE;
END;
$$;

-- Grant minimal necessary permissions
GRANT EXECUTE ON FUNCTION public.get_course_enrollment_emails(UUID) TO authenticated;

-- Revoke from other roles to be explicit
REVOKE EXECUTE ON FUNCTION public.get_course_enrollment_emails(UUID) FROM anon;
REVOKE EXECUTE ON FUNCTION public.get_course_enrollment_emails(UUID) FROM public;

-- Step 3: Create debug function for troubleshooting
CREATE OR REPLACE FUNCTION public.debug_course_email_access(course_id UUID)
RETURNS TABLE (
    debug_info TEXT,
    profile_id UUID,
    email TEXT,
    gdpr_consent BOOLEAN,
    is_creator BOOLEAN,
    user_name TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID := auth.uid();
    is_course_creator BOOLEAN;
BEGIN
  -- Check if current user is the course creator
  SELECT EXISTS (
    SELECT 1 FROM training_courses 
    WHERE id = course_id 
    AND (creator_id = current_user_id OR 
         EXISTS (SELECT 1 FROM profiles WHERE id = creator_id AND id = current_user_id))
  ) INTO is_course_creator;

  -- Return debug information
  RETURN QUERY
  SELECT 
    CASE 
      WHEN is_course_creator THEN 'User is course creator'
      ELSE 'User is NOT course creator'
    END as debug_info,
    e.profile_id,
    p.email,
    e.gdpr_consent,
    is_course_creator,
    COALESCE(p.first_name || ' ' || p.last_name, 'Unknown') as user_name
  FROM 
    training_course_enrollments e
  JOIN 
    profiles p ON e.profile_id = p.id
  WHERE 
    e.course_id = debug_course_email_access.course_id;
END;
$$;

-- Grant access to the debug function
GRANT EXECUTE ON FUNCTION public.debug_course_email_access(UUID) TO authenticated;

-- Add security comments
COMMENT ON FUNCTION public.get_course_enrollment_emails(UUID) IS 
'SECURITY: Returns enrollment emails only for course creators and only for users who gave GDPR consent. Includes multiple security checks.';

COMMENT ON VIEW public.training_course_enrollments_view IS 
'SECURITY: Secure view that excludes email addresses. Use get_course_enrollment_emails() function for GDPR-compliant email access.';

COMMIT;

-- Verify the changes
SELECT 'Training course enrollment security implemented successfully' as status;
