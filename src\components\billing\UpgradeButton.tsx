import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface UpgradeButtonProps {
  tier: 'sapling' | 'woodland';
  currentTier: string;
  className?: string;
  children?: React.ReactNode;
}

export const UpgradeButton: React.FC<UpgradeButtonProps> = ({ 
  tier, 
  currentTier, 
  className,
  children 
}) => {
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const tierNames = {
    sapling: 'Sapling',
    woodland: 'Woodland'
  };

  const tierPrices = {
    sapling: '£5.99',
    woodland: '£10.99'
  };

  const handleUpgrade = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to upgrade your subscription.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    
    try {
      // Get the current session token
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      // Call your API to create checkout session
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({ 
          tier,
          type: 'user'
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL received');
      }
      
    } catch (error) {
      console.error('Upgrade failed:', error);
      toast({
        title: "Upgrade failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Don't show button if user is already on this tier or higher
  if (currentTier === tier) {
    return (
      <Button disabled className={className}>
        Current Plan
      </Button>
    );
  }

  // Check if user is on a higher tier
  const tierOrder = ['seed', 'sapling', 'woodland'];
  const currentTierIndex = tierOrder.indexOf(currentTier);
  const targetTierIndex = tierOrder.indexOf(tier);
  
  if (currentTierIndex > targetTierIndex) {
    return (
      <Button disabled className={className}>
        Downgrade to {tierNames[tier]}
      </Button>
    );
  }

  return (
    <Button
      onClick={handleUpgrade}
      disabled={loading}
      className={className}
    >
      {loading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Processing...
        </>
      ) : (
        children || `Upgrade to ${tierNames[tier]} (${tierPrices[tier]}/month)`
      )}
    </Button>
  );
};
