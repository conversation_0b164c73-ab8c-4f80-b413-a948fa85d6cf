-- Create a function to remove a connection
CREATE OR <PERSON><PERSON>LACE FUNCTION remove_connection(connection_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER -- Run with privileges of function creator
AS $$
DECLARE
  success BOOLEAN;
  updated_count INTEGER;
BEGIN
  -- Delete the connection
  DELETE FROM user_connections
  WHERE id = connection_id_param;
  
  -- Check if delete was successful
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  -- Return true if at least one row was deleted
  success := (updated_count > 0);
  
  RETURN success;
END;
$$;

-- Grant the delete permission to the service role and execute permission to authenticated users
GRANT DELETE ON user_connections TO service_role;
GRANT EXECUTE ON FUNCTION remove_connection TO authenticated;
