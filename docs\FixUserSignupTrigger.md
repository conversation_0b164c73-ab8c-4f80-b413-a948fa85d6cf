# Instructions to Fix Supabase User Registration

## The Problem
When users sign up, the application is failing with "Database error saving new user."
This is occurring because there is no trigger to automatically create a profile record for new users.

## Solution
We've created a SQL trigger that will automatically create a profile record whenever a new user registers.

## How to Fix

### Option 1: Run the SQL directly in Supabase Dashboard
1. Go to the [Supabase Dashboard](https://app.supabase.com/)
2. Select your project (psowjyllxqzllhbiyjtn)
3. Go to the SQL Editor
4. Copy and paste the content of the file `migrations/20250519000000_add_user_created_trigger.sql`
5. Run the SQL

### Option 2: Run via our script
1. Edit the `.env.fix-user-trigger` file and add your Supabase service role key
2. Run the following command:
   ```
   $env:VITE_SUPABASE_URL="https://psowjyllxqzllhbiyjtn.supabase.co"; $env:VITE_SUPABASE_SERVICE_ROLE_KEY="your-service-role-key-here"; npm run fix:user-trigger
   ```

## Verifying the Fix
After applying the fix, try registering a new user. The signup should work without errors.

## How It Works
The SQL creates a trigger function that runs whenever a new user is created in auth.users. 
The function automatically inserts a corresponding record into the profiles table with the same ID and user metadata (first name, last name).

## Troubleshooting
If you're still experiencing issues after applying the fix:
1. Check the Supabase logs for any errors
2. Verify the function and trigger exist in your database
3. Make sure the profiles table schema matches what the trigger is trying to insert
