-- Create function to handle post deletions and trigger edge function
CREATE OR R<PERSON>LACE FUNCTION handle_deleted_post_media()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the deleted post had an image
  IF OLD.media_type = 'image' AND OLD.media_url IS NOT NULL THEN
    -- Call edge function to delete the image from Cloudflare
    PERFORM net.http_post(
      url := CURRENT_SETTING('app.settings.edge_function_base_url') || '/delete-cloudflare-image',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || CURRENT_SETTING('app.settings.service_role_key')
      ),
      body := jsonb_build_object(
        'record', jsonb_build_object(
          'media_url', OLD.media_url,
          'media_type', OLD.media_type
        )
      )
    );
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS handle_deleted_post_media_trigger ON social_posts;

-- Create the trigger
CREATE TRIGGER handle_deleted_post_media_trigger
  AFTER DELETE ON social_posts
  FOR EACH ROW
  EXECUTE FUNCTION handle_deleted_post_media();

-- Add required settings if they don't exist
DO $$
BEGIN
  -- Set edge function base URL if not set
  IF NOT EXISTS (
    SELECT 1 FROM pg_settings WHERE name = 'app.settings.edge_function_base_url'
  ) THEN
    PERFORM set_config(
      'app.settings.edge_function_base_url',
      'https://' || CURRENT_SETTING('app.settings.project_ref') || '.functions.supabase.co',
      false
    );
  END IF;

  -- Set service role key if not set (this should be set manually in production)
  IF NOT EXISTS (
    SELECT 1 FROM pg_settings WHERE name = 'app.settings.service_role_key'
  ) THEN
    PERFORM set_config(
      'app.settings.service_role_key',
      'service-role-key-placeholder',
      false
    );
  END IF;
END $$;
