-- Fix user_categories RLS policies to ensure consistent access
-- Date: 2025-01-03

BEGIN;

-- Drop all existing user_categories policies to avoid conflicts
DROP POLICY IF EXISTS "Service role full access to user_categories" ON public.user_categories;
DROP POLICY IF EXISTS "Users can delete their own categories" ON public.user_categories;
DROP POLICY IF EXISTS "Users can insert their own categories" ON public.user_categories;
DROP POLICY IF EXISTS "Users can manage their categories" ON public.user_categories;
DROP POLICY IF EXISTS "Users can update their own categories" ON public.user_categories;
DROP POLICY IF EXISTS "Users can view all user categories" ON public.user_categories;
DROP POLICY IF EXISTS "Anyone can view user categories" ON public.user_categories;
DROP POLICY IF EXISTS "Authenticated users can view all user categories" ON public.user_categories;

-- Create simplified and consistent RLS policies
-- 1. Service role has full access
CREATE POLICY "Service role full access to user_categories" 
ON public.user_categories
FOR ALL 
TO service_role
USING (true)
WITH CHECK (true);

-- 2. Authenticated users can view all user categories (for community features)
CREATE POLICY "Authenticated users can view all user categories" 
ON public.user_categories
FOR SELECT 
TO authenticated
USING (true);

-- 3. Users can manage their own categories
CREATE POLICY "Users can manage their own categories" 
ON public.user_categories
FOR ALL 
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_categories TO authenticated;
GRANT SELECT ON public.user_categories TO anon;

-- Add helpful comments
COMMENT ON TABLE public.user_categories IS 'Junction table linking users to their selected net zero interest categories';

COMMIT;

-- Test the policies by checking if they exist
SELECT 
    tablename, 
    policyname,
    roles,
    cmd,
    permissive
FROM 
    pg_policies
WHERE 
    schemaname = 'public' AND 
    tablename = 'user_categories'
ORDER BY policyname;
