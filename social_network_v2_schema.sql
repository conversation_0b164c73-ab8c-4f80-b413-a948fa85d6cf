-- =====================================================
-- SOCIAL NETWORK V2: Clean, Purpose-Built System
-- =====================================================
-- Designed for: Social feed visibility + Activity notifications
-- NOT for: Direct messaging (no E2EE needed)

-- =====================================================
-- 1. SOCIAL CONNECTIONS TABLE (Clean Design)
-- =====================================================

CREATE TABLE IF NOT EXISTS public.social_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'connected', 'declined', 'blocked')),
    connected_at TIMESTAMPTZ NULL, -- When connection was accepted
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Ensure no duplicate connections
    UNIQUE(requester_id, recipient_id),
    -- Ensure users can't connect to themselves
    CHECK (requester_id != recipient_id)
);

-- =====================================================
-- 2. CONNECTION ACTIVITY FEED
-- =====================================================

CREATE TABLE IF NOT EXISTS public.connection_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL CHECK (activity_type IN (
        'profile_updated', 'post_created', 'event_created', 'event_joined',
        'business_created', 'achievement_earned', 'connection_made'
    )),
    activity_data JSONB NOT NULL DEFAULT '{}', -- Flexible data storage
    visibility TEXT NOT NULL DEFAULT 'connections' CHECK (visibility IN ('public', 'connections', 'private')),
    created_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 3. SOCIAL FEED PREFERENCES
-- =====================================================

CREATE TABLE IF NOT EXISTS public.social_feed_preferences (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    show_connection_posts BOOLEAN DEFAULT true,
    show_connection_events BOOLEAN DEFAULT true,
    show_connection_achievements BOOLEAN DEFAULT true,
    show_connection_businesses BOOLEAN DEFAULT true,
    feed_algorithm TEXT DEFAULT 'chronological' CHECK (feed_algorithm IN ('chronological', 'engagement', 'mixed')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 4. CONNECTION NOTIFICATIONS
-- =====================================================

CREATE TABLE IF NOT EXISTS public.connection_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recipient_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    actor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL CHECK (notification_type IN (
        'connection_request', 'connection_accepted', 'connection_activity'
    )),
    reference_id UUID, -- ID of the related object (post, event, etc.)
    reference_type TEXT, -- Type of the related object
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- =====================================================
-- 5. PERFORMANCE INDEXES
-- =====================================================

-- Social connections indexes
CREATE INDEX IF NOT EXISTS idx_social_connections_requester_status 
ON social_connections(requester_id, status);

CREATE INDEX IF NOT EXISTS idx_social_connections_recipient_status 
ON social_connections(recipient_id, status);

CREATE INDEX IF NOT EXISTS idx_social_connections_connected_at 
ON social_connections(connected_at DESC) WHERE status = 'connected';

-- Activity feed indexes
CREATE INDEX IF NOT EXISTS idx_connection_activities_user_created 
ON connection_activities(user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_connection_activities_type_created 
ON connection_activities(activity_type, created_at DESC);

-- Notification indexes
CREATE INDEX IF NOT EXISTS idx_connection_notifications_recipient_unread 
ON connection_notifications(recipient_id, is_read, created_at DESC);

-- =====================================================
-- 6. ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE social_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE connection_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_feed_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE connection_notifications ENABLE ROW LEVEL SECURITY;

-- Social connections policies
CREATE POLICY "Users can view their own connections" ON social_connections
    FOR SELECT TO authenticated
    USING (requester_id = auth.uid() OR recipient_id = auth.uid());

CREATE POLICY "Users can create connection requests" ON social_connections
    FOR INSERT TO authenticated
    WITH CHECK (requester_id = auth.uid());

CREATE POLICY "Users can update their connections" ON social_connections
    FOR UPDATE TO authenticated
    USING (requester_id = auth.uid() OR recipient_id = auth.uid());

-- Activity feed policies
CREATE POLICY "Users can view connection activities" ON connection_activities
    FOR SELECT TO authenticated
    USING (
        visibility = 'public' OR 
        user_id = auth.uid() OR
        (visibility = 'connections' AND EXISTS (
            SELECT 1 FROM social_connections sc 
            WHERE sc.status = 'connected' 
            AND ((sc.requester_id = auth.uid() AND sc.recipient_id = user_id) 
                 OR (sc.recipient_id = auth.uid() AND sc.requester_id = user_id))
        ))
    );

CREATE POLICY "Users can create their own activities" ON connection_activities
    FOR INSERT TO authenticated
    WITH CHECK (user_id = auth.uid());

-- Feed preferences policies
CREATE POLICY "Users can manage their feed preferences" ON social_feed_preferences
    FOR ALL TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Notification policies
CREATE POLICY "Users can view their notifications" ON connection_notifications
    FOR SELECT TO authenticated
    USING (recipient_id = auth.uid());

CREATE POLICY "Users can update their notifications" ON connection_notifications
    FOR UPDATE TO authenticated
    USING (recipient_id = auth.uid());

-- =====================================================
-- 7. HELPER FUNCTIONS
-- =====================================================

-- Function to check if two users are connected
CREATE OR REPLACE FUNCTION are_users_connected(user1_id UUID, user2_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM social_connections 
        WHERE status = 'connected' 
        AND ((requester_id = user1_id AND recipient_id = user2_id) 
             OR (requester_id = user2_id AND recipient_id = user1_id))
    );
END;
$$;

-- Drop existing function if it exists (to avoid return type conflicts)
DROP FUNCTION IF EXISTS get_connection_status(UUID, UUID);

-- Function to get connection status between users
CREATE OR REPLACE FUNCTION get_connection_status_v2(user1_id UUID, user2_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    connection_record RECORD;
BEGIN
    SELECT * INTO connection_record
    FROM social_connections
    WHERE (requester_id = user1_id AND recipient_id = user2_id)
       OR (requester_id = user2_id AND recipient_id = user1_id);

    IF NOT FOUND THEN
        RETURN 'none';
    END IF;

    -- Return status with direction info
    IF connection_record.requester_id = user1_id THEN
        RETURN connection_record.status || '_sent';
    ELSE
        RETURN connection_record.status || '_received';
    END IF;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION are_users_connected(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_connection_status_v2(UUID, UUID) TO authenticated;

-- =====================================================
-- 8. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_social_connections_updated_at 
    BEFORE UPDATE ON social_connections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_feed_preferences_updated_at 
    BEFORE UPDATE ON social_feed_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Set connected_at when status changes to connected
CREATE OR REPLACE FUNCTION set_connected_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'connected' AND OLD.status != 'connected' THEN
        NEW.connected_at = now();
    ELSIF NEW.status != 'connected' THEN
        NEW.connected_at = NULL;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_connected_at_trigger 
    BEFORE UPDATE ON social_connections 
    FOR EACH ROW EXECUTE FUNCTION set_connected_at();

-- Comments
COMMENT ON TABLE social_connections IS 'Clean social connections system for feed visibility and notifications';
COMMENT ON TABLE connection_activities IS 'Activity feed for connection-based social features';
COMMENT ON TABLE social_feed_preferences IS 'User preferences for social feed content';
COMMENT ON TABLE connection_notifications IS 'Notifications for connection-related activities';
