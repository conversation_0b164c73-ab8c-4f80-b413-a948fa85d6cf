-- Fix read permission for authenticated users on businesses table
BEGIN;

-- First check if the policy already exists
DO $$
BEGIN
    -- Try to drop the policy if it exists
    DROP POLICY IF EXISTS "Enable read access for all users" ON public.businesses;
EXCEPTION
    WHEN undefined_object THEN
        NULL;  -- Policy doesn't exist, which is fine
END $$;

-- Create policy to allow all users to read businesses
CREATE POLICY "Enable read access for all users"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

-- Enable RLS on the businesses table
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;

-- Grant explicit SELECT permission to authenticated users
GRANT SELECT ON public.businesses TO authenticated;

-- Verify the policy was created
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM
    pg_policies
WHERE
    tablename = 'businesses'
    AND policyname = 'Enable read access for all users';

COMMIT;
