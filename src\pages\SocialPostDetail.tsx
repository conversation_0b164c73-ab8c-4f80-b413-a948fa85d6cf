import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { fetchSocialPostById } from '@/lib/social';
import SocialFeedPost from '@/components/social/SocialFeedPost';
import { useSocialFeed } from '@/hooks/useSocialFeed';
import { useAuth } from '@/contexts/AuthContext';
import { Card } from '@/components/ui/card';

const SocialPostDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { session } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const [post, setPost] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const highlightCommentId = searchParams.get('highlight_comment');

  const {
    currentUser,
    handleToggleLike,
    handleAddComment,
    handleLikeComment,
    handleEditComment,
    handleDeleteComment,
    handleEditPost,
    handleDeletePost,
  } = useSocialFeed();

  useEffect(() => {
    const loadPost = async () => {
      if (!id) {
        setError('No post ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const postData = await fetchSocialPostById(id);
        
        if (!postData) {
          setError('Post not found');
          return;
        }

        setPost(postData);
      } catch (err) {
        console.error('Error loading post:', err);
        setError('Failed to load post');
        toast({
          title: 'Error',
          description: 'Failed to load post. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    loadPost();
  }, [id, toast]);

  // Handle highlighting specific comments
  useEffect(() => {
    if (!loading && highlightCommentId && post) {
      const timer = setTimeout(() => {
        const commentElement = document.getElementById(`comment-${highlightCommentId}`);

        if (commentElement) {
          commentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          commentElement.classList.add('ring-2', 'ring-primary', 'ring-opacity-50');

          // Remove highlight after 3 seconds
          setTimeout(() => {
            commentElement.classList.remove('ring-2', 'ring-primary', 'ring-opacity-50');
          }, 3000);
        }

        // Clear the URL parameter after highlighting
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete('highlight_comment');
        setSearchParams(newSearchParams, { replace: true });
      }, 1000); // Delay to ensure comments are rendered

      return () => clearTimeout(timer);
    }
  }, [loading, highlightCommentId, post, searchParams, setSearchParams]);

  const handleGoBack = () => {
    // Always go to social feed for better UX
    navigate('/social');
  };

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !post) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleGoBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>
          
          <Card className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Post Not Found</h2>
            <p className="text-muted-foreground mb-6">
              {error || 'The post you are looking for does not exist or has been removed.'}
            </p>
            <Button onClick={() => navigate('/social')}>
              Go to Social Feed
            </Button>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleGoBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <h1 className="text-xl font-semibold">Post Details</h1>
        </div>

        {session?.user && (
          <SocialFeedPost
            post={post}
            currentUserId={session.user.id}
            currentUserProfile={{
              name: currentUser.name,
              avatar: currentUser.avatar
            }}
            onLikePost={handleToggleLike}
            onCommentOnPost={handleAddComment}
            onLikeComment={handleLikeComment}
            onEditComment={handleEditComment}
            onDeleteComment={handleDeleteComment}
            onEditPost={handleEditPost}
            onDeletePost={handleDeletePost}
          />
        )}
      </div>
    </Layout>
  );
};

export default SocialPostDetail;
