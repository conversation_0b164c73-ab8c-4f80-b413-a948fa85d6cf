-- Fix recurring social media and profile visibility issues
-- This migration addresses problems that keep reoccurring
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- PART 1: FIX PROFILE VISIBILITY ISSUES
-- ================================================================

-- Ensure user_consent_settings table has profile_visibility column
ALTER TABLE public.user_consent_settings 
ADD COLUMN IF NOT EXISTS profile_visibility BOOLEAN DEFAULT true;

-- Ensure all users have user_consent_settings records
INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    created_at,
    updated_at
)
SELECT 
    p.id,
    true, -- Default to visible
    false, -- Default newsletter to false (will be updated by other migration)
    true,
    true,
    true,
    NOW(),
    NOW()
FROM public.profiles p
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_consent_settings ucs 
    WHERE ucs.user_id = p.id
)
ON CONFLICT (user_id) DO NOTHING;

-- Remove profile_visibility from profiles table if it exists
ALTER TABLE public.profiles DROP COLUMN IF EXISTS profile_visibility CASCADE;

-- ================================================================
-- PART 2: FIX SOCIAL MEDIA TABLE STRUCTURE
-- ================================================================

-- Fix post_likes table
DO $$
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'post_likes' AND table_schema = 'public') THEN
        -- If it has profile_id, rename to user_id
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'post_likes' AND column_name = 'profile_id' AND table_schema = 'public') THEN
            ALTER TABLE public.post_likes RENAME COLUMN profile_id TO user_id;
            RAISE NOTICE 'Renamed post_likes.profile_id to user_id';
        END IF;
        
        -- Ensure user_id references profiles(id)
        ALTER TABLE public.post_likes DROP CONSTRAINT IF EXISTS post_likes_user_id_fkey;
        ALTER TABLE public.post_likes DROP CONSTRAINT IF EXISTS post_likes_profile_id_fkey;
        ALTER TABLE public.post_likes 
        ADD CONSTRAINT post_likes_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed post_likes foreign key to reference profiles(id)';
    END IF;
END $$;

-- Fix comment_likes table
DO $$
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'comment_likes' AND table_schema = 'public') THEN
        -- If it has profile_id, rename to user_id
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comment_likes' AND column_name = 'profile_id' AND table_schema = 'public') THEN
            ALTER TABLE public.comment_likes RENAME COLUMN profile_id TO user_id;
            RAISE NOTICE 'Renamed comment_likes.profile_id to user_id';
        END IF;
        
        -- Ensure user_id references profiles(id)
        ALTER TABLE public.comment_likes DROP CONSTRAINT IF EXISTS comment_likes_user_id_fkey;
        ALTER TABLE public.comment_likes DROP CONSTRAINT IF EXISTS comment_likes_profile_id_fkey;
        ALTER TABLE public.comment_likes 
        ADD CONSTRAINT comment_likes_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed comment_likes foreign key to reference profiles(id)';
    END IF;
END $$;

-- Fix post_comments table
DO $$
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'post_comments' AND table_schema = 'public') THEN
        -- If it has profile_id, rename to user_id
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'post_comments' AND column_name = 'profile_id' AND table_schema = 'public') THEN
            ALTER TABLE public.post_comments RENAME COLUMN profile_id TO user_id;
            RAISE NOTICE 'Renamed post_comments.profile_id to user_id';
        END IF;
        
        -- Ensure user_id references profiles(id)
        ALTER TABLE public.post_comments DROP CONSTRAINT IF EXISTS post_comments_user_id_fkey;
        ALTER TABLE public.post_comments DROP CONSTRAINT IF EXISTS post_comments_profile_id_fkey;
        ALTER TABLE public.post_comments 
        ADD CONSTRAINT post_comments_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed post_comments foreign key to reference profiles(id)';
    END IF;
END $$;

-- ================================================================
-- PART 3: UPDATE VIEWS TO USE CORRECT PROFILE VISIBILITY
-- ================================================================

-- Update event_signups_with_users view
DROP VIEW IF EXISTS public.event_signups_with_users CASCADE;
CREATE VIEW public.event_signups_with_users AS
SELECT 
    es.id,
    es.event_id,
    es.user_id,
    es.created_at,
    es.updated_at,
    es.hide_attendance,
    es.gdpr_consent,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    COALESCE(ucs.profile_visibility, true) as profile_visibility,
    -- Create user object for compatibility
    jsonb_build_object(
        'id', p.id,
        'full_name', CONCAT(p.first_name, ' ', p.last_name),
        'first_name', p.first_name,
        'last_name', p.last_name,
        'avatar_url', p.avatar_url,
        'title', p.title,
        'organization', p.organization,
        'profile_visibility', COALESCE(ucs.profile_visibility, true)
    ) as user
FROM event_signups es
JOIN profiles p ON es.user_id = p.id
LEFT JOIN user_consent_settings ucs ON p.id = ucs.user_id;

-- Update training_course_enrollments_view
DROP VIEW IF EXISTS public.training_course_enrollments_view CASCADE;
CREATE OR REPLACE VIEW public.training_course_enrollments_view AS
SELECT
    e.id,
    e.course_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    e.gdpr_consent,
    e.completion_date,
    e.feedback,
    e.rating,
    e.certificate_issued,
    e.notes,
    CONCAT(p.first_name, ' ', p.last_name) AS user_name,
    p.avatar_url,
    p.title AS user_title,
    p.organization AS user_organization,
    COALESCE(ucs.profile_visibility, true) as profile_visibility,
    t.title AS course_title,
    t.organization_name AS course_provider
FROM training_course_enrollments e
JOIN profiles p ON e.profile_id = p.id
LEFT JOIN user_consent_settings ucs ON p.id = ucs.user_id
JOIN training_courses t ON e.course_id = t.id;

-- Grant permissions
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.training_course_enrollments_view TO authenticated;

-- Add comments
COMMENT ON VIEW public.event_signups_with_users IS 
'Secure view of event signups with user data. Uses profile_visibility from user_consent_settings table.';

COMMENT ON VIEW public.training_course_enrollments_view IS 
'Secure view of course enrollments with user data. Uses profile_visibility from user_consent_settings table.';

-- ================================================================
-- PART 4: FIX NOTIFICATION TRIGGERS
-- ================================================================

-- Fix notification functions that might be referencing profile_id instead of user_id
-- These functions are triggered when users like posts or comments

-- Fix notify_post_like function
CREATE OR REPLACE FUNCTION public.notify_post_like()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  post_owner_id UUID;
  should_notify BOOLEAN := true;
BEGIN
  -- Get post owner user_id from social_posts (social_posts uses user_id column)
  SELECT user_id INTO post_owner_id
  FROM public.social_posts
  WHERE id = NEW.post_id;

  -- Don't notify if user likes their own post
  IF post_owner_id = NEW.user_id THEN
    RETURN NEW;
  END IF;

  -- Check notification preferences if the table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_preferences' AND table_schema = 'public') THEN
    -- notification_preferences uses profile_id (as confirmed by your data)
    SELECT COALESCE(post_likes, true) INTO should_notify
    FROM public.notification_preferences
    WHERE profile_id = post_owner_id;
  END IF;

  -- Create notification if enabled and notifications table exists
  IF should_notify AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications' AND table_schema = 'public') THEN
    -- Notifications table uses profile_id (as confirmed by your data)
    -- Use ON CONFLICT DO NOTHING to handle duplicate notifications gracefully
    INSERT INTO public.notifications (profile_id, type, content, related_id, related_type, actor_id)
    VALUES (post_owner_id, 'post_like', 'liked your post', NEW.post_id, 'post', NEW.user_id)
    ON CONFLICT DO NOTHING;
  END IF;

  RETURN NEW;
END;
$$;

-- Fix notify_comment_like function
CREATE OR REPLACE FUNCTION public.notify_comment_like()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  comment_owner_id UUID;
  should_notify BOOLEAN := true;
BEGIN
  -- Get comment owner user_id from post_comments (post_comments uses user_id column)
  SELECT user_id INTO comment_owner_id
  FROM public.post_comments
  WHERE id = NEW.comment_id;

  -- Don't notify if user likes their own comment
  IF comment_owner_id = NEW.user_id THEN
    RETURN NEW;
  END IF;

  -- Check notification preferences if the table exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_preferences' AND table_schema = 'public') THEN
    -- notification_preferences uses profile_id (as confirmed by your data)
    SELECT COALESCE(comment_likes, true) INTO should_notify
    FROM public.notification_preferences
    WHERE profile_id = comment_owner_id;
  END IF;

  -- Create notification if enabled and notifications table exists
  IF should_notify AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications' AND table_schema = 'public') THEN
    -- Notifications table uses profile_id (as confirmed by your data)
    -- Use ON CONFLICT DO NOTHING to handle duplicate notifications gracefully
    INSERT INTO public.notifications (profile_id, type, content, related_id, related_type, actor_id)
    VALUES (comment_owner_id, 'comment_like', 'liked your comment', NEW.comment_id, 'comment', NEW.user_id)
    ON CONFLICT DO NOTHING;
  END IF;

  RETURN NEW;
END;
$$;

-- Ensure triggers are properly connected to the updated functions
DROP TRIGGER IF EXISTS post_like_notification_trigger ON public.post_likes;
CREATE TRIGGER post_like_notification_trigger
  AFTER INSERT ON public.post_likes
  FOR EACH ROW
  EXECUTE FUNCTION notify_post_like();

DROP TRIGGER IF EXISTS comment_like_notification_trigger ON public.comment_likes;
CREATE TRIGGER comment_like_notification_trigger
  AFTER INSERT ON public.comment_likes
  FOR EACH ROW
  EXECUTE FUNCTION notify_comment_like();

-- Fix any comment creation notification triggers that might exist
-- These might be trying to access profile_id in post_comments table

-- Check if there's a notify_comment_creation function and fix it
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'notify_comment_creation') THEN
    -- Fix the function to use user_id instead of profile_id
    CREATE OR REPLACE FUNCTION public.notify_comment_creation()
    RETURNS TRIGGER
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $func$
    DECLARE
      post_owner_id UUID;
      should_notify BOOLEAN := true;
    BEGIN
      -- Get post owner user_id from social_posts
      SELECT user_id INTO post_owner_id
      FROM public.social_posts
      WHERE id = NEW.post_id;

      -- Don't notify if user comments on their own post
      IF post_owner_id = NEW.user_id THEN
        RETURN NEW;
      END IF;

      -- Check notification preferences if the table exists
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_preferences' AND table_schema = 'public') THEN
        SELECT COALESCE(post_comments, true) INTO should_notify
        FROM public.notification_preferences
        WHERE profile_id = post_owner_id;
      END IF;

      -- Create notification if enabled
      IF should_notify AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications' AND table_schema = 'public') THEN
        INSERT INTO public.notifications (profile_id, type, content, related_id, related_type, actor_id)
        VALUES (post_owner_id, 'post_comment', 'commented on your post', NEW.post_id, 'post', NEW.user_id)
        ON CONFLICT DO NOTHING;
      END IF;

      RETURN NEW;
    END;
    $func$;

    RAISE NOTICE 'Fixed notify_comment_creation function to use user_id';
  END IF;
END $$;

-- Check if there's a comment creation trigger and recreate it
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'comment_notification_trigger') THEN
    DROP TRIGGER IF EXISTS comment_notification_trigger ON public.post_comments;
    CREATE TRIGGER comment_notification_trigger
      AFTER INSERT ON public.post_comments
      FOR EACH ROW
      EXECUTE FUNCTION notify_comment_creation();

    RAISE NOTICE 'Recreated comment_notification_trigger';
  END IF;
END $$;

-- Fix any other notification functions that might reference profile_id
-- Check for any functions that might be triggered on post_comments insert

DO $$
DECLARE
    func_record RECORD;
BEGIN
    -- Find all functions that might be triggered on post_comments
    FOR func_record IN
        SELECT DISTINCT t.tgname, p.proname
        FROM pg_trigger t
        JOIN pg_proc p ON t.tgfoid = p.oid
        JOIN pg_class c ON t.tgrelid = c.oid
        WHERE c.relname = 'post_comments'
        AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
    LOOP
        RAISE NOTICE 'Found trigger % calling function % on post_comments', func_record.tgname, func_record.proname;

        -- Fix common notification functions that might reference profile_id
        IF func_record.proname IN ('notify_post_comment', 'handle_comment_notification', 'create_comment_notification') THEN
            EXECUTE format('
                CREATE OR REPLACE FUNCTION public.%I()
                RETURNS TRIGGER
                LANGUAGE plpgsql
                SECURITY DEFINER
                AS $func$
                DECLARE
                  post_owner_id UUID;
                  should_notify BOOLEAN := true;
                BEGIN
                  -- Get post owner user_id from social_posts
                  SELECT user_id INTO post_owner_id
                  FROM public.social_posts
                  WHERE id = NEW.post_id;

                  -- Don''t notify if user comments on their own post
                  IF post_owner_id = NEW.user_id THEN
                    RETURN NEW;
                  END IF;

                  -- Check notification preferences if the table exists
                  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = ''notification_preferences'' AND table_schema = ''public'') THEN
                    SELECT COALESCE(post_comments, true) INTO should_notify
                    FROM public.notification_preferences
                    WHERE profile_id = post_owner_id;
                  END IF;

                  -- Create notification if enabled
                  IF should_notify AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = ''notifications'' AND table_schema = ''public'') THEN
                    INSERT INTO public.notifications (profile_id, type, content, related_id, related_type, actor_id)
                    VALUES (post_owner_id, ''post_comment'', ''commented on your post'', NEW.post_id, ''post'', NEW.user_id)
                    ON CONFLICT DO NOTHING;
                  END IF;

                  RETURN NEW;
                END;
                $func$;
            ', func_record.proname);

            RAISE NOTICE 'Fixed function % to use user_id instead of profile_id', func_record.proname;
        END IF;
    END LOOP;
END $$;

-- ================================================================
-- PART 5: FIX NOTIFICATION DISPLAY NAMES
-- ================================================================

-- Update notifications_with_details view to properly show actor names
DROP VIEW IF EXISTS public.notifications_with_details CASCADE;
CREATE VIEW public.notifications_with_details AS
SELECT
  n.id,
  n.profile_id,
  n.type,
  n.content,
  n.related_id,
  n.related_type,
  n.actor_id,
  n.is_read,
  n.created_at,
  n.updated_at,
  n.expires_at,
  -- Actor details with better name handling
  actor_profile.first_name as actor_first_name,
  actor_profile.last_name as actor_last_name,
  actor_profile.avatar_url as actor_avatar_url,
  -- Improved actor display name logic
  CASE
    WHEN actor_profile.first_name IS NOT NULL AND actor_profile.last_name IS NOT NULL THEN
      CONCAT(actor_profile.first_name, ' ', actor_profile.last_name)
    WHEN actor_profile.first_name IS NOT NULL THEN
      actor_profile.first_name
    WHEN actor_profile.last_name IS NOT NULL THEN
      actor_profile.last_name
    WHEN actor_profile.name IS NOT NULL THEN
      actor_profile.name
    ELSE 'Someone'
  END as actor_display_name,
  -- Related object details (basic)
  CASE
    WHEN n.related_type = 'post' THEN (
      SELECT LEFT(content, 100) FROM public.social_posts WHERE id = n.related_id
    )
    WHEN n.related_type = 'event' THEN (
      SELECT title FROM public.events WHERE id = n.related_id
    )
    WHEN n.related_type = 'comment' THEN (
      SELECT LEFT(content, 100) FROM public.post_comments WHERE id = n.related_id
    )
    ELSE NULL
  END as related_preview
FROM
  public.notifications n
  LEFT JOIN public.profiles actor_profile ON n.actor_id = actor_profile.id;

-- Grant permissions on the updated view
GRANT SELECT ON public.notifications_with_details TO authenticated;

-- Add comment to the view
COMMENT ON VIEW public.notifications_with_details IS
'Enhanced notifications view with proper actor name handling and related object previews';

-- ================================================================
-- PART 6: VERIFICATION
-- ================================================================

-- Log the current state for verification
DO $$
DECLARE
    post_likes_structure TEXT;
    comment_likes_structure TEXT;
    post_comments_structure TEXT;
    profile_visibility_count INTEGER;
    notification_count INTEGER;
BEGIN
    -- Check post_likes structure
    SELECT string_agg(column_name, ', ' ORDER BY ordinal_position) INTO post_likes_structure
    FROM information_schema.columns
    WHERE table_name = 'post_likes' AND table_schema = 'public';

    -- Check comment_likes structure
    SELECT string_agg(column_name, ', ' ORDER BY ordinal_position) INTO comment_likes_structure
    FROM information_schema.columns
    WHERE table_name = 'comment_likes' AND table_schema = 'public';

    -- Check post_comments structure
    SELECT string_agg(column_name, ', ' ORDER BY ordinal_position) INTO post_comments_structure
    FROM information_schema.columns
    WHERE table_name = 'post_comments' AND table_schema = 'public';

    -- Check user_consent_settings with profile_visibility
    SELECT COUNT(*) INTO profile_visibility_count
    FROM user_consent_settings
    WHERE profile_visibility IS NOT NULL;

    -- Check notifications with proper actor names
    SELECT COUNT(*) INTO notification_count
    FROM notifications_with_details
    WHERE actor_display_name != 'Someone';

    RAISE NOTICE 'VERIFICATION RESULTS:';
    RAISE NOTICE 'post_likes columns: %', post_likes_structure;
    RAISE NOTICE 'comment_likes columns: %', comment_likes_structure;
    RAISE NOTICE 'post_comments columns: %', post_comments_structure;
    RAISE NOTICE 'Users with profile_visibility settings: %', profile_visibility_count;
    RAISE NOTICE 'Notifications with proper actor names: %', notification_count;
END $$;

COMMIT;
