-- Clean up old backup tables and ensure schema consistency
-- This removes any old backup tables that might be causing confusion

BEGIN;

-- Drop any old backup tables that might exist
DROP TABLE IF EXISTS public.training_course_enrollments_backup CASCADE;
DROP TABLE IF EXISTS public.training_course_enrollments_old CASCADE;
DROP TABLE IF EXISTS public.training_course_enrollments_bak CASCADE;

-- Drop any old views that might have incorrect schemas
DROP VIEW IF EXISTS public.training_course_enrollments_view_1 CASCADE;
DROP VIEW IF EXISTS public.training_course_enrollments_view_old CASCADE;

-- Drop any old functions that might reference incorrect table names
DROP FUNCTION IF EXISTS public.increment_post_comment_count(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.decrement_post_comment_count(UUID) CASCADE;

COMMIT;

-- Verify the cleanup
SELECT 'Old backup tables and views cleaned up' as status;
