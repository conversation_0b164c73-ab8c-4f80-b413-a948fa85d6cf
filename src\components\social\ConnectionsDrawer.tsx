// ConnectionsDrawer component - Now using Social Network V2 system
// Modern, fast, LinkedIn-style connections management

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Users } from "lucide-react";
import { SocialNetworkV2 } from "@/components/social/SocialNetworkV2Production";

export interface ConnectionsDrawerProps {
  defaultOpen?: boolean;
  initialTab?: string;
  children?: React.ReactNode;
}

export function ConnectionsDrawer({ 
  defaultOpen = false, 
  initialTab = "connections",
  children 
}: ConnectionsDrawerProps) {
  const [open, setOpen] = useState(defaultOpen);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        {children || (
          <Button variant="outline" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Connections
          </Button>
        )}
      </SheetTrigger>
      
      <SheetContent className="w-full sm:max-w-4xl overflow-y-auto">
        <SheetHeader className="flex flex-row items-center justify-between">
          <div>
            <SheetTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Professional Connections
            </SheetTitle>
            <SheetDescription>
              Manage your professional network and discover new opportunities
            </SheetDescription>
          </div>
        </SheetHeader>
        
        <div className="mt-6">
          <SocialNetworkV2 
            defaultTab={initialTab}
            showStats={true}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}
