-- Investigate how unauthorized email consent was set
-- Focus on the 3 users with share_email_with_attendees = true

-- 1. Check if there's a UI component setting these values
-- Look for any functions that might set attendee email sharing to TRUE

-- 2. Check the update_user_consent_settings function
SELECT 
    'update_user_consent_settings function:' as info,
    pg_get_functiondef('public.update_user_consent_settings'::regproc) as function_definition;

-- 3. Check if there are any other consent-related functions
SELECT 
    'All consent-related functions:' as info,
    p.proname as function_name,
    pg_get_function_arguments(p.oid) as arguments
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND (
    p.proname ILIKE '%consent%'
    OR pg_get_functiondef(p.oid) ILIKE '%share_email_with_attendees%'
);

-- 4. Check if there are any default values being set incorrectly
SELECT 
    'Table defaults for user_consent_settings:' as info,
    column_name,
    column_default,
    is_nullable,
    data_type
FROM information_schema.columns
WHERE table_name = 'user_consent_settings'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 5. Look for any triggers on user_consent_settings
SELECT 
    'Triggers on user_consent_settings:' as info,
    t.tgname as trigger_name,
    pg_get_triggerdef(t.oid) as trigger_definition
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
AND c.relname = 'user_consent_settings';

-- 6. Check the profiles of these specific users to see if there's a pattern
SELECT 
    'Problematic users profile info:' as info,
    p.id,
    p.first_name,
    p.last_name,
    p.email,
    p.created_at as profile_created,
    ucs.created_at as consent_created,
    ucs.updated_at as consent_updated,
    ucs.share_email_with_event_creators,
    ucs.share_email_with_attendees,
    ucs.share_contact_details
FROM public.profiles p
JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
WHERE ucs.user_id IN (
    '85d461cb-7d8f-499e-92a4-96d66b4887ee',
    '39a620c4-9fe2-4dac-9891-900b600e0db3',
    '2fb6ba90-d692-4d3f-a43f-15c9e8e0e0ee'
);

-- 7. Check if these users have any event signups that might have triggered this
SELECT 
    'Event signups for problematic users:' as info,
    es.user_id,
    es.event_id,
    es.gdpr_consent,
    es.created_at as signup_date,
    e.title as event_title
FROM public.event_signups es
JOIN public.events e ON es.event_id = e.id
WHERE es.user_id IN (
    '85d461cb-7d8f-499e-92a4-96d66b4887ee',
    '39a620c4-9fe2-4dac-9891-900b600e0db3',
    '2fb6ba90-d692-4d3f-a43f-15c9e8e0e0ee'
)
ORDER BY es.created_at DESC;

-- 8. Check if there's a pattern in the timestamps
SELECT 
    'Consent setting patterns:' as info,
    DATE_TRUNC('day', updated_at) as update_date,
    COUNT(*) as users_updated,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as attendee_consent_enabled,
    COUNT(CASE WHEN share_contact_details = true THEN 1 END) as contact_details_enabled
FROM public.user_consent_settings
GROUP BY DATE_TRUNC('day', updated_at)
ORDER BY update_date DESC;

-- 9. Check the notification_system_health view to see why the count is wrong
SELECT 
    'Recount attendee email consent:' as info,
    COUNT(*) as total_users,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as attendee_email_true_count,
    COUNT(CASE WHEN share_email_with_attendees = false THEN 1 END) as attendee_email_false_count,
    COUNT(CASE WHEN share_email_with_attendees IS NULL THEN 1 END) as attendee_email_null_count
FROM public.user_consent_settings;
