-- First, validate that all tables exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'business_relevant_industries') THEN
        RAISE EXCEPTION 'Table business_relevant_industries does not exist!';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'industries') THEN
        RAISE EXCEPTION 'Table industries does not exist!';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'businesses') THEN
        RAISE EXCEPTION 'Table businesses does not exist!';
    END IF;
END $$;

-- Check that the foreign keys are correctly set up
SELECT 
    tc.table_schema, 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name = 'business_relevant_industries';

-- Check if any business_relevant_industries rows exist
SELECT COUNT(*) FROM business_relevant_industries;

-- Create temp function for test
CREATE OR REPLACE FUNCTION test_nested_query() RETURNS TABLE (
    business_id UUID, 
    business_name TEXT, 
    industry_id UUID, 
    industry_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.id AS business_id, 
        b.name AS business_name, 
        i.id AS industry_id, 
        i.name AS industry_name
    FROM 
        businesses b
    LEFT JOIN 
        business_relevant_industries bri ON b.id = bri.business_id
    LEFT JOIN 
        industries i ON bri.industry_id = i.id
    LIMIT 10;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test if an explicit join works
SELECT * FROM test_nested_query();

-- Display current policies
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM
    pg_policies
WHERE
    tablename IN ('businesses', 'business_relevant_industries', 'industries');

-- Drop the temp function
DROP FUNCTION IF EXISTS test_nested_query();

-- Add special service role policy for nested queries
CREATE POLICY "Service role nested query policy" ON public.business_relevant_industries
    FOR SELECT 
    TO service_role
    USING (true);
    
CREATE POLICY "Service role nested query policy for businesses" ON public.businesses
    FOR SELECT 
    TO service_role
    USING (true);
    
CREATE POLICY "Service role nested query policy for industries" ON public.industries
    FOR SELECT 
    TO service_role
    USING (true);

-- Make sure service role has all needed permissions
GRANT SELECT ON public.businesses TO service_role;
GRANT SELECT ON public.business_relevant_industries TO service_role;
GRANT SELECT ON public.industries TO service_role;
