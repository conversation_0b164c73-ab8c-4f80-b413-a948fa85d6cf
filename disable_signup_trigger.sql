-- Temporarily disable the signup trigger to test if that's the issue
-- This will help us isolate whether the problem is with the trigger or something else

-- Disable the handle_new_user trigger
ALTER TABLE auth.users DISABLE TRIGGER on_auth_user_created;

-- Also disable the welcome notification trigger in case that's causing issues
ALTER TABLE auth.users DISABLE TRIGGER create_welcome_notification_trigger;

-- Check which triggers are now disabled
SELECT 
    t.tgname as trigger_name,
    t.tgenabled as enabled,
    c.relname as table_name,
    p.proname as function_name
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE c.relname = 'users' 
AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'auth')
AND t.tgname IN ('on_auth_user_created', 'create_welcome_notification_trigger');

SELECT 'Signup triggers disabled - test signup now' as status;
