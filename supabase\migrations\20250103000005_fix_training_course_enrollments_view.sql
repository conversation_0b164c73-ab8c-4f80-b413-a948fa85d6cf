-- Fix training_course_enrollments_view to ensure avatar_url column exists
-- This addresses the column not found error

BEGIN;

-- Drop the existing view if it exists
DROP VIEW IF EXISTS public.training_course_enrollments_view CASCADE;

-- Recreate the view with explicit column selection to ensure all columns exist
-- Note: The table only has profile_id, not user_id
CREATE OR REPLACE VIEW public.training_course_enrollments_view AS
SELECT
    e.id,
    e.course_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    e.gdpr_consent,
    e.completion_date,
    e.feedback,
    e.rating,
    e.certificate_issued,
    e.notes,
    CONCAT(p.first_name, ' ', p.last_name) AS user_name,
    p.avatar_url,
    p.title AS user_title,
    p.organization AS user_organization,
    p.email AS user_email,
    t.title AS course_title,
    t.organization_name AS course_provider
FROM training_course_enrollments e
JOIN profiles p ON e.profile_id = p.id
JOIN training_courses t ON e.course_id = t.id;

-- Grant access to authenticated users
GRANT SELECT ON public.training_course_enrollments_view TO authenticated;
GRANT SELECT ON public.training_course_enrollments_view TO anon;

COMMIT;

-- Verify the fix
SELECT 'Training course enrollments view recreated with avatar_url column' as status;
