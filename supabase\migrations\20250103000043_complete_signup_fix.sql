-- Complete signup fix - populate profile data and create notification preferences
-- Updated to remove push_notifications and ensure proper preference checking
-- Date: 2025-01-03

BEGIN;

-- Update handle_new_user to populate profile data properly and create notification preferences
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert profile with proper data from signup form
  INSERT INTO public.profiles (
    id,
    first_name,
    last_name,
    email,
    social_visibility,
    subscription_tier,
    subscription_status,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  );

  -- Create notification preferences (individual boolean columns structure)
  -- Note: Removed push_notifications as it's not used in the platform
  INSERT INTO public.notification_preferences (
    profile_id,
    email_notifications,
    post_likes,
    post_comments,
    comment_replies,
    comment_likes,
    connection_requests,
    connection_accepted,
    event_signups,
    event_updates,
    system_notifications,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    true, -- email_notifications - default enabled
    true, -- post_likes - default enabled
    true, -- post_comments - default enabled
    true, -- comment_replies - default enabled
    true, -- comment_likes - default enabled
    true, -- connection_requests - default enabled
    true, -- connection_accepted - default enabled
    true, -- event_signups - default enabled
    true, -- event_updates - default enabled
    true, -- system_notifications - default enabled
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (profile_id) DO NOTHING;

  -- Create user consent settings with defaults
  INSERT INTO public.user_consent_settings (
    user_id,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    false, -- share_email_with_event_creators - default disabled for privacy
    false, -- share_email_with_attendees - default disabled for privacy
    false, -- share_contact_details - default disabled for privacy
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (user_id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Remove the duplicate email sync trigger that might be causing conflicts
-- We already have sync_auth_email_to_profile, we don't need z_sync_email_after_signup
DROP TRIGGER IF EXISTS z_sync_email_after_signup ON auth.users;
DROP FUNCTION IF EXISTS public.sync_email_to_profile();

-- Update the comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates profile with proper data and notification preferences during signup';

COMMIT;

-- Verify the function was updated
SELECT 'Complete signup fix applied - profile data and notification preferences will be created' as status;
