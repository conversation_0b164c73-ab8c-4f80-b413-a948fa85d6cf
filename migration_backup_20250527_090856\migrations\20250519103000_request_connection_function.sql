-- Create a function to request a connection to another user
CREATE OR R<PERSON>LACE FUNCTION request_connection(user_id_param UUID, connection_id_param UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER -- Run with privileges of function creator
AS $$
DECLARE
  result JSON;
  existing_connection_id UUID;
BEGIN
  -- First check if a connection already exists
  SELECT id INTO existing_connection_id 
  FROM user_connections
  WHERE (user_id = user_id_param AND connection_id = connection_id_param)
     OR (user_id = connection_id_param AND connection_id = user_id_param);
  
  -- If connection exists, return it
  IF existing_connection_id IS NOT NULL THEN
    SELECT row_to_json(conn) 
    INTO result
    FROM (
      SELECT * FROM user_connections WHERE id = existing_connection_id
    ) conn;
    
    RETURN result;
  END IF;
  
  -- If no connection exists, create a new one
  WITH new_connection AS (
    INSERT INTO user_connections (user_id, connection_id, status)
    VALUES (user_id_param, connection_id_param, 'pending')
    RETURNING *
  )
  SELECT row_to_json(conn) 
  INTO result
  FROM (
    SELECT 
      nc.*,
      json_build_object(
        'first_name', p.first_name,
        'last_name', p.last_name,
        'avatar_url', p.avatar_url,
        'title', p.title,
        'organization', p.organization
      ) AS connection_profile
    FROM new_connection nc
    LEFT JOIN profiles p ON p.id = nc.connection_id
  ) conn;
  
  RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION request_connection TO authenticated;
