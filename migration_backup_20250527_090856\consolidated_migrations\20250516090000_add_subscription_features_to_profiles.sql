-- Migration: Add subscription features to profiles table
-- This migration adds subscription tier related columns to the profiles table

ALTER TABLE public.profiles
  ADD COLUMN subscription_tier TEXT NOT NULL DEFAULT 'seed' CHECK (subscription_tier IN ('seed', 'seedling', 'sapling', 'woodland')),
  ADD COLUMN subscription_start_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN subscription_end_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'canceled', 'expired', 'trial')),
  ADD COLUMN payment_method_id TEXT,
  ADD COLUMN customer_id TEXT;

-- Create index for efficient querying of subscription data
CREATE INDEX idx_profiles_subscription_tier ON public.profiles (subscription_tier);
CREATE INDEX idx_profiles_subscription_status ON public.profiles (subscription_status);

COMMENT ON COLUMN public.profiles.subscription_tier IS 'The tier level of the user subscription (seed, seedling, sapling, woodland)';
COMMENT ON COLUMN public.profiles.subscription_start_date IS 'The start date of the current subscription period';
COMMENT ON COLUMN public.profiles.subscription_end_date IS 'The end date of the current subscription period';
COMMENT ON COLUMN public.profiles.subscription_status IS 'The current status of the subscription (active, canceled, expired, trial)';
COMMENT ON COLUMN public.profiles.payment_method_id IS 'Reference to payment method in payment processor';
COMMENT ON COLUMN public.profiles.customer_id IS 'Reference to customer ID in payment processor';
