-- Fix notification actor names to show proper user names instead of "Someone"
-- This migration improves the notifications_with_details view to better handle actor names
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- PART 1: IMPROVE NOTIFICATIONS VIEW ACTOR NAME HANDLING
-- ================================================================

-- Drop and recreate the notifications_with_details view with better name logic
DROP VIEW IF EXISTS public.notifications_with_details CASCADE;

CREATE VIEW public.notifications_with_details AS
SELECT 
  n.id,
  n.profile_id,
  n.type,
  n.content,
  n.related_id,
  n.related_type,
  n.actor_id,
  n.is_read,
  n.created_at,
  n.updated_at,
  n.expires_at,
  -- Actor details with comprehensive name handling
  actor_profile.first_name as actor_first_name,
  actor_profile.last_name as actor_last_name,
  actor_profile.avatar_url as actor_avatar_url,
  -- Enhanced actor display name logic with multiple fallbacks
  CASE
    -- Try first_name + last_name combination
    WHEN actor_profile.first_name IS NOT NULL AND TRIM(actor_profile.first_name) != ''
         AND actor_profile.last_name IS NOT NULL AND TRIM(actor_profile.last_name) != '' THEN
      TRIM(CONCAT(actor_profile.first_name, ' ', actor_profile.last_name))
    -- Try just first_name if available
    WHEN actor_profile.first_name IS NOT NULL AND TRIM(actor_profile.first_name) != '' THEN
      TRIM(actor_profile.first_name)
    -- Try just last_name if available
    WHEN actor_profile.last_name IS NOT NULL AND TRIM(actor_profile.last_name) != '' THEN
      TRIM(actor_profile.last_name)
    -- If actor_id is null, it's a system notification
    WHEN n.actor_id IS NULL THEN
      'System'
    -- Final fallback
    ELSE 'A user'
  END as actor_display_name,
  -- Related object details with better error handling
  CASE 
    WHEN n.related_type = 'post' THEN (
      SELECT COALESCE(LEFT(content, 100), 'Post content unavailable') 
      FROM public.social_posts 
      WHERE id = n.related_id
    )
    WHEN n.related_type = 'event' THEN (
      SELECT COALESCE(title, 'Event title unavailable') 
      FROM public.events 
      WHERE id = n.related_id
    )
    WHEN n.related_type = 'comment' THEN (
      SELECT COALESCE(LEFT(content, 100), 'Comment content unavailable') 
      FROM public.post_comments 
      WHERE id = n.related_id
    )
    WHEN n.related_type = 'connection' THEN
      'Connection request'
    WHEN n.related_type = 'profile' THEN
      'Profile update'
    ELSE NULL
  END as related_preview
FROM 
  public.notifications n
  LEFT JOIN public.profiles actor_profile ON n.actor_id = actor_profile.id;

-- Grant permissions on the updated view
GRANT SELECT ON public.notifications_with_details TO authenticated;

-- Add comment to the view
COMMENT ON VIEW public.notifications_with_details IS 
'Enhanced notifications view with comprehensive actor name handling and improved related object previews. Handles missing names gracefully.';

-- ================================================================
-- PART 2: CREATE DIAGNOSTIC FUNCTION
-- ================================================================

-- Create a function to diagnose notification actor name issues
CREATE OR REPLACE FUNCTION public.diagnose_notification_actors()
RETURNS TABLE (
  notification_id UUID,
  actor_id UUID,
  actor_first_name TEXT,
  actor_last_name TEXT,
  computed_display_name TEXT,
  issue_description TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    n.id as notification_id,
    n.actor_id,
    p.first_name as actor_first_name,
    p.last_name as actor_last_name,
    CASE
      WHEN p.first_name IS NOT NULL AND TRIM(p.first_name) != ''
           AND p.last_name IS NOT NULL AND TRIM(p.last_name) != '' THEN
        TRIM(CONCAT(p.first_name, ' ', p.last_name))
      WHEN p.first_name IS NOT NULL AND TRIM(p.first_name) != '' THEN
        TRIM(p.first_name)
      WHEN p.last_name IS NOT NULL AND TRIM(p.last_name) != '' THEN
        TRIM(p.last_name)
      WHEN n.actor_id IS NULL THEN
        'System'
      ELSE 'A user'
    END as computed_display_name,
    CASE
      WHEN n.actor_id IS NULL THEN
        'System notification (no actor)'
      WHEN p.id IS NULL THEN
        'Actor profile not found'
      WHEN (p.first_name IS NULL OR TRIM(p.first_name) = '')
           AND (p.last_name IS NULL OR TRIM(p.last_name) = '') THEN
        'Actor has no name fields populated'
      ELSE 'Actor name available'
    END as issue_description
  FROM public.notifications n
  LEFT JOIN public.profiles p ON n.actor_id = p.id
  ORDER BY n.created_at DESC;
END;
$$;

-- Grant execute permission on the diagnostic function
GRANT EXECUTE ON FUNCTION public.diagnose_notification_actors() TO authenticated;

-- ================================================================
-- PART 3: VERIFICATION AND LOGGING
-- ================================================================

-- Log the current state for verification
DO $$
DECLARE
    total_notifications INTEGER;
    notifications_with_actors INTEGER;
    notifications_with_proper_names INTEGER;
    system_notifications INTEGER;
    missing_actor_profiles INTEGER;
    sample_name RECORD;
BEGIN
    -- Count total notifications
    SELECT COUNT(*) INTO total_notifications
    FROM public.notifications;

    -- Count notifications with actors
    SELECT COUNT(*) INTO notifications_with_actors
    FROM public.notifications
    WHERE actor_id IS NOT NULL;

    -- Count notifications with proper display names (not fallbacks)
    SELECT COUNT(*) INTO notifications_with_proper_names
    FROM public.notifications_with_details
    WHERE actor_display_name NOT IN ('A user', 'System');

    -- Count system notifications
    SELECT COUNT(*) INTO system_notifications
    FROM public.notifications
    WHERE actor_id IS NULL;

    -- Count notifications where actor profile is missing
    SELECT COUNT(*) INTO missing_actor_profiles
    FROM public.notifications n
    LEFT JOIN public.profiles p ON n.actor_id = p.id
    WHERE n.actor_id IS NOT NULL AND p.id IS NULL;

    RAISE NOTICE 'NOTIFICATION ACTOR NAME VERIFICATION:';
    RAISE NOTICE 'Total notifications: %', total_notifications;
    RAISE NOTICE 'Notifications with actors: %', notifications_with_actors;
    RAISE NOTICE 'Notifications with proper names: %', notifications_with_proper_names;
    RAISE NOTICE 'System notifications: %', system_notifications;
    RAISE NOTICE 'Missing actor profiles: %', missing_actor_profiles;
    
    -- Show sample of actor display names
    RAISE NOTICE 'Sample actor display names:';
    FOR sample_name IN (
        SELECT DISTINCT actor_display_name
        FROM public.notifications_with_details
        WHERE actor_display_name IS NOT NULL
        LIMIT 10
    ) LOOP
        RAISE NOTICE '  - %', sample_name.actor_display_name;
    END LOOP;
END $$;

COMMIT;
