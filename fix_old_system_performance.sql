-- =====================================================
-- FIX OLD SYSTEM PERFORMANCE TEST
-- =====================================================
-- Test if adding cached profile_visibility fixes the old system
-- Before deciding to migrate to new system

-- =====================================================
-- 1. UPDATE OLD get_suggested_connections FUNCTION
-- =====================================================

-- Drop and recreate the old function to use cached profile_visibility
DROP FUNCTION IF EXISTS get_suggested_connections(UUID, INTEGER);

CREATE OR REPLACE FUNCTION get_suggested_connections(
    requesting_user_id UUID,
    suggestion_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    first_name TEXT,
    last_name TEXT,
    title TEXT,
    organization TEXT,
    avatar_url TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        p.title,
        p.organization,
        p.avatar_url
    FROM profiles p
    WHERE 
        -- Exclude the requesting user
        p.id != requesting_user_id
        AND 
        -- Use cached profile_visibility column (fast!)
        p.profile_visibility = true
        AND 
        -- Exclude users with existing relationships (optimized with NOT EXISTS)
        NOT EXISTS (
            SELECT 1 
            FROM user_connections uc 
            WHERE (
                (uc.user_id = requesting_user_id AND uc.connection_id = p.id)
                OR 
                (uc.connection_id = requesting_user_id AND uc.user_id = p.id)
            )
        )
    ORDER BY p.created_at DESC
    LIMIT suggestion_limit;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_suggested_connections(UUID, INTEGER) TO authenticated;

-- =====================================================
-- 2. PERFORMANCE TEST QUERIES
-- =====================================================

-- Test the updated function performance
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
SELECT * FROM get_suggested_connections(
    (SELECT id FROM profiles LIMIT 1),  -- Use first user as test
    10
);

-- =====================================================
-- 3. VERIFY FUNCTION WORKS
-- =====================================================

-- Test the function returns data
SELECT 
    'Old System Test' as test_type,
    COUNT(*) as suggestions_returned
FROM get_suggested_connections(
    (SELECT id FROM profiles LIMIT 1),
    10
);

-- Compare with direct query
SELECT 
    'Direct Query Test' as test_type,
    COUNT(*) as suggestions_available
FROM profiles p
WHERE p.profile_visibility = true
AND p.id != (SELECT id FROM profiles LIMIT 1)
LIMIT 10;

-- =====================================================
-- 4. CHECK EXISTING CONNECTIONS DATA
-- =====================================================

-- Check what's in the old user_connections table
SELECT 
    'Old Connections Data' as check_type,
    COUNT(*) as total_connections,
    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
FROM user_connections;

-- Show sample connections
SELECT 
    'Sample Connections' as check_type,
    uc.id,
    uc.user_id,
    uc.connection_id,
    uc.status,
    p1.first_name || ' ' || p1.last_name as user_name,
    p2.first_name || ' ' || p2.last_name as connection_name
FROM user_connections uc
LEFT JOIN profiles p1 ON uc.user_id = p1.id
LEFT JOIN profiles p2 ON uc.connection_id = p2.id
ORDER BY uc.created_at DESC
LIMIT 5;

-- =====================================================
-- 5. COMPARISON WITH NEW SYSTEM
-- =====================================================

-- Check if new system tables exist
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'social_connections') 
        THEN 'New system tables exist'
        ELSE 'New system tables do not exist'
    END as new_system_status;

-- If new system exists, compare data
SELECT 
    'System Comparison' as comparison_type,
    'Old user_connections' as system,
    COUNT(*) as connection_count
FROM user_connections
UNION ALL
SELECT 
    'System Comparison' as comparison_type,
    'New social_connections' as system,
    COALESCE((SELECT COUNT(*) FROM social_connections), 0) as connection_count;

-- =====================================================
-- 6. PERFORMANCE BENCHMARK
-- =====================================================

-- Time the old function call
SELECT 
    'Performance Test Started' as status,
    now() as start_time;

-- Run the function multiple times to test performance
SELECT get_suggested_connections(
    (SELECT id FROM profiles ORDER BY random() LIMIT 1),
    10
) FROM generate_series(1, 5);

SELECT 
    'Performance Test Completed' as status,
    now() as end_time;

-- =====================================================
-- 7. RECOMMENDATIONS
-- =====================================================

SELECT 
    '=== PERFORMANCE TEST RESULTS ===' as section,
    'Check the EXPLAIN ANALYZE output above' as instruction,
    'If execution time < 50ms, old system is fixed!' as success_criteria,
    'If still slow, recommend migrating to new system' as fallback_plan;

-- =====================================================
-- 8. NEXT STEPS DECISION MATRIX
-- =====================================================

SELECT 
    '=== DECISION MATRIX ===' as section,
    'Fast Performance (< 50ms)' as criteria_1,
    'Has all needed features' as criteria_2,
    'Easy to maintain' as criteria_3,
    'KEEP OLD SYSTEM' as recommendation_if_all_true;

SELECT 
    '=== MIGRATION BENEFITS ===' as section,
    'Cleaner architecture' as benefit_1,
    'Better error handling' as benefit_2,
    'Modern React components' as benefit_3,
    'Purpose-built for your needs' as benefit_4,
    'MIGRATE TO NEW SYSTEM' as recommendation;
