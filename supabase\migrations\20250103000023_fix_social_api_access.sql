-- Comprehensive fix for social media REST API access
-- This ensures all tables are properly accessible via Supabase REST API

BEGIN;

-- Grant all necessary permissions to authenticated role
GRANT ALL ON public.post_likes TO authenticated;
GRANT ALL ON public.post_comments TO authenticated;
GRANT ALL ON public.comment_likes TO authenticated;
GRANT ALL ON public.post_categories TO authenticated;
GRANT ALL ON public.social_posts TO authenticated;

-- Grant permissions to anon role for reading (if needed)
GRANT SELECT ON public.post_likes TO anon;
GRANT SELECT ON public.post_comments TO anon;
GRANT SELECT ON public.comment_likes TO anon;
GRANT SELECT ON public.post_categories TO anon;
GRANT SELECT ON public.social_posts TO anon;

-- Grant permissions to service_role
GRANT ALL ON public.post_likes TO service_role;
GRANT ALL ON public.post_comments TO service_role;
GRANT ALL ON public.comment_likes TO service_role;
GRANT ALL ON public.post_categories TO service_role;
GRANT ALL ON public.social_posts TO service_role;

-- Ensure RLS is enabled on all tables
ALTER TABLE public.post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_posts ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "post_likes_select_policy" ON public.post_likes;
DROP POLICY IF EXISTS "post_likes_insert_policy" ON public.post_likes;
DROP POLICY IF EXISTS "post_likes_delete_policy" ON public.post_likes;

DROP POLICY IF EXISTS "post_comments_select_policy" ON public.post_comments;
DROP POLICY IF EXISTS "post_comments_insert_policy" ON public.post_comments;
DROP POLICY IF EXISTS "post_comments_update_policy" ON public.post_comments;
DROP POLICY IF EXISTS "post_comments_delete_policy" ON public.post_comments;

DROP POLICY IF EXISTS "comment_likes_select_policy" ON public.comment_likes;
DROP POLICY IF EXISTS "comment_likes_insert_policy" ON public.comment_likes;
DROP POLICY IF EXISTS "comment_likes_delete_policy" ON public.comment_likes;

-- Create permissive RLS policies for post_likes
CREATE POLICY "Enable read access for all users" ON public.post_likes
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.post_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable delete for users based on user_id" ON public.post_likes
    FOR DELETE USING (auth.uid() = user_id);

-- Create permissive RLS policies for post_comments
CREATE POLICY "Enable read access for all users" ON public.post_comments
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.post_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for users based on user_id" ON public.post_comments
    FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable delete for users based on user_id" ON public.post_comments
    FOR DELETE USING (auth.uid() = user_id);

-- Create permissive RLS policies for comment_likes
CREATE POLICY "Enable read access for all users" ON public.comment_likes
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.comment_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable delete for users based on user_id" ON public.comment_likes
    FOR DELETE USING (auth.uid() = user_id);

-- Create permissive RLS policies for social_posts (if not already exist)
DROP POLICY IF EXISTS "Enable read access for all users" ON public.social_posts;
CREATE POLICY "Enable read access for all users" ON public.social_posts
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.social_posts;
CREATE POLICY "Enable insert for authenticated users only" ON public.social_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.social_posts;
CREATE POLICY "Enable update for users based on user_id" ON public.social_posts
    FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.social_posts;
CREATE POLICY "Enable delete for users based on user_id" ON public.social_posts
    FOR DELETE USING (auth.uid() = user_id);

COMMIT;

-- Test that we can access the tables
SELECT 'post_likes' as table_name, COUNT(*) as count FROM public.post_likes;
SELECT 'post_comments' as table_name, COUNT(*) as count FROM public.post_comments;
SELECT 'comment_likes' as table_name, COUNT(*) as count FROM public.comment_likes;
SELECT 'social_posts' as table_name, COUNT(*) as count FROM public.social_posts;
