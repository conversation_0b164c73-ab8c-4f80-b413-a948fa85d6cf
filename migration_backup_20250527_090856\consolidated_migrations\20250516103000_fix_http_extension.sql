-- Create the pg_net extension if it doesn't exist
CREATE EXTENSION IF NOT EXISTS pg_net;

-- <PERSON>reate function to handle post deletions and trigger edge function
CREATE OR REPLACE FUNCTION handle_deleted_post_media()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the deleted post had an image
  IF OLD.media_type = 'image' AND OLD.media_url IS NOT NULL THEN
    -- Call edge function to delete the image from Cloudflare
    PERFORM http_post(
      url := 'https://' || get_app_setting('edge_function_base_url') || '/delete-cloudflare-image',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || get_app_setting('service_role_key')
      )::jsonb,
      body := jsonb_build_object(
        'record', jsonb_build_object(
          'media_url', OLD.media_url,
          'media_type', OLD.media_type
        )
      )::jsonb
    );
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS handle_deleted_post_media_trigger ON social_posts;
