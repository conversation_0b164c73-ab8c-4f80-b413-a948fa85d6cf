-- Find any database objects that reference "posts" table (not "social_posts")
-- This will help identify what's causing the error

-- Check all functions for "posts" references
SELECT 
    'FUNCTION' as object_type,
    routine_name as object_name,
    routine_definition as definition
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND (
    routine_definition ILIKE '%FROM posts %' 
    OR routine_definition ILIKE '%FROM posts;%'
    OR routine_definition ILIKE '%UPDATE posts %'
    OR routine_definition ILIKE '%INSERT INTO posts %'
    OR routine_definition ILIKE '%DELETE FROM posts %'
    OR routine_definition ILIKE '%JOIN posts %'
)

UNION ALL

-- Check all views for "posts" references  
SELECT 
    'VIEW' as object_type,
    table_name as object_name,
    view_definition as definition
FROM information_schema.views 
WHERE table_schema = 'public'
AND (
    view_definition ILIKE '%FROM posts %'
    OR view_definition ILIKE '%FROM posts;%'
    OR view_definition ILIKE '%JOIN posts %'
)

UNION ALL

-- Check triggers
SELECT 
    'TRIGGER' as object_type,
    trigger_name as object_name,
    action_statement as definition
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND (
    action_statement ILIKE '%FROM posts %'
    OR action_statement ILIKE '%FROM posts;%'
    OR action_statement ILIKE '%UPDATE posts %'
    OR action_statement ILIKE '%INSERT INTO posts %'
    OR action_statement ILIKE '%DELETE FROM posts %'
    OR action_statement ILIKE '%JOIN posts %'
)

ORDER BY object_type, object_name;
