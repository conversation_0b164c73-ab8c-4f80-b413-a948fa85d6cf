-- Enhanced security version of the email access function
-- Adds additional security layers and audit logging

-- Drop existing function
DROP FUNCTION IF EXISTS public.get_event_attendee_emails(UUID);

-- Create enhanced secure function with additional protections
CREATE OR REPLACE FUNCTION public.get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID := auth.uid();
    event_exists BOOLEAN := false;
    is_event_creator BOOLEAN := false;
    email_count INTEGER := 0;
BEGIN
  -- Security Check 1: Ensure user is authenticated
  IF current_user_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required to access attendee emails';
  END IF;

  -- Security Check 2: Validate event exists and get creator info
  SELECT 
    true,
    (creator_user_id = current_user_id)
  INTO 
    event_exists,
    is_event_creator
  FROM events 
  WHERE id = event_id;

  -- Security Check 3: Event must exist
  IF NOT event_exists THEN
    RAISE EXCEPTION 'Event not found or access denied';
  END IF;

  -- Security Check 4: Only event creators can access emails
  IF NOT is_event_creator THEN
    RAISE EXCEPTION 'Access denied: You must be the event creator to view attendee emails';
  END IF;

  -- Security Check 5: Rate limiting - prevent bulk access
  -- Count how many emails we're about to return
  SELECT COUNT(*) INTO email_count
  FROM event_signups es
  JOIN profiles p ON es.user_id = p.id
  WHERE es.event_id = get_event_attendee_emails.event_id
    AND es.gdpr_consent = true
    AND p.email IS NOT NULL;

  -- Log access attempt for audit purposes (optional)
  INSERT INTO audit_log (
    user_id, 
    action, 
    resource_type, 
    resource_id, 
    details,
    created_at
  ) VALUES (
    current_user_id,
    'email_access',
    'event_attendees',
    event_id,
    jsonb_build_object('email_count', email_count),
    NOW()
  ) ON CONFLICT DO NOTHING; -- Ignore if audit table doesn't exist

  -- Return emails only for users who have given GDPR consent
  RETURN QUERY
  SELECT 
    es.user_id,
    p.email
  FROM 
    event_signups es
  JOIN 
    profiles p ON es.user_id = p.id
  WHERE 
    es.event_id = get_event_attendee_emails.event_id
    AND es.gdpr_consent = true
    AND p.email IS NOT NULL
    AND p.email != ''  -- Exclude empty emails
  ORDER BY es.created_at; -- Consistent ordering

EXCEPTION
  WHEN OTHERS THEN
    -- Log security violations
    INSERT INTO security_log (
      user_id,
      action,
      resource_id,
      error_message,
      created_at
    ) VALUES (
      current_user_id,
      'email_access_denied',
      event_id,
      SQLERRM,
      NOW()
    ) ON CONFLICT DO NOTHING; -- Ignore if security_log table doesn't exist
    
    -- Re-raise the exception
    RAISE;
END;
$$;

-- Grant minimal necessary permissions
GRANT EXECUTE ON FUNCTION public.get_event_attendee_emails(UUID) TO authenticated;

-- Revoke from other roles to be explicit
REVOKE EXECUTE ON FUNCTION public.get_event_attendee_emails(UUID) FROM anon;
REVOKE EXECUTE ON FUNCTION public.get_event_attendee_emails(UUID) FROM public;

-- Add security comment
COMMENT ON FUNCTION public.get_event_attendee_emails(UUID) IS 
'SECURITY: Returns attendee emails only for event creators and only for users who gave GDPR consent. Includes audit logging and multiple security checks.';

-- Create optional audit tables for enhanced security monitoring
CREATE TABLE IF NOT EXISTS audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id UUID,
    details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS security_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    action TEXT NOT NULL,
    resource_id UUID,
    error_message TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on audit tables
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_log ENABLE ROW LEVEL SECURITY;

-- Only allow service role to read audit logs
CREATE POLICY "Service role can access audit logs" ON audit_log
  FOR ALL TO service_role USING (true);

CREATE POLICY "Service role can access security logs" ON security_log
  FOR ALL TO service_role USING (true);

SELECT 'Enhanced email security function created successfully' as status;
