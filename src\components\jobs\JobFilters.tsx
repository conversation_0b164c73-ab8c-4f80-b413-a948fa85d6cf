import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Filter, X, MapPin, Briefcase, Clock, PoundSterling, Building, Tag } from "lucide-react";

export interface JobFilters {
  locationTypes: string[];
  contractTypes: string[];
  hoursTypes: string[];
  salaryBrackets: string[];
  jobFunctions: string[];
  categories: string[];
  industries: string[];
  locations: string[];
}

interface JobFiltersProps {
  filters: JobFilters;
  onFiltersChange: (filters: JobFilters) => void;
  onClearFilters: () => void;
  availableOptions: {
    locationTypes: string[];
    contractTypes: string[];
    hoursTypes: string[];
    salaryBrackets: string[];
    jobFunctions: string[];
    categories: string[];
    industries: string[];
    locations: string[];
  };
}

const JobFiltersComponent: React.FC<JobFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  availableOptions
}) => {
  const updateFilter = (filterType: keyof JobFilters, value: string, checked: boolean) => {
    const currentValues = filters[filterType];
    const newValues = checked
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value);
    
    onFiltersChange({
      ...filters,
      [filterType]: newValues
    });
  };

  const removeFilter = (filterType: keyof JobFilters, value: string) => {
    updateFilter(filterType, value, false);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).reduce((count, filterArray) => count + filterArray.length, 0);
  };

  const hasActiveFilters = getActiveFiltersCount() > 0;

  const FilterPopover = ({ 
    title, 
    icon: Icon, 
    filterType, 
    options 
  }: { 
    title: string; 
    icon: React.ElementType; 
    filterType: keyof JobFilters; 
    options: string[] 
  }) => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" className="relative">
          <Icon className="h-4 w-4 mr-2" />
          {title}
          {filters[filterType].length > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {filters[filterType].length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-4">
        <div className="space-y-3">
          <h4 className="font-medium text-sm">{title}</h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {options.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <Checkbox
                  id={`${filterType}-${option}`}
                  checked={filters[filterType].includes(option)}
                  onCheckedChange={(checked) => 
                    updateFilter(filterType, option, checked as boolean)
                  }
                />
                <label
                  htmlFor={`${filterType}-${option}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                >
                  {option}
                </label>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );

  return (
    <div className="bg-card rounded-lg border p-4 mb-6 space-y-4">
      {/* Filter Controls */}
      <div className="flex flex-wrap gap-3 items-center">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filters:</span>
        </div>
        
        <FilterPopover
          title="Location Type"
          icon={MapPin}
          filterType="locationTypes"
          options={availableOptions.locationTypes}
        />
        
        <FilterPopover
          title="Contract Type"
          icon={Briefcase}
          filterType="contractTypes"
          options={availableOptions.contractTypes}
        />
        
        <FilterPopover
          title="Hours"
          icon={Clock}
          filterType="hoursTypes"
          options={availableOptions.hoursTypes}
        />
        
        <FilterPopover
          title="Salary"
          icon={PoundSterling}
          filterType="salaryBrackets"
          options={availableOptions.salaryBrackets}
        />
        
        <FilterPopover
          title="Function"
          icon={Building}
          filterType="jobFunctions"
          options={availableOptions.jobFunctions}
        />
        
        <FilterPopover
          title="Categories"
          icon={Tag}
          filterType="categories"
          options={availableOptions.categories}
        />
        
        <FilterPopover
          title="Industries"
          icon={Building}
          filterType="industries"
          options={availableOptions.industries}
        />
        
        <FilterPopover
          title="Locations"
          icon={MapPin}
          filterType="locations"
          options={availableOptions.locations}
        />

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(filters).map(([filterType, values]) =>
            values.map((value) => (
              <Badge
                key={`${filterType}-${value}`}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {value}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFilter(filterType as keyof JobFilters, value)}
                  className="h-4 w-4 p-0 hover:bg-transparent"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default JobFiltersComponent;
