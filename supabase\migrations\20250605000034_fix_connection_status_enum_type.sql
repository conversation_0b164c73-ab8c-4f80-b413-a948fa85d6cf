-- Fix connection_status enum type issue in user_connections table
-- This migration ensures the status column uses the proper enum type
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- CREATE CONNECTION_STATUS ENUM TYPE IF NOT EXISTS
-- ================================================================

-- Create the connection_status enum type if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'connection_status') THEN
        CREATE TYPE connection_status AS ENUM ('pending', 'accepted', 'rejected');
        RAISE NOTICE 'Created connection_status enum type';
    ELSE
        RAISE NOTICE 'connection_status enum type already exists';
    END IF;
END $$;

-- ================================================================
-- FIX USER_CONNECTIONS TABLE STATUS COLUMN
-- ================================================================

-- Check current column type and convert if necessary
DO $$
DECLARE
    current_data_type TEXT;
BEGIN
    -- Get current data type of status column
    SELECT data_type INTO current_data_type
    FROM information_schema.columns 
    WHERE table_name = 'user_connections' 
    AND column_name = 'status' 
    AND table_schema = 'public';
    
    RAISE NOTICE 'Current status column type: %', current_data_type;
    
    -- Only convert if it's not already using the enum
    IF current_data_type != 'USER-DEFINED' THEN
        RAISE NOTICE 'Converting status column from % to connection_status enum...', current_data_type;
        
        -- First, ensure all existing values are valid
        UPDATE user_connections 
        SET status = 'pending' 
        WHERE status NOT IN ('pending', 'accepted', 'rejected');
        
        -- Convert the column type with explicit casting
        ALTER TABLE user_connections 
        ALTER COLUMN status TYPE connection_status 
        USING status::text::connection_status;
        
        RAISE NOTICE 'Successfully converted status column to connection_status enum';
    ELSE
        RAISE NOTICE 'Status column already uses connection_status enum type';
    END IF;
END $$;

-- ================================================================
-- UPDATE RPC FUNCTION TO HANDLE ENUM PROPERLY
-- ================================================================

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS public.respond_to_connection_request(UUID, TEXT);

-- Create the corrected RPC function that properly handles enum casting
CREATE OR REPLACE FUNCTION public.respond_to_connection_request(
    request_id_param UUID,
    status_param TEXT
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    connection_record RECORD;
    current_user_id UUID;
    update_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Validate status parameter
    IF status_param NOT IN ('accepted', 'rejected') THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid status. Must be accepted or rejected'
        );
    END IF;
    
    -- Verify the connection request exists and user is authorized to respond
    SELECT * INTO connection_record
    FROM public.user_connections
    WHERE id = request_id_param 
      AND connection_id = current_user_id 
      AND status = 'pending'::connection_status;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found or you are not authorized to respond'
        );
    END IF;
    
    -- Update the request status with proper enum casting
    UPDATE user_connections 
    SET 
        status = status_param::connection_status,
        updated_at = NOW()
    WHERE id = request_id_param 
    AND connection_id = current_user_id 
    AND status = 'pending'::connection_status;
    
    GET DIAGNOSTICS update_count = ROW_COUNT;
    
    IF update_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to update request'
        );
    END IF;
    
    RETURN jsonb_build_object(
        'success', true,
        'message', format('Connection request %s successfully', status_param),
        'data', jsonb_build_object(
            'id', request_id_param,
            'status', status_param
        )
    );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.respond_to_connection_request(UUID, TEXT) TO authenticated;

-- Add comment
COMMENT ON FUNCTION public.respond_to_connection_request(UUID, TEXT)
IS 'Allows a user to accept or reject a pending connection request. Properly handles connection_status enum type.';

-- ================================================================
-- UPDATE OTHER CONNECTION-RELATED RPC FUNCTIONS
-- ================================================================

-- Drop and recreate request_connection function if it exists
DROP FUNCTION IF EXISTS public.request_connection(UUID, UUID);

-- Create request_connection function that properly handles enum
CREATE OR REPLACE FUNCTION public.request_connection(
    user_id_param UUID,
    connection_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_connection_id UUID;
    current_user_id UUID;
BEGIN
    -- Get current user
    current_user_id := auth.uid();

    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;

    -- Verify the requesting user is the authenticated user
    IF user_id_param != current_user_id THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Unauthorized: can only create connections for yourself'
        );
    END IF;

    -- Check if connection already exists
    IF EXISTS (
        SELECT 1 FROM user_connections
        WHERE (user_id = user_id_param AND connection_id = connection_id_param)
        OR (user_id = connection_id_param AND connection_id = user_id_param)
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection already exists'
        );
    END IF;

    -- Create new connection request with proper enum casting
    INSERT INTO user_connections (user_id, connection_id, status)
    VALUES (user_id_param, connection_id_param, 'pending'::connection_status)
    RETURNING id INTO new_connection_id;

    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'id', new_connection_id,
            'user_id', user_id_param,
            'connection_id', connection_id_param,
            'status', 'pending'
        )
    );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.request_connection(UUID, UUID) TO authenticated;

-- Drop and recreate cancel_connection_request function if it exists
DROP FUNCTION IF EXISTS public.cancel_connection_request(UUID);

-- Create cancel_connection_request function
CREATE OR REPLACE FUNCTION public.cancel_connection_request(
    request_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();

    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;

    -- Delete the connection request (only if user is the sender and status is pending)
    DELETE FROM user_connections
    WHERE id = request_id_param
      AND user_id = current_user_id
      AND status = 'pending'::connection_status;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    IF deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found or cannot be cancelled'
        );
    END IF;

    RETURN jsonb_build_object(
        'success', true,
        'message', 'Connection request cancelled successfully'
    );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.cancel_connection_request(UUID) TO authenticated;

-- Drop and recreate remove_connection function if it exists
DROP FUNCTION IF EXISTS public.remove_connection(UUID);

-- Create remove_connection function that properly handles enum and returns JSONB
CREATE OR REPLACE FUNCTION public.remove_connection(
    connection_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    connection_record RECORD;
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();

    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;

    -- Verify the connection exists and user is authorized to remove it
    SELECT * INTO connection_record
    FROM public.user_connections
    WHERE id = connection_id_param
      AND (user_id = current_user_id OR connection_id = current_user_id)
      AND status = 'accepted'::connection_status;

    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection not found or you are not authorized to remove it'
        );
    END IF;

    -- Delete the connection
    DELETE FROM user_connections
    WHERE id = connection_id_param
    AND (user_id = current_user_id OR connection_id = current_user_id)
    AND status = 'accepted'::connection_status;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    IF deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to remove connection'
        );
    END IF;

    RETURN jsonb_build_object(
        'success', true,
        'message', 'Connection removed successfully'
    );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.remove_connection(UUID) TO authenticated;

-- Add comment
COMMENT ON FUNCTION public.remove_connection(UUID)
IS 'Allows a user to remove an existing connection. Properly handles connection_status enum type.';

-- ================================================================
-- VERIFY THE FIX
-- ================================================================

-- Display current table structure for verification
SELECT 
    'USER_CONNECTIONS_COLUMN_INFO' as info_type,
    column_name,
    data_type,
    udt_name,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'user_connections' 
AND table_schema = 'public'
AND column_name = 'status'
ORDER BY ordinal_position;

COMMIT;
