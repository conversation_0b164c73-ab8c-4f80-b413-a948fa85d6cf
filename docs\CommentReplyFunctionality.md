# Comment Reply Functionality

This document describes the implementation of the comment reply functionality in the social feed of the NetZero Platform.

## Overview

The comment reply feature allows users to:
- Reply directly to comments on posts
- See threaded conversations with proper indentation
- Navigate nested replies up to 3 levels deep
- Like both top-level comments and replies

## Implementation Details

### Data Structure

Comments and replies are stored in the same `post_comments` table with a self-referential relationship:

```
post_comments
- id (UUID, primary key)
- post_id (UUID, foreign key to social_posts)
- user_id (UUID, foreign key to auth.users)
- content (text)
- created_at (timestamp)
- parent_comment_id (UUID, foreign key to post_comments, nullable)
- likes_count (integer, default 0)
```

This structure allows for:
- Top-level comments (where `parent_comment_id` is null)
- Replies to comments (where `parent_comment_id` references another comment)
- Nested replies (replies to replies)

### Key Components

1. **PostComment Component**
   - Renders individual comments and their replies
   - <PERSON>les showing and hiding replies
   - Provides "Reply" button and reply form
   - Supports likes for comments

2. **RepliesContext**
   - Manages the global state for which comment is being replied to
   - Tracks highlighted comments
   - Ensures consistent UI across components

3. **useCommentReplies Hook**
   - <PERSON><PERSON> fetching, adding, and updating replies
   - Provides functions for reply-specific operations

4. **PostgreSQL Functions**
   - `get_comment_replies_count`: Counts direct replies to a comment
   - `get_post_comment_tree`: Gets comments in a hierarchical structure
   - `get_total_comment_replies_count`: Recursively counts all replies

### User Experience

1. User clicks "Reply" on a comment
2. Reply form appears beneath the comment
3. After submitting a reply:
   - It appears as nested under the parent comment
   - The reply form is hidden
   - The new reply is briefly highlighted

### Display Logic

- Comments are organized into a tree structure using the `organizeComments` function
- Depth is limited to 3 levels for readability
- Each level has visual indentation and a left border
- Replies can be collapsed/expanded to manage screen space

## Usage

To add a reply to a comment:

1. Click the "Reply" button on a comment
2. Enter your reply text in the form
3. Click "Reply" to submit

To view replies:
- Replies are shown by default
- Click "Hide X replies" to collapse them
- Click "Show X replies" to expand them

## Security

- All comment and reply actions require authentication
- Users can only like comments once
- Reply creation is protected against spam by rate limiting

## Extensions

This implementation can be extended for:
- Notifications when someone replies to your comment
- Tagging users in replies with @mentions
- Email notifications for important replies
- Advanced moderation tools for nested comments
