-- Migration: Add sustainability professional fields and tables

-- 1. Add boolean to profiles table
ALTER TABLE profiles
ADD COLUMN is_sustainability_professional BOOLEAN DEFAULT FALSE;

-- 2. Create table for professional types
CREATE TABLE sustainability_professional_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE
);

-- 3. Create join table for profile <-> professional types
CREATE TABLE profile_professional_types (
    profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    professional_type_id INTEGER REFERENCES sustainability_professional_types(id) ON DELETE CASCADE,
    PRIMARY KEY (profile_id, professional_type_id)
);
