-- Diagnostic script to check jobs table schema and constraints
-- Run this in Supabase SQL Editor to understand the current state

-- 1. Check the current enum types and their values
SELECT 
    'Enum Types' as section,
    t.typname as enum_name,
    e.enumlabel as enum_value,
    e.enumsortorder as sort_order
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN ('location_type', 'job_location_type', 'salary_bracket', 'contract_type', 'job_contract_type', 'hours_type', 'job_hours_type', 'job_function')
ORDER BY t.typname, e.enumsortorder;

-- 2. Check the jobs table structure
SELECT 
    'Jobs Table Structure' as section,
    column_name,
    data_type,
    udt_name,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'jobs'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check constraints on the jobs table
SELECT 
    'Table Constraints' as section,
    constraint_name,
    constraint_type,
    check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'jobs'
AND tc.table_schema = 'public';

-- 4. Check current data in jobs table (sample)
SELECT 
    'Sample Jobs Data' as section,
    id,
    title,
    location_type,
    office_location,
    contract_type,
    hours_type,
    job_function,
    salary_bracket,
    status
FROM jobs
LIMIT 5;

-- 5. Check for any data that might violate the constraint
SELECT 
    'Constraint Violations' as section,
    id,
    title,
    location_type,
    office_location,
    CASE 
        WHEN location_type = 'Remote' AND office_location IS NOT NULL THEN 'Remote job has office_location'
        WHEN location_type IN ('Hybrid', 'Office') AND office_location IS NULL THEN 'Office/Hybrid job missing office_location'
        ELSE 'OK'
    END as violation_type
FROM jobs
WHERE 
    (location_type = 'Remote' AND office_location IS NOT NULL) OR
    (location_type IN ('Hybrid', 'Office') AND office_location IS NULL);

-- 6. Check if there are any lowercase enum values in the data
SELECT 
    'Lowercase Enum Values' as section,
    COUNT(*) as count,
    location_type
FROM jobs
WHERE location_type IN ('remote', 'hybrid', 'on-site')
GROUP BY location_type;

-- 7. Show the exact constraint definition
SELECT 
    'Constraint Definition' as section,
    pg_get_constraintdef(c.oid) as constraint_definition
FROM pg_constraint c
JOIN pg_class t ON c.conrelid = t.oid
JOIN pg_namespace n ON t.relnamespace = n.oid
WHERE t.relname = 'jobs'
AND n.nspname = 'public'
AND c.conname = 'valid_office_location';
