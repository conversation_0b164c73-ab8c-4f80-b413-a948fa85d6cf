-- =====================================================
-- TEST PROFILE VISIBILITY SYNC SYSTEM
-- =====================================================
-- Run this after setting up the sync system to verify it works

-- =====================================================
-- 1. INITIAL STATE CHECK
-- =====================================================

SELECT 
    '=== INITIAL STATE CHECK ===' as test_phase,
    COUNT(*) as total_profiles,
    COUNT(CASE WHEN profile_visibility = true THEN 1 END) as visible_profiles,
    COUNT(CASE WHEN profile_visibility = false THEN 1 END) as hidden_profiles
FROM profiles;

-- =====================================================
-- 2. TEST SYNC FROM user_consent_settings TO profiles
-- =====================================================

-- Get a test user (first user in the system)
DO $$
DECLARE
    test_user_id UUID;
    original_visibility BOOLEAN;
    cached_visibility BOOLEAN;
BEGIN
    -- Get first user
    SELECT id INTO test_user_id FROM profiles LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Testing with user ID: %', test_user_id;
        
        -- Check original state
        SELECT profile_visibility INTO cached_visibility FROM profiles WHERE id = test_user_id;
        RAISE NOTICE 'Original cached visibility: %', cached_visibility;
        
        -- Test 1: Update user_consent_settings to false
        INSERT INTO user_consent_settings (user_id, profile_visibility) 
        VALUES (test_user_id, false)
        ON CONFLICT (user_id) 
        DO UPDATE SET profile_visibility = false;
        
        -- Check if profiles table was updated
        SELECT profile_visibility INTO cached_visibility FROM profiles WHERE id = test_user_id;
        RAISE NOTICE 'After setting to false - cached visibility: %', cached_visibility;
        
        -- Test 2: Update user_consent_settings to true
        UPDATE user_consent_settings 
        SET profile_visibility = true 
        WHERE user_id = test_user_id;
        
        -- Check if profiles table was updated
        SELECT profile_visibility INTO cached_visibility FROM profiles WHERE id = test_user_id;
        RAISE NOTICE 'After setting to true - cached visibility: %', cached_visibility;
        
        RAISE NOTICE 'Sync test completed successfully!';
    ELSE
        RAISE NOTICE 'No users found to test with';
    END IF;
END $$;

-- =====================================================
-- 3. VERIFY SYNC STATUS
-- =====================================================

SELECT 
    '=== SYNC VERIFICATION ===' as test_phase,
    p.id,
    p.first_name,
    p.profile_visibility as cached_in_profiles,
    COALESCE(ucs.profile_visibility, true) as source_in_consent_settings,
    CASE 
        WHEN p.profile_visibility = COALESCE(ucs.profile_visibility, true) THEN '✅ SYNCED'
        ELSE '❌ OUT_OF_SYNC'
    END as sync_status
FROM profiles p
LEFT JOIN user_consent_settings ucs ON p.id = ucs.user_id
ORDER BY p.created_at DESC
LIMIT 10;

-- =====================================================
-- 4. TEST PERFORMANCE QUERY
-- =====================================================

-- Test the fast query that uses cached column
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization
FROM profiles p
WHERE p.profile_visibility = true
AND p.id != '00000000-0000-0000-0000-000000000000'  -- Dummy user ID
LIMIT 10;

-- =====================================================
-- 5. SUMMARY REPORT
-- =====================================================

SELECT 
    '=== FINAL SUMMARY ===' as report_section,
    'Profile Visibility Sync System' as system_name,
    'ACTIVE' as status,
    (SELECT COUNT(*) FROM profiles WHERE profile_visibility = true) as visible_profiles,
    (SELECT COUNT(*) FROM profiles WHERE profile_visibility = false) as hidden_profiles,
    (SELECT COUNT(*) FROM profiles) as total_profiles;

-- Check if triggers exist
SELECT 
    '=== TRIGGER STATUS ===' as report_section,
    trigger_name,
    event_manipulation,
    action_timing,
    'ACTIVE' as status
FROM information_schema.triggers 
WHERE trigger_name IN ('sync_profile_visibility_trigger', 'ensure_profile_visibility_trigger');

-- Check if indexes exist
SELECT 
    '=== INDEX STATUS ===' as report_section,
    indexname,
    tablename,
    'ACTIVE' as status
FROM pg_indexes 
WHERE indexname LIKE '%profile%visibility%';

-- =====================================================
-- 6. PERFORMANCE COMPARISON
-- =====================================================

-- Show the difference between old (with JOIN) and new (cached) approaches
SELECT '=== PERFORMANCE COMPARISON ===' as comparison;

-- Old approach (slow - with JOIN)
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
SELECT p.id, p.first_name, p.last_name
FROM profiles p
LEFT JOIN user_consent_settings ucs ON ucs.user_id = p.id
WHERE COALESCE(ucs.profile_visibility, true) = true
LIMIT 10;

-- New approach (fast - cached column)
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
SELECT p.id, p.first_name, p.last_name
FROM profiles p
WHERE p.profile_visibility = true
LIMIT 10;
