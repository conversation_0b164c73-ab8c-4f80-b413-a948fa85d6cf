-- Add security definer view for event signups with user data
DROP VIEW IF EXISTS public.event_signups_with_users;

CREATE OR REPLACE VIEW public.event_signups_with_users AS
WITH visible_signups AS (
    SELECT es.*
    FROM event_signups es
    LEFT JOIN events e ON e.id = es.event_id
    WHERE 
        -- Show all signups to event owners
        (auth.uid() = e.owner_id) OR
        -- Show all non-hidden signups
        (NOT es.hide_attendance) OR
        -- Show user's own signup
        (es.user_id = auth.uid())
)
SELECT 
    s.id,
    s.event_id,
    s.user_id,
    s.created_at,
    s.updated_at,
    s.hide_attendance,
    s.gdpr_consent,
    u.email,
    p.first_name,
    p.last_name,
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    p.title,
    p.organization,
    p.avatar_url
FROM 
    visible_signups s
LEFT JOIN auth.users u ON u.id = s.user_id
LEFT JOIN public.profiles p ON p.id = s.user_id;

-- Grant necessary permissions
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.event_signups_with_users TO anon;
