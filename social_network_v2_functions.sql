-- =====================================================
-- SOCIAL NETWORK V2: Optimized API Functions
-- =====================================================

-- =====================================================
-- 0. DROP EXISTING FUNCTIONS TO AVOID CONFLICTS
-- =====================================================

-- Drop any existing functions that might conflict
DROP FUNCTION IF EXISTS send_connection_request(UUID);
DROP FUNCTION IF EXISTS accept_connection_request(UUID);
DROP FUNCTION IF EXISTS decline_connection_request(UUID);
DROP FUNCTION IF EXISTS cancel_connection_request(UUID);
DROP FUNCTION IF EXISTS remove_connection(UUID);
DROP FUNCTION IF EXISTS get_user_connections(UUID);
DROP FUNCTION IF EXISTS get_connection_requests();
DROP FUNCTION IF EXISTS get_sent_requests();
DROP FUNCTION IF EXISTS get_suggested_connections_v2(INTEGER);

-- =====================================================
-- 1. CONNECTION MANAGEMENT FUNCTIONS
-- =====================================================

-- Send connection request
CREATE OR REPLACE FUNCTION send_connection_request(recipient_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    existing_connection RECORD;
    new_connection_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Authentication required');
    END IF;
    
    IF current_user_id = recipient_user_id THEN
        RETURN jsonb_build_object('success', false, 'error', 'Cannot connect to yourself');
    END IF;
    
    -- Check for existing connection
    SELECT * INTO existing_connection
    FROM social_connections 
    WHERE (requester_id = current_user_id AND recipient_id = recipient_user_id)
       OR (requester_id = recipient_user_id AND recipient_id = current_user_id);
    
    IF FOUND THEN
        RETURN jsonb_build_object(
            'success', false, 
            'error', 'Connection already exists',
            'status', existing_connection.status
        );
    END IF;
    
    -- Create new connection request
    INSERT INTO social_connections (requester_id, recipient_id, status)
    VALUES (current_user_id, recipient_user_id, 'pending')
    RETURNING id INTO new_connection_id;
    
    -- Create notification for recipient
    INSERT INTO connection_notifications (recipient_id, actor_id, notification_type, reference_id, message)
    VALUES (
        recipient_user_id, 
        current_user_id, 
        'connection_request', 
        new_connection_id,
        'sent you a connection request'
    );
    
    RETURN jsonb_build_object(
        'success', true, 
        'connection_id', new_connection_id,
        'status', 'pending'
    );
END;
$$;

-- Accept connection request
CREATE OR REPLACE FUNCTION accept_connection_request(connection_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    connection_record RECORD;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Authentication required');
    END IF;
    
    -- Get connection record
    SELECT * INTO connection_record
    FROM social_connections 
    WHERE id = connection_id AND recipient_id = current_user_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Connection request not found or not authorized');
    END IF;
    
    -- Update connection status
    UPDATE social_connections 
    SET status = 'connected', connected_at = now(), updated_at = now()
    WHERE id = connection_id;
    
    -- Create notification for requester
    INSERT INTO connection_notifications (recipient_id, actor_id, notification_type, reference_id, message)
    VALUES (
        connection_record.requester_id, 
        current_user_id, 
        'connection_accepted', 
        connection_id,
        'accepted your connection request'
    );
    
    -- Create activity for both users
    INSERT INTO connection_activities (user_id, activity_type, activity_data)
    VALUES 
        (current_user_id, 'connection_made', jsonb_build_object('connected_with', connection_record.requester_id)),
        (connection_record.requester_id, 'connection_made', jsonb_build_object('connected_with', current_user_id));
    
    RETURN jsonb_build_object('success', true, 'status', 'connected');
END;
$$;

-- Decline connection request
CREATE OR REPLACE FUNCTION decline_connection_request(connection_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Authentication required');
    END IF;
    
    -- Update connection status
    UPDATE social_connections 
    SET status = 'declined', updated_at = now()
    WHERE id = connection_id AND recipient_id = current_user_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Connection request not found or not authorized');
    END IF;
    
    RETURN jsonb_build_object('success', true, 'status', 'declined');
END;
$$;

-- Cancel connection request
CREATE OR REPLACE FUNCTION cancel_connection_request(connection_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Authentication required');
    END IF;
    
    -- Delete connection request
    DELETE FROM social_connections 
    WHERE id = connection_id AND requester_id = current_user_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Connection request not found or not authorized');
    END IF;
    
    RETURN jsonb_build_object('success', true, 'status', 'cancelled');
END;
$$;

-- Remove connection
CREATE OR REPLACE FUNCTION remove_connection(connection_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Authentication required');
    END IF;
    
    -- Delete connection
    DELETE FROM social_connections 
    WHERE id = connection_id 
    AND (requester_id = current_user_id OR recipient_id = current_user_id)
    AND status = 'connected';
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Connection not found or not authorized');
    END IF;
    
    RETURN jsonb_build_object('success', true, 'status', 'removed');
END;
$$;

-- =====================================================
-- 2. QUERY FUNCTIONS
-- =====================================================

-- Get user's connections
CREATE OR REPLACE FUNCTION get_user_connections(target_user_id UUID DEFAULT NULL)
RETURNS TABLE (
    connection_id UUID,
    user_id UUID,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    title TEXT,
    organization TEXT,
    connected_at TIMESTAMPTZ,
    mutual_connections INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    query_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    query_user_id := COALESCE(target_user_id, current_user_id);
    
    IF current_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;
    
    RETURN QUERY
    SELECT 
        sc.id as connection_id,
        CASE 
            WHEN sc.requester_id = query_user_id THEN sc.recipient_id
            ELSE sc.requester_id
        END as user_id,
        p.first_name,
        p.last_name,
        p.avatar_url,
        p.title,
        p.organization,
        sc.connected_at,
        0 as mutual_connections -- TODO: Calculate mutual connections
    FROM social_connections sc
    JOIN profiles p ON p.id = CASE 
        WHEN sc.requester_id = query_user_id THEN sc.recipient_id
        ELSE sc.requester_id
    END
    WHERE (sc.requester_id = query_user_id OR sc.recipient_id = query_user_id)
    AND sc.status = 'connected'
    ORDER BY sc.connected_at DESC;
END;
$$;

-- Get connection requests (received)
CREATE OR REPLACE FUNCTION get_connection_requests()
RETURNS TABLE (
    connection_id UUID,
    requester_id UUID,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    title TEXT,
    organization TEXT,
    created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;
    
    RETURN QUERY
    SELECT 
        sc.id as connection_id,
        sc.requester_id,
        p.first_name,
        p.last_name,
        p.avatar_url,
        p.title,
        p.organization,
        sc.created_at
    FROM social_connections sc
    JOIN profiles p ON p.id = sc.requester_id
    WHERE sc.recipient_id = current_user_id
    AND sc.status = 'pending'
    ORDER BY sc.created_at DESC;
END;
$$;

-- Get sent requests
CREATE OR REPLACE FUNCTION get_sent_requests()
RETURNS TABLE (
    connection_id UUID,
    recipient_id UUID,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    title TEXT,
    organization TEXT,
    created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;
    
    RETURN QUERY
    SELECT 
        sc.id as connection_id,
        sc.recipient_id,
        p.first_name,
        p.last_name,
        p.avatar_url,
        p.title,
        p.organization,
        sc.created_at
    FROM social_connections sc
    JOIN profiles p ON p.id = sc.recipient_id
    WHERE sc.requester_id = current_user_id
    AND sc.status = 'pending'
    ORDER BY sc.created_at DESC;
END;
$$;

-- Get suggested connections (optimized)
CREATE OR REPLACE FUNCTION get_suggested_connections_v2(suggestion_limit INTEGER DEFAULT 10)
RETURNS TABLE (
    user_id UUID,
    first_name TEXT,
    last_name TEXT,
    avatar_url TEXT,
    title TEXT,
    organization TEXT,
    mutual_connections INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();

    IF current_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;

    RETURN QUERY
    SELECT
        p.id as user_id,
        p.first_name,
        p.last_name,
        p.avatar_url,
        p.title,
        p.organization,
        0 as mutual_connections -- TODO: Calculate mutual connections
    FROM profiles p
    WHERE p.id != current_user_id
    AND p.profile_visibility = true  -- Use cached column for fast queries
    AND NOT EXISTS (
        SELECT 1 FROM social_connections sc
        WHERE (sc.requester_id = current_user_id AND sc.recipient_id = p.id)
           OR (sc.recipient_id = current_user_id AND sc.requester_id = p.id)
    )
    ORDER BY COALESCE(p.created_at, now()) DESC  -- Handle NULL created_at
    LIMIT suggestion_limit;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION send_connection_request(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION accept_connection_request(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decline_connection_request(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cancel_connection_request(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION remove_connection(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_connections(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_connection_requests() TO authenticated;
GRANT EXECUTE ON FUNCTION get_sent_requests() TO authenticated;
GRANT EXECUTE ON FUNCTION get_suggested_connections_v2(INTEGER) TO authenticated;
