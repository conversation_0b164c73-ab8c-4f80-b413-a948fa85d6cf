# NetZero Platform Database Reset Instructions

This document provides comprehensive instructions for resetting and recreating the NetZero Platform database with proper table dependencies.

## Prerequisites

- Supabase CLI installed
- PostgreSQL client installed (psql)
- PowerShell 5.0 or later

## Quick Reset (Using Schema File)

The fastest way to reset the database is using the pre-ordered schema file:

```powershell
# Reset the database using the schema file
npx supabase db reset

# Or with a specific schema file
npx supabase db reset --schema-only --db-url postgresql://postgres:postgres@localhost:54322/postgres -f ./supabase/schema.sql
```

## Full Reset Process (From Migration Files)

For a complete reset using the migration files (recommended for development):

### Step 1: Reset to Base State

```powershell
# Reset database to empty state
npx supabase db reset --no-backup
```

### Step 2: Apply Core Migrations First

Apply the core migration files in the correct order:

```powershell
# Apply core migration files in order
$migrationFiles = @(
    "20240000000000_create_profiles_table.sql",
    "20240000000001_create_businesses_table.sql", 
    "20240000000002_create_netzero_standards_table.sql",
    "20240000000003_create_business_standards_table.sql"
)

foreach ($file in $migrationFiles) {
    $filePath = "./supabase/migrations/$file"
    Write-Host "Applying migration: $file"
    Get-Content $filePath | npx supabase db execute
}
```

### Step 3: Apply Remaining Migrations

```powershell
# Apply remaining migrations in timestamp order
$remainingMigrations = Get-ChildItem -Path ./supabase/migrations -Filter "2025*.sql" | Sort-Object Name

foreach ($file in $remainingMigrations) {
    Write-Host "Applying migration: $($file.Name)"
    Get-Content $file.FullName | npx supabase db execute
}
```

## Verification

After resetting, verify that the database schema is correct:

```powershell
# Run verification script
.\Verify-Schema.ps1

# Check the log file for errors
Get-Content .\schema_verification_log.txt | Select-Object -Last 20
```

## Troubleshooting

### Foreign Key Constraints

If you encounter foreign key constraint errors:
1. Check if tables are being created in the right order
2. Verify that referenced tables exist before tables that reference them
3. Run the fix-schema-order.ps1 script to ensure proper table ordering

### Schema Generation

If you need to regenerate the schema file with proper ordering:

```powershell
# Generate initial schema
.\Extract-Schema.ps1

# Fix schema ordering
.\fix-schema-order.ps1
```

## Core Table Dependencies

Understanding these dependencies is crucial for database recreation:

1. `profiles` table depends on `auth.users`
2. `businesses` table depends on `profiles`
3. `netzero_standards` table has no external dependencies
4. `business_standards` table depends on both `businesses` and `netzero_standards`

## Notes

- Always back up your data before performing a reset
- The schema.sql file is automatically ordered with proper dependencies
- Custom migrations should follow the timestamp naming convention: YYYYMMDDHHMMSS_descriptive_name.sql
