-- Remove unnecessary email field from events_with_creator view
-- Attendees don't need to contact event creators, so email is not needed
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- UPDATE THE VIEW TO REMOVE EMAIL FIELD
-- ================================================================

-- Drop the existing view
DROP VIEW IF EXISTS public.events_with_creator CASCADE;

-- Recreate the view without the email field
CREATE OR REPLACE VIEW public.events_with_creator AS
SELECT 
    e.id,
    e.creator_user_id,
    e.title,
    e.start_date,
    e.start_time,
    e.end_date,
    e.end_time,
    e.description,
    e.image_url,
    e.event_type,
    e.physical_location,
    e.meeting_url,
    e.event_category,
    e.tags,
    e.created_at,
    e.updated_at,
    e.industry_ids,
    e.netzero_category_ids,
    e.owner_id,
    e.status,
    e.capacity,
    e.is_private,
    e.category,
    e.industry,
    -- Creator information from profiles table (no email for privacy)
    concat(p.first_name, ' ', p.last_name) AS creator_name,
    p.avatar_url AS creator_avatar_url
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id;

-- Grant permissions to the view
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;

-- Add comment explaining the privacy-focused approach
COMMENT ON VIEW public.events_with_creator IS 
'Events view with creator information. Email is excluded for privacy - attendees contact creators through the platform, not directly.';

-- ================================================================
-- TEST THE UPDATED VIEW
-- ================================================================

-- Test that the view works without email
SELECT 
    'EMAIL_REMOVED_TEST' as test_type,
    COUNT(*) as total_events,
    COUNT(CASE WHEN creator_name IS NOT NULL THEN 1 END) as events_with_creator_names
FROM public.events_with_creator;

-- Show sample data to verify email is removed
SELECT 
    'SAMPLE_NO_EMAIL' as test_type,
    id,
    title,
    creator_name,
    creator_avatar_url
FROM public.events_with_creator
LIMIT 3;

COMMIT;
