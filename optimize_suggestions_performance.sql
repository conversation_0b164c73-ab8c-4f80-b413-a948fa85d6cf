-- Optimize performance for suggested connections queries

-- Create index on user_consent_settings.profile_visibility for faster filtering
CREATE INDEX IF NOT EXISTS idx_user_consent_settings_profile_visibility
ON user_consent_settings(profile_visibility)
WHERE profile_visibility = true;

-- Create composite index for even better performance
CREATE INDEX IF NOT EXISTS idx_user_consent_settings_visibility_user
ON user_consent_settings(profile_visibility, user_id)
WHERE profile_visibility = true;

-- Create index on profiles.created_at for ordering (if not exists)
CREATE INDEX IF NOT EXISTS idx_profiles_created_at
ON profiles(created_at DESC);

-- Create index on user_connections for faster exclusion filtering
CREATE INDEX IF NOT EXISTS idx_user_connections_user_status
ON user_connections(user_id, status);

CREATE INDEX IF NOT EXISTS idx_user_connections_connection_status
ON user_connections(connection_id, status);

-- Analyze tables to update statistics for query planner
ANALYZE user_consent_settings;
ANALYZE profiles;
ANALYZE user_connections;

-- <PERSON>reate optimized RPC function for fetching suggested connections
-- This version uses EXISTS instead of NOT IN for much better performance
CREATE OR REPLACE FUNCTION get_suggested_connections(
    requesting_user_id UUID,
    suggestion_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    first_name TEXT,
    last_name TEXT,
    title TEXT,
    organization TEXT,
    avatar_url TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.title,
        p.organization,
        p.avatar_url
    FROM profiles p
    LEFT JOIN user_consent_settings ucs ON ucs.user_id = p.id
    WHERE
        -- Exclude the requesting user
        p.id != requesting_user_id
        AND
        -- Only show profiles with visibility enabled (default to true for backward compatibility)
        COALESCE(ucs.profile_visibility, true) = true
        AND
        -- Use NOT EXISTS for much better performance than NOT IN
        NOT EXISTS (
            SELECT 1
            FROM user_connections uc
            WHERE (
                (uc.user_id = requesting_user_id AND uc.connection_id = p.id)
                OR
                (uc.connection_id = requesting_user_id AND uc.user_id = p.id)
            )
        )
    ORDER BY p.created_at DESC
    LIMIT suggestion_limit;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_suggested_connections(UUID, INTEGER) TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION get_suggested_connections IS
'Efficiently fetch suggested connections with all filtering done at database level for optimal performance. Excludes current user, non-visible profiles, and users with existing relationships.';
