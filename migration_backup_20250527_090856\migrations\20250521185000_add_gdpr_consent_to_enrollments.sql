-- Add GDPR consent column to training_course_enrollments table
ALTER TABLE training_course_enrollments 
ADD COLUMN IF NOT EXISTS gdpr_consent BOOLEAN NOT NULL DEFAULT FALSE;

-- Add a comment explaining the purpose of the column
COMMENT ON COLUMN training_course_enrollments.gdpr_consent IS 'User consent for data processing related to course enrollment';

-- Create or replace view to help with displaying enrollment information
CREATE OR REPLACE VIEW training_course_enrollments_with_user AS
SELECT 
    tce.*,
    p.first_name || ' ' || p.last_name AS full_name,
    p.avatar_url,
    p.title,
    p.organization
FROM 
    training_course_enrollments tce
JOIN 
    profiles p ON tce.user_id = p.id;

-- Grant permissions for authenticated users
GRANT SELECT ON training_course_enrollments_with_user TO authenticated;
