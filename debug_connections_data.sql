-- Debug script to check connection data and find duplicates
-- Run this in Supabase SQL Editor while logged in

-- 1. Check all user_connections for the current user
SELECT 
    'All user connections' as section,
    id,
    user_id,
    connection_id,
    status,
    created_at,
    CASE 
        WHEN user_id = auth.uid() THEN 'I am the sender'
        WHEN connection_id = auth.uid() THEN 'I am the recipient'
        ELSE 'Not my connection'
    END as my_role
FROM public.user_connections
WHERE user_id = auth.uid() OR connection_id = auth.uid()
ORDER BY created_at DESC;

-- 2. Check for duplicate IDs
SELECT 
    'Duplicate check' as section,
    id,
    COUNT(*) as count
FROM public.user_connections
WHERE user_id = auth.uid() OR connection_id = auth.uid()
GROUP BY id
HAVING COUNT(*) > 1;

-- 3. Check sent requests specifically (what the frontend should see)
SELECT 
    'Sent requests (pending)' as section,
    id,
    user_id,
    connection_id,
    status,
    created_at
FROM public.user_connections
WHERE user_id = auth.uid() 
AND status = 'pending'
ORDER BY created_at DESC;

-- 4. Check received requests specifically
SELECT 
    'Received requests (pending)' as section,
    id,
    user_id,
    connection_id,
    status,
    created_at
FROM public.user_connections
WHERE connection_id = auth.uid() 
AND status = 'pending'
ORDER BY created_at DESC;

-- 5. Test cancel function with a specific ID (replace with actual ID from above)
-- This will show us exactly what the function is checking
SELECT 
    'Cancel function test' as section,
    id,
    user_id,
    connection_id,
    status,
    CASE 
        WHEN user_id = auth.uid() AND status = 'pending' THEN 'Can cancel'
        WHEN user_id != auth.uid() THEN 'Not the sender'
        WHEN status != 'pending' THEN 'Not pending'
        ELSE 'Cannot cancel'
    END as cancel_status
FROM public.user_connections
WHERE id = '9109f126-e3f0-4937-a9e9-6f391911e031'::uuid;

-- 6. Check current user ID
SELECT 
    'Current user' as section,
    auth.uid() as user_id,
    auth.role() as role;
