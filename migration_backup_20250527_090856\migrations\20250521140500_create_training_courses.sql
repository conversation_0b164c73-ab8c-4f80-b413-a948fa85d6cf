-- Create function for updating timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc', NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create training courses table
CREATE TABLE training_courses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', NOW()),
    title TEXT NOT NULL,
    description TEXT,
    organization_name TEXT NOT NULL,
    delivery_method TEXT NOT NULL CHECK (delivery_method IN ('Online', 'InPerson', 'Hybrid')),
    location_address TEXT, -- Only required for InPerson or Hybrid
    start_date DATE,
    end_date DATE,
    website_url TEXT,
    cost DECIMAL,
    status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Draft', 'Published')),
    creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    category_id UUID REFERENCES netzero_categories(id) ON DELETE SET NULL -- Single category selection
);

-- Add RLS policies
ALTER TABLE training_courses ENABLE ROW LEVEL SECURITY;

-- Grant base table permissions
GRANT ALL ON training_courses TO authenticated;
GRANT SELECT ON training_courses TO anon;

-- Policy to allow anyone to view published courses
CREATE POLICY "View published training courses"
    ON training_courses
    FOR SELECT
    USING (status = 'Published' OR auth.uid() = creator_id);

-- Policy to allow authenticated users to create courses
CREATE POLICY "Users can create training courses"
    ON training_courses
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = creator_id);

-- Policy to allow course creators to update their own courses
CREATE POLICY "Creators can update own training courses"
    ON training_courses
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = creator_id)
    WITH CHECK (auth.uid() = creator_id);

-- Policy to allow course creators to delete their own courses
CREATE POLICY "Creators can delete own training courses"
    ON training_courses
    FOR DELETE
    TO authenticated
    USING (auth.uid() = creator_id);

-- Create a view for training courses that includes resolved category names
CREATE VIEW training_courses_view AS
SELECT 
    t.*,
    nc.name as category_name
FROM training_courses t
LEFT JOIN netzero_categories nc ON nc.id = t.category_id
WHERE t.status = 'Published' OR auth.uid() = t.creator_id;

-- Grant permissions on the view
GRANT SELECT ON training_courses_view TO authenticated;
GRANT SELECT ON training_courses_view TO anon;

-- Create trigger to update the updated_at timestamp
CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON training_courses
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- Add some indexes for common query patterns
CREATE INDEX training_courses_status_idx ON training_courses(status);
CREATE INDEX training_courses_creator_idx ON training_courses(creator_id);
CREATE INDEX training_courses_delivery_method_idx ON training_courses(delivery_method);
CREATE INDEX training_courses_category_id_idx ON training_courses(category_id);
