-- Diagnostic script to check notification_preferences table structure
-- Run this to verify the table structure after applying the fix
-- Date: 2025-06-05

-- Check notification_preferences table structure
SELECT 
    'notification_preferences_structure' as check_name,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'notification_preferences' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if push_notifications column still exists (should be FALSE)
SELECT 
    'push_notifications_exists' as check_name,
    EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification_preferences' 
        AND column_name = 'push_notifications'
        AND table_schema = 'public'
    ) as column_exists;

-- Check handle_new_user function definition
SELECT 
    'handle_new_user_function' as check_name,
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- Check if the trigger exists
SELECT 
    'auth_trigger_exists' as check_name,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- Test notification preferences creation (dry run)
DO $$
DECLARE
    test_profile_id UUID := gen_random_uuid();
    success BOOLEAN := FALSE;
BEGIN
    -- Try to insert a test record to see if the structure works
    BEGIN
        INSERT INTO public.notification_preferences (
            profile_id,
            email_notifications,
            post_likes,
            post_comments,
            comment_replies,
            comment_likes,
            connection_requests,
            connection_accepted,
            event_signups,
            event_updates,
            system_notifications,
            created_at,
            updated_at
        )
        VALUES (
            test_profile_id,
            true, true, true, true, true, true, true, true, true, true,
            NOW(), NOW()
        );
        
        success := TRUE;
        
        -- Clean up the test record
        DELETE FROM public.notification_preferences WHERE profile_id = test_profile_id;
        
    EXCEPTION
        WHEN OTHERS THEN
            success := FALSE;
    END;
    
    RAISE NOTICE 'Test notification preferences insert: %', 
        CASE WHEN success THEN 'SUCCESS' ELSE 'FAILED' END;
END $$;
