-- Test the minimal signup function
-- This will help us identify if there are other issues

DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
BEGIN
    -- Test the absolute minimal insert
    RAISE NOTICE 'Testing minimal profile insert with ID: %', test_user_id;
    
    BEGIN
        INSERT INTO public.profiles (
            id,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'SUCCESS: Minimal profile insert worked';
        
        -- Clean up
        DELETE FROM public.profiles WHERE id = test_user_id;
        RAISE NOTICE 'Test completed successfully';
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: Minimal profile insert failed: %', SQLERRM;
        RAISE NOTICE 'SQLSTATE: %', SQLSTATE;
    END;
END $$;
