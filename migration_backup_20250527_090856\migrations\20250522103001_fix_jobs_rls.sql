-- Drop existing jobs RLS policies
DROP POLICY IF EXISTS "Anyone can read jobs" ON public.jobs;
DROP POLICY IF EXISTS "Authenticated users can create jobs" ON public.jobs;
DROP POLICY IF EXISTS "Users can update their own jobs" ON public.jobs;
DROP POLICY IF EXISTS "Users can delete their own jobs" ON public.jobs;

-- <PERSON><PERSON> revised RLS policies
-- Allow public read access to all jobs (for job listings page)
CREATE POLICY "Anyone can read jobs" ON public.jobs
    FOR SELECT USING (true);

-- Allow authenticated users to create jobs
CREATE POLICY "Authenticated users can create jobs" ON public.jobs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow users to see their own jobs (specifically for the dashboard)
CREATE POLICY "Users can see their own jobs" ON public.jobs
    FOR SELECT USING (auth.uid() = created_by);

-- Allow users to update their own jobs
CREATE POLICY "Users can update their own jobs" ON public.jobs
    FOR UPDATE USING (auth.uid() = created_by);

-- Allow users to delete their own jobs
CREATE POLICY "Users can delete their own jobs" ON public.jobs
    FOR DELETE USING (auth.uid() = created_by);
