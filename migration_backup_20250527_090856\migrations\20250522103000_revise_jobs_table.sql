-- Drop existing jobs-related tables
DROP TABLE IF EXISTS public.jobs_benefits CASCADE;
DROP TABLE IF EXISTS public.jobs_requirements CASCADE;
DROP TABLE IF EXISTS public.jobs_responsibilities CASCADE;
DROP TABLE IF EXISTS public.jobs_tags CASCADE;
DROP TABLE IF EXISTS public.jobs CASCADE;

-- Create enum types for job attributes
CREATE TYPE public.job_location_type AS ENUM ('Hybrid', 'Remote', 'Office');
CREATE TYPE public.job_contract_type AS ENUM ('Permanent', 'Contract', 'Freelance');
CREATE TYPE public.job_hours_type AS ENUM ('Job Share', 'Full Time', 'Part Time');
CREATE TYPE public.salary_bracket AS ENUM (
    'Under £25,000',
    '£25,000 - £35,000',
    '£35,000 - £45,000',
    '£45,000 - £55,000',
    '£55,000 - £65,000',
    '£65,000 - £75,000',
    '£75,000 - £85,000',
    '£85,000 - £95,000',
    '£95,000 - £105,000',
    'Above £105,000'
);

CREATE TYPE public.job_function AS ENUM (
    'Auditing and Assurance',
    'Carbon Accounting',
    'Circular Economy',
    'Climate Adaptation and Resilience',
    'Conservation',
    'Consulting',
    'Corporate Social Responsibility',
    'Ecology and Biodiversity',
    'Energy Management',
    'Environmental Compliance and Permitting',
    'Environmental Data Analysis',
    'Environmental Impact Assessment (EIA)',
    'Environmental Management',
    'Environmental Social & Governance (ESG)',
    'Health, Safety, and Environment (HSE)',
    'Natural Resources Management',
    'Noise Management',
    'Planning',
    'Pollution and Contamination',
    'QHSE (Quality, Health, Safety, and Environment)',
    'Renewable Energy',
    'Resource Management',
    'Social Value and Impact',
    'Sustainability',
    'Sustainability Analysis and Reporting',
    'Sustainable Buildings',
    'Sustainable Design',
    'Sustainable Finance',
    'Sustainable Supply Chain and Procurement',
    'Waste Management',
    'Water Quality and Management'
);

-- Create the revised jobs table
CREATE TABLE public.jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    organization TEXT NOT NULL,
    description TEXT NOT NULL,
    salary_bracket salary_bracket NOT NULL,
    location_type job_location_type NOT NULL,
    region TEXT NOT NULL,
    industry TEXT NOT NULL,
    net_zero_category TEXT NOT NULL,
    job_function job_function NOT NULL,
    contract_type job_contract_type NOT NULL,
    hours_type job_hours_type NOT NULL,
    office_location TEXT,
    application_url TEXT NOT NULL,
    date_posted TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    CONSTRAINT valid_office_location CHECK (
        (location_type = 'Remote' AND office_location IS NULL) OR
        (location_type IN ('Hybrid', 'Office') AND office_location IS NOT NULL)
    )
);

-- Create RLS policies
ALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;

-- Anyone can read jobs
CREATE POLICY "Anyone can read jobs" ON public.jobs
    FOR SELECT USING (true);

-- Only authenticated users can create jobs
CREATE POLICY "Authenticated users can create jobs" ON public.jobs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Only the job creator can update or delete their jobs
CREATE POLICY "Users can update their own jobs" ON public.jobs
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own jobs" ON public.jobs
    FOR DELETE USING (auth.uid() = created_by);

-- Add indexes for common search patterns
CREATE INDEX jobs_date_posted_idx ON public.jobs(date_posted DESC);
CREATE INDEX jobs_region_idx ON public.jobs(region);
CREATE INDEX jobs_industry_idx ON public.jobs(industry);
CREATE INDEX jobs_net_zero_category_idx ON public.jobs(net_zero_category);
CREATE INDEX jobs_job_function_idx ON public.jobs(job_function);
CREATE INDEX jobs_location_type_idx ON public.jobs(location_type);
CREATE INDEX jobs_contract_type_idx ON public.jobs(contract_type);
