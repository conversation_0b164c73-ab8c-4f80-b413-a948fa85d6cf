-- Drop the old view
DROP VIEW IF EXISTS training_course_enrollments_view;

-- Create a security definer function to enforce access control
CREATE OR REPLACE FUNCTION get_enrollment_access(enrollment_row training_course_enrollments)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- User can see their own enrollments or enrollments for courses they created
  RETURN (
    enrollment_row.user_id = auth.uid() OR 
    EXISTS (
      SELECT 1 
      FROM training_courses 
      WHERE id = enrollment_row.course_id 
      AND creator_id = auth.uid()
    )
  );
END;
$$;

-- Create the view with access control built in
CREATE OR REPLACE VIEW training_course_enrollments_view AS
SELECT 
    e.id,
    e.course_id,
    e.user_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.avatar_url,
    p.title as user_title,
    p.organization as user_organization,
    p.email as user_email,
    t.title as course_title,
    t.organization_name as course_provider
FROM 
    training_course_enrollments e
JOIN 
    profiles p ON e.profile_id = p.id
JOIN 
    training_courses t ON e.course_id = t.id
WHERE 
    get_enrollment_access(e);

-- Grant access to authenticated users
GRANT SELECT ON training_course_enrollments_view TO authenticated;
GRANT EXECUTE ON FUNCTION get_enrollment_access TO authenticated;
