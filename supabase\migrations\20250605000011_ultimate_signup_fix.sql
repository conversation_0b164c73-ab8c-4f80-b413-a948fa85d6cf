-- Ultimate signup fix - handle foreign key constraint issues
-- Date: 2025-06-05

BEGIN;

-- Create a completely bulletproof handle_new_user function
-- This version removes notification preferences creation from the trigger
-- to avoid foreign key constraint issues
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
  error_context TEXT;
BEGIN
  -- Step 1: Create profile record (this must succeed)
  BEGIN
    INSERT INTO public.profiles (
      id, 
      first_name, 
      last_name, 
      email, 
      social_visibility, 
      subscription_tier, 
      subscription_status, 
      created_at, 
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.raw_user_meta_data->>'first_name',
      NEW.raw_user_meta_data->>'last_name',
      NEW.email,
      'public',
      'none',
      'trial',
      NEW.created_at,
      NEW.created_at
    )
    RETURNING id INTO profile_id;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- If profile creation fails, try minimal insert
      error_context := 'Full profile creation failed: ' || SQLERRM;
      RAISE WARNING '%', error_context;
      
      BEGIN
        INSERT INTO public.profiles (id, created_at, updated_at)
        VALUES (NEW.id, NEW.created_at, NEW.created_at)
        RETURNING id INTO profile_id;
      EXCEPTION
        WHEN OTHERS THEN
          error_context := 'Minimal profile creation failed: ' || SQLERRM;
          RAISE WARNING '%', error_context;
          profile_id := NEW.id;
      END;
  END;

  -- Step 2: Create user consent settings (only if table exists)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'user_consent_settings'
    ) THEN
      INSERT INTO public.user_consent_settings (
        user_id,
        profile_visibility,
        newsletter_subscription,
        show_businesses,
        show_events,
        show_connections,
        created_at,
        updated_at
      )
      VALUES (
        NEW.id,
        true, false, true, true, true,
        NEW.created_at, NEW.created_at
      )
      ON CONFLICT (user_id) DO NOTHING;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      error_context := 'User consent settings creation failed: ' || SQLERRM;
      RAISE WARNING '%', error_context;
  END;

  -- Step 3: DO NOT create notification preferences in the trigger
  -- This will be handled by the application after the transaction commits
  -- to avoid foreign key constraint timing issues

  -- Always return NEW to ensure signup succeeds
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates profile and user consent settings for new users - notification preferences handled separately';

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create a separate function to create notification preferences after signup
CREATE OR REPLACE FUNCTION public.create_notification_preferences_for_user(user_profile_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  success BOOLEAN := FALSE;
BEGIN
  -- Only create if the profile exists and notification preferences don't exist yet
  IF EXISTS (SELECT 1 FROM public.profiles WHERE id = user_profile_id) 
     AND NOT EXISTS (SELECT 1 FROM public.notification_preferences WHERE profile_id = user_profile_id) THEN
    
    INSERT INTO public.notification_preferences (
      profile_id,
      email_notifications,
      post_likes,
      post_comments,
      comment_replies,
      comment_likes,
      connection_requests,
      connection_accepted,
      event_signups,
      event_updates,
      system_notifications,
      created_at,
      updated_at
    )
    VALUES (
      user_profile_id,
      true, true, true, true, true, true, true, true, true, true,
      NOW(), NOW()
    );
    
    success := TRUE;
  END IF;
  
  RETURN success;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.create_notification_preferences_for_user(UUID) TO authenticated;

COMMIT;
