# NetZero Platform - User Signup Process Analysis

## Current Status (After Fix)

✅ **Signup is working** - Users can successfully create accounts  
❌ **Missing functionality** - Several expected auto-creation processes are not happening

---

## What Currently Happens During Signup

### 1. **Profiles Table** ✅ (Partially Working)
**Current behavior:**
- ✅ Profile record is created with `id`, `created_at`, `updated_at`
- ❌ `first_name` and `last_name` are NOT populated from signup form
- ❌ `email` is NOT populated from auth.users

**Expected behavior:**
- Should populate `first_name` and `last_name` from `NEW.raw_user_meta_data`
- Should populate `email` from `NEW.email`

### 2. **User Consent Settings** ✅ (Working)
**Current behavior:**
- ✅ Record is automatically created in `user_consent_settings`
- ✅ Default privacy settings are applied

**How it works:**
- There appears to be a separate trigger or function creating these records
- Default values: `profile_visibility: true`, `email_notifications: true`, etc.

### 3. **Notification Preferences** ❌ (Not Working)
**Current behavior:**
- ❌ NO records are created in `notification_preferences` table
- ❌ Users have no notification preferences set

**Expected behavior:**
- Should create default notification preference records for each notification type
- Types: `post_like`, `post_comment`, `comment_reply`, etc.
- Default: `email_enabled: true`, `push_enabled: true`

---

## Investigation Needed

To understand the current state, please run the investigation script:

```sql
-- Run this in Supabase SQL Editor
-- File: investigate_current_signup_process.sql
```

This will show us:
1. What the current `handle_new_user` function actually does
2. What triggers are active on `auth.users`
3. If `notification_preferences` table exists and its structure
4. What other functions might be creating `user_consent_settings`
5. Recent signup data to see what's actually being created

---

## What SHOULD Happen During Signup

### Ideal Signup Flow:

1. **User submits signup form** with email, password, first_name, last_name
2. **Supabase Auth creates user** in `auth.users` table
3. **Trigger fires: `on_auth_user_created`** calls `handle_new_user()`
4. **Profile creation:**
   ```sql
   INSERT INTO profiles (
     id, first_name, last_name, email, 
     social_visibility, subscription_tier, subscription_status,
     created_at, updated_at
   ) VALUES (
     NEW.id, 
     NEW.raw_user_meta_data->>'first_name',
     NEW.raw_user_meta_data->>'last_name', 
     NEW.email,
     'public', 'none', 'trial',
     NEW.created_at, NEW.created_at
   );
   ```

5. **User consent settings creation:**
   ```sql
   INSERT INTO user_consent_settings (
     user_id, profile_visibility, email_notifications,
     newsletter_subscription, show_businesses, show_events, 
     show_connections, share_email_with_event_creators,
     share_email_with_attendees, share_contact_details
   ) VALUES (
     NEW.id, true, true, false, true, true, true, false, false, false
   );
   ```

6. **Notification preferences creation:**
   ```sql
   -- For each notification type: post_like, post_comment, comment_reply, 
   -- comment_like, connection_request, connection_accepted, event_signup, event_creation
   INSERT INTO notification_preferences (
     profile_id, type, email_enabled, push_enabled
   ) VALUES (
     NEW.id, 'post_like', true, true
   );
   ```

7. **Welcome notification creation** (optional)

---

## Current Issues to Fix

### Issue 1: Missing Profile Data Population
**Problem:** `first_name`, `last_name`, `email` not populated  
**Cause:** Current `handle_new_user` function is minimal (only ID and timestamps)  
**Fix:** Update function to populate these fields from signup data

### Issue 2: Missing Notification Preferences
**Problem:** No notification preferences created during signup
**Cause:** Function doesn't include notification preferences creation
**Fix:** Add notification preferences creation to signup function

### Issue 3: CRITICAL - Wrong Table Structure! 🚨
**Problem:** Our `NotificationPreferences` component expects flexible structure (`type`, `email_enabled`, `push_enabled`) but the actual table uses old structure (individual boolean columns)
**Cause:** Database has old structure, component was built for new structure
**Fix:** Either migrate table to new structure OR update component to use old structure

### Issue 3: Mystery User Consent Settings Creation
**Problem:** `user_consent_settings` are being created but we don't know how  
**Investigation needed:** Find what trigger/function is creating these  
**Risk:** Might conflict with our fixes

---

## Next Steps

1. **Run investigation script** to understand current state
2. **Identify the mystery function** creating `user_consent_settings`
3. **Update `handle_new_user` function** to:
   - Populate profile data properly
   - Create notification preferences
   - Avoid conflicts with existing consent settings creation
4. **Test thoroughly** to ensure no regressions

---

## Tables Involved

### `profiles` (public schema)
- **Purpose:** User profile information
- **Key columns:** `id`, `first_name`, `last_name`, `email`
- **Current status:** ✅ Created but missing data

### `user_consent_settings` (public schema)  
- **Purpose:** Privacy and consent preferences
- **Key columns:** `user_id`, `profile_visibility`, `email_notifications`
- **Current status:** ✅ Working (mystery function)

### `notification_preferences` (public schema)
- **Purpose:** Notification delivery preferences  
- **Key columns:** `profile_id`, `type`, `email_enabled`, `push_enabled`
- **Current status:** ❌ Not created during signup

---

## Frontend Integration Status

### UserDashboard Settings
- ✅ **NotificationPreferences component** added to UserSettings
- ✅ **Component handles** the flexible notification_preferences structure
- ✅ **UI integration** complete with toggle switches
- ❌ **No data to display** because preferences aren't created during signup

The frontend is ready - we just need to fix the backend to create the notification preferences during signup.
