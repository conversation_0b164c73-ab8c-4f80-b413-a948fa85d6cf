-- Comprehensive RLS Policies for All Tables
-- Date: May 19, 2025

-- First, enable RLS on all tables
DO $$
DECLARE
    table_name text;
BEGIN
    FOR table_name IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', table_name);
    END LOOP;
END
$$;

-- Drop all existing policies to start fresh
DO $$
DECLARE
    policy_record record;
BEGIN
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
            policy_record.policyname, 
            policy_record.schemaname, 
            policy_record.tablename);
    END LOOP;
END
$$;

-- Grant basic schema permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;

-- Grant sequence permissions
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- Profiles table policies
CREATE POLICY "Users can view all profiles"
    ON public.profiles FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can update their own profile"
    ON public.profiles FOR UPDATE
    TO authenticated
    USING (id = auth.uid());

-- Businesses table policies
CREATE POLICY "Users can view all businesses"
    ON public.businesses FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their own businesses"
    ON public.businesses FOR ALL
    TO authenticated
    USING (owner_id = auth.uid())
    WITH CHECK (owner_id = auth.uid());

-- Business standards table policies
CREATE POLICY "Users can view all business standards"
    ON public.business_standards FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their business standards"
    ON public.business_standards FOR ALL
    TO authenticated
    USING (business_id IN (SELECT id FROM public.businesses WHERE owner_id = auth.uid()))
    WITH CHECK (business_id IN (SELECT id FROM public.businesses WHERE owner_id = auth.uid()));

-- Business relevant industries table policies
CREATE POLICY "Users can view all business industries"
    ON public.business_relevant_industries FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their business industries"
    ON public.business_relevant_industries FOR ALL
    TO authenticated
    USING (business_id IN (SELECT id FROM public.businesses WHERE owner_id = auth.uid()))
    WITH CHECK (business_id IN (SELECT id FROM public.businesses WHERE owner_id = auth.uid()));

-- Social posts table policies
CREATE POLICY "Users can view all public posts"
    ON public.social_posts FOR SELECT
    TO authenticated
    USING (visibility = 'public' OR user_id = auth.uid());

CREATE POLICY "Users can manage their own posts"
    ON public.social_posts FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Post comments table policies
CREATE POLICY "Users can view all comments"
    ON public.post_comments FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their own comments"
    ON public.post_comments FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Post likes and comment likes table policies
CREATE POLICY "Users can view all likes"
    ON public.post_likes FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their own likes"
    ON public.post_likes FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can view all comment likes"
    ON public.comment_likes FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their own comment likes"
    ON public.comment_likes FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- User connections table policies
CREATE POLICY "Users can view their connections"
    ON public.user_connections FOR SELECT
    TO authenticated
    USING (user_id = auth.uid() OR connection_id = auth.uid());

CREATE POLICY "Users can manage their connections"
    ON public.user_connections FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Events table policies
CREATE POLICY "Users can view all events"
    ON public.events FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their own events"
    ON public.events FOR ALL
    TO authenticated
    USING (creator_user_id = auth.uid())
    WITH CHECK (creator_user_id = auth.uid());

-- Event signups table policies
CREATE POLICY "Users can view event signups"
    ON public.event_signups FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their own signups"
    ON public.event_signups FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Categories tables policies
CREATE POLICY "Users can view all categories"
    ON public.netzero_categories FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can view all user categories"
    ON public.user_categories FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can manage their categories"
    ON public.user_categories FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Notifications table policies
CREATE POLICY "Users can view their notifications"
    ON public.notifications FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can manage their notifications"
    ON public.notifications FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Grant table permissions
DO $$
DECLARE
    table_name text;
BEGIN
    FOR table_name IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        -- Grant SELECT to all authenticated users
        EXECUTE format('GRANT SELECT ON public.%I TO authenticated', table_name);
        
        -- Grant full access to service role
        EXECUTE format('GRANT ALL ON public.%I TO service_role', table_name);
        
        -- Grant INSERT, UPDATE, DELETE to authenticated users (RLS will restrict access)
        EXECUTE format('GRANT INSERT, UPDATE, DELETE ON public.%I TO authenticated', table_name);
    END LOOP;
END
$$;

-- Special handling for public.industries table (read-only for authenticated users)
CREATE POLICY "Users can view all industries"
    ON public.industries FOR SELECT
    TO authenticated
    USING (true);

-- Create service role policies for all tables
DO $$
DECLARE
    table_name text;
BEGIN
    FOR table_name IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('
            CREATE POLICY "Service role full access to %I"
            ON public.%I FOR ALL
            TO service_role
            USING (true)
            WITH CHECK (true)
        ', table_name, table_name);
    END LOOP;
END
$$;

-- Verify policies
DO $$
DECLARE
    table_name text;
    policy_count int;
BEGIN
    FOR table_name IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        SELECT COUNT(*) INTO policy_count
        FROM pg_policies
        WHERE schemaname = 'public' AND tablename = table_name;
        
        IF policy_count = 0 THEN
            RAISE NOTICE 'Warning: No policies found for table: %', table_name;
        END IF;
    END LOOP;
END
$$;
