-- Verification script for privacy settings consolidation
-- Run this after applying the migration to verify everything is working

-- 1. Check that duplicate columns have been removed from profiles table
SELECT 
    'Profiles table columns' as section,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
AND column_name IN (
    'profile_visibility', 
    'email_notifications', 
    'newsletter_subscription',
    'show_businesses', 
    'show_events', 
    'show_connections'
)
ORDER BY column_name;

-- 2. Check that user_consent_settings table has all the required columns
SELECT 
    'User consent settings columns' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'user_consent_settings' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check that all profiles have corresponding consent settings
SELECT 
    'Data consistency check' as section,
    COUNT(*) as total_profiles,
    (SELECT COUNT(*) FROM user_consent_settings) as total_consent_settings,
    COUNT(*) - (SELECT COUNT(*) FROM user_consent_settings) as missing_consent_settings
FROM profiles;

-- 4. Check that views were recreated successfully
SELECT 
    'Recreated views' as section,
    viewname
FROM pg_views 
WHERE schemaname = 'public' 
AND viewname IN (
    'public_professional_profiles',
    'professionals_with_search',
    'events_with_creator',
    'notifications_with_details'
)
ORDER BY viewname;

-- 5. Check that RLS policies are working
SELECT 
    'RLS policies' as section,
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'profiles'
AND policyname = 'Allow profile access for events and users';

-- 6. Test the public_professional_profiles view
SELECT 
    'Public profiles test' as section,
    COUNT(*) as visible_profiles
FROM public_professional_profiles;

-- 7. Check current user's consent settings (if logged in)
SELECT 
    'My consent settings' as section,
    profile_visibility,
    email_notifications,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details
FROM user_consent_settings 
WHERE user_id = auth.uid();

-- 8. Test that profile visibility filtering works
SELECT 
    'Profile visibility test' as section,
    'Public profiles' as type,
    COUNT(*) as count
FROM profiles p
WHERE EXISTS (
    SELECT 1 FROM user_consent_settings ucs 
    WHERE ucs.user_id = p.id 
    AND ucs.profile_visibility = true
)

UNION ALL

SELECT 
    'Profile visibility test' as section,
    'Private profiles' as type,
    COUNT(*) as count
FROM profiles p
WHERE EXISTS (
    SELECT 1 FROM user_consent_settings ucs 
    WHERE ucs.user_id = p.id 
    AND ucs.profile_visibility = false
);
