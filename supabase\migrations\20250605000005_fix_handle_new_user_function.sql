-- Fix handle_new_user function to work with actual table structures
-- Date: 2025-06-05

B<PERSON>IN;

-- Update handle_new_user function to work with current table structure
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
BEGIN
  -- Insert a new profile record for the new user
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    email, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  )
  RETURNING id INTO profile_id;

  -- Create default user_consent_settings for the new user
  -- Only insert into columns that actually exist
  INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    true,  -- profile_visibility - default to public
    false, -- newsletter_subscription - default to disabled
    true,  -- show_businesses - default to visible
    true,  -- show_events - default to visible
    true,  -- show_connections - default to visible
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (user_id) DO NOTHING;

  -- Create notification preferences
  -- Only insert if the table exists and has the expected structure
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'notification_preferences'
  ) THEN
    -- Check if table has individual boolean columns structure
    IF EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'notification_preferences' 
      AND column_name = 'post_likes'
    ) THEN
      -- Insert with individual boolean columns
      INSERT INTO public.notification_preferences (
        profile_id,
        email_notifications,
        post_likes,
        post_comments,
        comment_replies,
        comment_likes,
        connection_requests,
        connection_accepted,
        event_signups,
        event_updates,
        system_notifications,
        created_at,
        updated_at
      )
      VALUES (
        profile_id,
        true, -- email_notifications
        true, -- post_likes
        true, -- post_comments
        true, -- comment_replies
        true, -- comment_likes
        true, -- connection_requests
        true, -- connection_accepted
        true, -- event_signups
        true, -- event_updates
        true, -- system_notifications
        NOW(),
        NOW()
      )
      ON CONFLICT (profile_id) DO NOTHING;
    END IF;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the signup
    RAISE WARNING 'Error in handle_new_user function: %', SQLERRM;
    -- Still return NEW so the user signup doesn't fail
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment to reflect the current functionality
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates a profile record, user consent settings, and notification preferences when a new user signs up (fixed for current table structure)';

COMMIT;
