-- Add stored procedure for safe enrollment creation
CREATE OR REPLACE FUNCTION create_enrollment_safely(
    p_course_id UUID,
    p_user_id UUID,
    p_request_message TEXT,
    p_gdpr_consent BOOLEAN
) RETURNS VOID AS $$
BEGIN
    -- Check if enrollment exists in a transaction
    PERFORM pg_advisory_xact_lock(hashtext('create_enrollment_' || p_course_id::text || '_' || p_user_id::text));
    
    IF EXISTS (
        SELECT 1 
        FROM training_course_enrollments 
        WHERE course_id = p_course_id 
        AND user_id = p_user_id
    ) THEN
        RAISE EXCEPTION 'Duplicate enrollment' USING ERRCODE = '23505';
    END IF;

    -- If no enrollment exists, create one
    INSERT INTO training_course_enrollments (
        course_id,
        user_id,
        status,
        request_message,
        gdpr_consent
    ) VALUES (
        p_course_id,
        p_user_id,
        'pending',
        p_request_message,
        p_gdpr_consent
    );

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
