-- NOTIFICATIONS AND MESSAGING SYSTEM FOR NETZERO PLATFORM
-- This migration adds a notifications system and direct messaging capability

---------------------------------------------------------
-- PART 1: CREATE NOTIFICATIONS TABLE
---------------------------------------------------------

-- Create the notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    related_id UUID,
    related_type VARCHAR(50),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes to improve notification retrieval
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);

-- Add RLS to the notifications table
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can view their own notifications
CREATE POLICY "Users can view their own notifications" 
    ON public.notifications 
    FOR SELECT
    USING (auth.uid() = user_id);

-- Users can mark their notifications as read/unread
CREATE POLICY "Users can update their own notifications" 
    ON public.notifications 
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Only service role can insert notifications
CREATE POLICY "Service role can insert notifications" 
    ON public.notifications 
    FOR INSERT
    WITH CHECK (auth.role() = 'service_role');

-- Create function to mark a notification as read
CREATE OR REPLACE FUNCTION mark_notification_as_read(notification_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
    v_updated BOOLEAN;
BEGIN
    UPDATE notifications
    SET is_read = TRUE,
        updated_at = now()
    WHERE id = notification_id AND user_id = auth.uid();
    
    GET DIAGNOSTICS v_updated = ROW_COUNT;
    
    RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Create function to mark all notifications as read
CREATE OR REPLACE FUNCTION mark_all_notifications_as_read()
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
    v_updated BOOLEAN;
BEGIN
    UPDATE notifications
    SET is_read = TRUE,
        updated_at = now()
    WHERE user_id = auth.uid() AND is_read = FALSE;
    
    GET DIAGNOSTICS v_updated = ROW_COUNT;
    
    RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT, UPDATE ON notifications TO authenticated;
GRANT EXECUTE ON FUNCTION mark_notification_as_read TO authenticated;
GRANT EXECUTE ON FUNCTION mark_all_notifications_as_read TO authenticated;

---------------------------------------------------------
-- PART 2: CREATE DIRECT MESSAGING TABLES
---------------------------------------------------------

-- Create the conversations table
CREATE TABLE IF NOT EXISTS public.conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create the conversation_participants table
CREATE TABLE IF NOT EXISTS public.conversation_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(conversation_id, user_id)
);

-- Create indexes for conversation participants
CREATE INDEX IF NOT EXISTS idx_conv_participants_conversation_id ON conversation_participants(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conv_participants_user_id ON conversation_participants(user_id);

-- Create the messages table
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for messages
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);

-- Create message limits table to enforce weekly limits for free users
CREATE TABLE IF NOT EXISTS public.message_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    week_start DATE NOT NULL, -- Start of the current week
    message_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, week_start)
);

-- Create index for message limits
CREATE INDEX IF NOT EXISTS idx_message_limits_user_id ON message_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_message_limits_week_start ON message_limits(week_start);

-- Add RLS to all tables
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_limits ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for conversations
CREATE POLICY "Users can view conversations they're part of" 
    ON public.conversations 
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM conversation_participants 
        WHERE conversation_participants.conversation_id = conversations.id 
        AND conversation_participants.user_id = auth.uid()
    ));

-- Create RLS policies for conversation_participants
CREATE POLICY "Users can view conversation participants for their conversations" 
    ON public.conversation_participants 
    FOR SELECT
    USING (user_id = auth.uid() OR EXISTS (
        SELECT 1 FROM conversation_participants cp 
        WHERE cp.conversation_id = conversation_participants.conversation_id 
        AND cp.user_id = auth.uid()
    ));

-- Create RLS policies for messages
CREATE POLICY "Users can view messages in their conversations" 
    ON public.messages 
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM conversation_participants 
        WHERE conversation_participants.conversation_id = messages.conversation_id 
        AND conversation_participants.user_id = auth.uid()
    ));

CREATE POLICY "Users can send messages in their conversations" 
    ON public.messages 
    FOR INSERT
    WITH CHECK (
        sender_id = auth.uid() AND 
        EXISTS (
            SELECT 1 FROM conversation_participants 
            WHERE conversation_participants.conversation_id = messages.conversation_id 
            AND conversation_participants.user_id = auth.uid()
        )
    );

-- Create RLS policies for message_limits
CREATE POLICY "Users can view their own message limits" 
    ON public.message_limits 
    FOR SELECT
    USING (user_id = auth.uid());

-- Create functions for direct messaging

-- Function to start a new conversation
CREATE OR REPLACE FUNCTION start_conversation(participant_ids UUID[])
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
    v_conversation_id UUID;
    v_participant_id UUID;
BEGIN
    -- Check if the current user is in the participant list
    IF NOT participant_ids @> ARRAY[auth.uid()] THEN
        RAISE EXCEPTION 'You must be a participant in the conversation';
    END IF;
    
    -- Check if conversation already exists between these users (for 2 participants)
    IF array_length(participant_ids, 1) = 2 THEN
        SELECT cp1.conversation_id INTO v_conversation_id
        FROM conversation_participants cp1
        JOIN conversation_participants cp2 ON cp1.conversation_id = cp2.conversation_id
        WHERE cp1.user_id = participant_ids[1] AND cp2.user_id = participant_ids[2]
        GROUP BY cp1.conversation_id
        HAVING COUNT(DISTINCT cp1.user_id) = 2;
        
        IF v_conversation_id IS NOT NULL THEN
            RETURN v_conversation_id;
        END IF;
    END IF;
    
    -- Create new conversation
    INSERT INTO conversations DEFAULT VALUES RETURNING id INTO v_conversation_id;
    
    -- Add participants
    FOREACH v_participant_id IN ARRAY participant_ids
    LOOP
        INSERT INTO conversation_participants (conversation_id, user_id)
        VALUES (v_conversation_id, v_participant_id);
    END LOOP;
    
    RETURN v_conversation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to send a message
CREATE OR REPLACE FUNCTION send_message(p_conversation_id UUID, p_content TEXT)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
    v_message_id UUID;
    v_user_id UUID;
    v_subscription_tier TEXT;
    v_current_week DATE;
    v_message_count INTEGER;
    v_message_limit INTEGER := 5; -- Default limit for free users
BEGIN
    v_user_id := auth.uid();
    v_current_week := date_trunc('week', CURRENT_DATE)::DATE;
    
    -- Get user's subscription tier
    SELECT subscription_tier INTO v_subscription_tier
    FROM profiles
    WHERE id = v_user_id;
    
    -- Check if user is not on a paid tier
    IF v_subscription_tier NOT IN ('premium', 'business') THEN
        -- Get user's message count for the current week
        SELECT message_count INTO v_message_count
        FROM message_limits
        WHERE user_id = v_user_id AND week_start = v_current_week;
        
        -- If no record exists yet, create one
        IF v_message_count IS NULL THEN
            INSERT INTO message_limits (user_id, week_start, message_count)
            VALUES (v_user_id, v_current_week, 0)
            RETURNING message_count INTO v_message_count;
        END IF;
        
        -- Check if user has reached the weekly limit
        IF v_message_count >= v_message_limit THEN
            RAISE EXCEPTION 'You have reached your weekly message limit. Upgrade to a paid account for unlimited messaging.';
        END IF;
        
        -- Update message count
        UPDATE message_limits
        SET message_count = message_count + 1,
            updated_at = now()
        WHERE user_id = v_user_id AND week_start = v_current_week;
    END IF;
    
    -- Insert the message
    INSERT INTO messages (conversation_id, sender_id, content)
    VALUES (p_conversation_id, v_user_id, p_content)
    RETURNING id INTO v_message_id;
    
    -- Update conversation timestamp
    UPDATE conversations
    SET updated_at = now()
    WHERE id = p_conversation_id;    -- Create notifications for other participants
    PERFORM create_notification(
        cp.user_id,
        'new_message',
        'You have a new message',
        p_conversation_id,
        'conversation',
        v_user_id
    )
    FROM conversation_participants cp
    WHERE cp.conversation_id = p_conversation_id AND cp.user_id != v_user_id;
    
    RETURN v_message_id;
END;
$$ LANGUAGE plpgsql;

-- Function to mark a message as read
CREATE OR REPLACE FUNCTION mark_message_as_read(p_message_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
    v_updated BOOLEAN;
BEGIN
    UPDATE messages
    SET is_read = TRUE,
        updated_at = now()
    WHERE id = p_message_id
    AND EXISTS (
        SELECT 1 FROM conversation_participants 
        WHERE conversation_participants.conversation_id = messages.conversation_id 
        AND conversation_participants.user_id = auth.uid()
    )
    AND sender_id != auth.uid();
    
    GET DIAGNOSTICS v_updated = ROW_COUNT;
    
    RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to mark all messages in a conversation as read
CREATE OR REPLACE FUNCTION mark_conversation_messages_as_read(p_conversation_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
    v_updated BOOLEAN;
BEGIN
    UPDATE messages
    SET is_read = TRUE,
        updated_at = now()
    WHERE conversation_id = p_conversation_id
    AND EXISTS (
        SELECT 1 FROM conversation_participants 
        WHERE conversation_participants.conversation_id = messages.conversation_id 
        AND conversation_participants.user_id = auth.uid()
    )
    AND sender_id != auth.uid()
    AND is_read = FALSE;
    
    GET DIAGNOSTICS v_updated = ROW_COUNT;
    
    RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to get unread message count
CREATE OR REPLACE FUNCTION get_unread_message_count()
RETURNS INTEGER
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM messages m
    JOIN conversation_participants cp ON m.conversation_id = cp.conversation_id
    WHERE cp.user_id = auth.uid()
    AND m.sender_id != auth.uid()
    AND m.is_read = FALSE;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT ON conversations TO authenticated;
GRANT SELECT ON conversation_participants TO authenticated;
GRANT SELECT, INSERT ON messages TO authenticated;
GRANT SELECT ON message_limits TO authenticated;

GRANT EXECUTE ON FUNCTION start_conversation TO authenticated;
GRANT EXECUTE ON FUNCTION send_message TO authenticated;
GRANT EXECUTE ON FUNCTION mark_message_as_read TO authenticated;
GRANT EXECUTE ON FUNCTION mark_conversation_messages_as_read TO authenticated;
GRANT EXECUTE ON FUNCTION get_unread_message_count TO authenticated;

---------------------------------------------------------
-- PART 3: CREATE AUTO-NOTIFICATION TRIGGERS
---------------------------------------------------------

-- Trigger function for post like notifications
CREATE OR REPLACE FUNCTION notify_post_like()
RETURNS TRIGGER AS $$
BEGIN
    -- Skip notification if user is liking their own post
    IF NEW.user_id = (SELECT user_id FROM social_posts WHERE id = NEW.post_id) THEN
        RETURN NEW;
    END IF;
    
    -- Create notification for post owner
    PERFORM create_notification(
        sp.user_id,
        'post_like',
        'Someone liked your post',
        NEW.post_id,
        'post'
    )
    FROM social_posts sp
    WHERE sp.id = NEW.post_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for post likes
CREATE TRIGGER post_like_notification_trigger
AFTER INSERT ON post_likes
FOR EACH ROW EXECUTE FUNCTION notify_post_like();

-- Trigger function for comment notifications
CREATE OR REPLACE FUNCTION notify_post_comment()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is a reply to a comment
    IF NEW.parent_comment_id IS NOT NULL THEN
        -- Skip notification if user is replying to their own comment
        IF NEW.user_id = (SELECT user_id FROM post_comments WHERE id = NEW.parent_comment_id) THEN
            RETURN NEW;
        END IF;
        
        -- Create notification for parent comment owner
        INSERT INTO notifications (
            user_id, 
            type, 
            related_entity_id, 
            related_entity_type, 
            actor_id, 
            message, 
            data
        )
        SELECT 
            pc.user_id,
            'comment_reply',
            NEW.post_id,
            'post',
            NEW.user_id,
            'Someone replied to your comment',
            json_build_object(
                'post_id', NEW.post_id,
                'comment_id', NEW.id,
                'actor_name', COALESCE(p.first_name || ' ' || p.last_name, 'A user'),
                'comment_preview', left(NEW.content, 100)
            )
        FROM post_comments pc
        LEFT JOIN profiles p ON NEW.user_id = p.id
        WHERE pc.id = NEW.parent_comment_id;
    ELSE
        -- Skip notification if user is commenting on their own post
        IF NEW.user_id = (SELECT user_id FROM social_posts WHERE id = NEW.post_id) THEN
            RETURN NEW;
        END IF;
        
        -- Create notification for post owner
        PERFORM create_notification(
            sp.user_id,
            'post_comment',
            'Someone commented on your post',
            NEW.post_id,
            'post'
        )
        FROM social_posts sp
        WHERE sp.id = NEW.post_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for comments
CREATE TRIGGER post_comment_notification_trigger
AFTER INSERT ON post_comments
FOR EACH ROW EXECUTE FUNCTION notify_post_comment();

-- Trigger function for comment like notifications
CREATE OR REPLACE FUNCTION notify_comment_like()
RETURNS TRIGGER AS $$
BEGIN
    -- Skip notification if user is liking their own comment
    IF NEW.user_id = (SELECT user_id FROM post_comments WHERE id = NEW.comment_id) THEN
        RETURN NEW;
    END IF;
    
    -- Create notification for comment owner
    INSERT INTO notifications (
        user_id, 
        type, 
        related_entity_id, 
        related_entity_type, 
        actor_id, 
        message, 
        data
    )
    SELECT 
        pc.user_id,
        'comment_like',
        pc.post_id,
        'comment',
        NEW.user_id,
        'Someone liked your comment',
        json_build_object(
            'post_id', pc.post_id,
            'comment_id', NEW.comment_id,
            'actor_name', COALESCE(p.first_name || ' ' || p.last_name, 'A user')
        )
    FROM post_comments pc
    LEFT JOIN profiles p ON NEW.user_id = p.id
    WHERE pc.id = NEW.comment_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for comment likes
CREATE TRIGGER comment_like_notification_trigger
AFTER INSERT ON comment_likes
FOR EACH ROW EXECUTE FUNCTION notify_comment_like();

-- Trigger function for connection request notifications
CREATE OR REPLACE FUNCTION notify_connection_request()
RETURNS TRIGGER AS $$
BEGIN
    -- Create notification for connection request recipient
    INSERT INTO notifications (
        user_id, 
        type, 
        related_entity_id, 
        related_entity_type, 
        actor_id, 
        message, 
        data
    )
    SELECT 
        NEW.connection_id,
        'connection_request',
        NEW.id,
        'connection',
        NEW.user_id,
        'Someone wants to connect with you',
        json_build_object(
            'connection_id', NEW.id,
            'actor_name', COALESCE(p.first_name || ' ' || p.last_name, 'A user')
        )
    FROM profiles p
    WHERE p.id = NEW.user_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for connection requests
CREATE TRIGGER connection_request_notification_trigger
AFTER INSERT ON user_connections
FOR EACH ROW 
WHEN (NEW.status = 'pending')
EXECUTE FUNCTION notify_connection_request();

-- Trigger function for connection acceptance notifications
CREATE OR REPLACE FUNCTION notify_connection_accepted()
RETURNS TRIGGER AS $$
BEGIN
    -- Only notify if status changed from pending to accepted
    IF OLD.status = 'pending' AND NEW.status = 'accepted' THEN
        -- Create notification for the person who sent the request
        INSERT INTO notifications (
            user_id, 
            type, 
            related_entity_id, 
            related_entity_type, 
            actor_id, 
            message, 
            data
        )
        SELECT 
            NEW.user_id,
            'connection_accepted',
            NEW.id,
            'connection',
            NEW.connection_id,
            'Someone accepted your connection request',
            json_build_object(
                'connection_id', NEW.id,
                'actor_name', COALESCE(p.first_name || ' ' || p.last_name, 'A user')
            )
        FROM profiles p
        WHERE p.id = NEW.connection_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for connection acceptances
CREATE TRIGGER connection_accepted_notification_trigger
AFTER UPDATE ON user_connections
FOR EACH ROW EXECUTE FUNCTION notify_connection_accepted();

-- Trigger function for event creation notifications
CREATE OR REPLACE FUNCTION notify_event_creation()
RETURNS TRIGGER AS $$
DECLARE
    v_category_id UUID;
BEGIN
    -- For each category of the event, notify users interested in that category
    IF NEW.netzero_category_ids IS NOT NULL THEN
        FOREACH v_category_id IN ARRAY NEW.netzero_category_ids
        LOOP
            INSERT INTO notifications (
                user_id, 
                type, 
                related_entity_id, 
                related_entity_type, 
                actor_id, 
                message, 
                data
            )
            SELECT 
                uc.user_id,
                'event_category_match',
                NEW.id,
                'event',
                NEW.creator_user_id,
                'New event in a category you follow',
                json_build_object(
                    'event_id', NEW.id,
                    'event_title', NEW.title,
                    'event_date', NEW.start_date,
                    'category_id', v_category_id,
                    'actor_name', COALESCE(p.first_name || ' ' || p.last_name, 'Event creator')
                )
            FROM user_categories uc
            LEFT JOIN profiles p ON NEW.creator_user_id = p.id
            WHERE uc.category_id = v_category_id::UUID
            AND uc.user_id != NEW.creator_user_id;  -- Don't notify the creator
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for event creation
CREATE TRIGGER event_creation_notification_trigger
AFTER INSERT ON events
FOR EACH ROW EXECUTE FUNCTION notify_event_creation();

-- Trigger function for event signup notifications
CREATE OR REPLACE FUNCTION notify_event_signup()
RETURNS TRIGGER AS $$
BEGIN
    -- Skip notification if event creator signs up for their own event
    IF NEW.user_id = (SELECT creator_user_id FROM events WHERE id = NEW.event_id) THEN
        RETURN NEW;
    END IF;
    
    -- Create notification for event creator
    INSERT INTO notifications (
        user_id, 
        type, 
        related_entity_id, 
        related_entity_type, 
        actor_id, 
        message, 
        data
    )
    SELECT 
        e.creator_user_id,
        'event_signup',
        NEW.event_id,
        'event',
        NEW.user_id,
        'Someone signed up for your event',
        json_build_object(
            'event_id', NEW.event_id,
            'event_title', e.title,
            'actor_name', COALESCE(p.first_name || ' ' || p.last_name, 'A user')
        )
    FROM events e
    LEFT JOIN profiles p ON NEW.user_id = p.id
    WHERE e.id = NEW.event_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for event signups
CREATE TRIGGER event_signup_notification_trigger
AFTER INSERT ON event_signups
FOR EACH ROW EXECUTE FUNCTION notify_event_signup();

---------------------------------------------------------
-- PART 4: CREATE CONVENIENT VIEWS
---------------------------------------------------------

-- View to show user notifications with actor information
DROP VIEW IF EXISTS user_notifications_with_details;

-- Create the view to match the actual database structure
CREATE OR REPLACE VIEW user_notifications_with_details AS
SELECT 
    n.id,
    n.user_id,
    n.type,
    n.related_id AS related_entity_id,
    n.related_type AS related_entity_type,
    n.content AS message,
    NULL::JSONB AS data,
    n.is_read,
    n.created_at,
    n.updated_at,
    NULL AS actor_first_name,
    NULL AS actor_last_name,
    NULL AS actor_avatar_url
FROM notifications n;

-- View to show conversations with latest message and participants
CREATE OR REPLACE VIEW conversations_with_details AS
SELECT 
    c.id,
    c.created_at,
    c.updated_at,
    (
        SELECT json_agg(json_build_object(
            'user_id', p.id,
            'first_name', p.first_name,
            'last_name', p.last_name,
            'avatar_url', p.avatar_url
        ))
        FROM conversation_participants cp
        JOIN profiles p ON cp.user_id = p.id
        WHERE cp.conversation_id = c.id
    ) AS participants,
    (
        SELECT json_build_object(
            'id', m.id,
            'sender_id', m.sender_id,
            'content', m.content,
            'created_at', m.created_at,
            'is_read', m.is_read
        )
        FROM messages m
        WHERE m.conversation_id = c.id
        ORDER BY m.created_at DESC
        LIMIT 1
    ) AS latest_message,
    (
        SELECT COUNT(*)
        FROM messages m
        WHERE m.conversation_id = c.id
    ) AS message_count,
    (
        SELECT COUNT(*)
        FROM messages m
        WHERE m.conversation_id = c.id
        AND m.is_read = FALSE
        AND m.sender_id != auth.uid()
    ) AS unread_count
FROM conversations c;

-- Grant permissions to the views
GRANT SELECT ON user_notifications_with_details TO authenticated;
GRANT SELECT ON conversations_with_details TO authenticated;

---------------------------------------------------------
-- PART 5: CREATE NOTIFICATION MANAGEMENT FUNCTIONS
---------------------------------------------------------

-- Function to send a manual notification to specific users
CREATE OR REPLACE FUNCTION send_notification(
    p_user_ids UUID[],
    p_type VARCHAR(50),
    p_related_entity_id UUID,
    p_related_entity_type VARCHAR(50),
    p_message TEXT,
    p_data JSONB
)
RETURNS INTEGER
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER := 0;
    v_user_id UUID;
BEGIN
    -- Only allow service role to call this function
    IF auth.role() != 'service_role' THEN
        RAISE EXCEPTION 'Only service role can send manual notifications';
    END IF;
    
    -- Send notification to each user
    FOREACH v_user_id IN ARRAY p_user_ids
    LOOP
        INSERT INTO notifications (
            user_id, 
            type, 
            related_entity_id, 
            related_entity_type, 
            actor_id, 
            message, 
            data
        )
        VALUES (
            v_user_id,
            p_type,
            p_related_entity_id,
            p_related_entity_type,
            auth.uid(),
            p_message,
            p_data
        );
        
        v_count := v_count + 1;
    END LOOP;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Function to send a notification to all users interested in a category
CREATE OR REPLACE FUNCTION send_notification_by_category(
    p_category_id UUID,
    p_type VARCHAR(50),
    p_related_entity_id UUID,
    p_related_entity_type VARCHAR(50),
    p_message TEXT,
    p_data JSONB
)
RETURNS INTEGER
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER := 0;
BEGIN
    -- Only allow service role to call this function
    IF auth.role() != 'service_role' THEN
        RAISE EXCEPTION 'Only service role can send category notifications';
    END IF;
    
    -- Insert notifications for all users interested in this category
    INSERT INTO notifications (
        user_id, 
        type, 
        related_entity_id, 
        related_entity_type, 
        actor_id, 
        message, 
        data
    )
    SELECT 
        uc.user_id,
        p_type,
        p_related_entity_id,
        p_related_entity_type,
        auth.uid(),
        p_message,
        p_data
    FROM user_categories uc
    WHERE uc.category_id = p_category_id;
    
    GET DIAGNOSTICS v_count = ROW_COUNT;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Function to delete a notification
CREATE OR REPLACE FUNCTION delete_notification(p_notification_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
    v_deleted BOOLEAN;
BEGIN
    DELETE FROM notifications
    WHERE id = p_notification_id AND user_id = auth.uid();
    
    GET DIAGNOSTICS v_deleted = ROW_COUNT;
    
    RETURN v_deleted > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to delete all read notifications
CREATE OR REPLACE FUNCTION delete_read_notifications()
RETURNS INTEGER
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER;
BEGIN
    DELETE FROM notifications
    WHERE user_id = auth.uid() AND is_read = TRUE;
    
    GET DIAGNOSTICS v_count = ROW_COUNT;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count()
RETURNS INTEGER
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM notifications
    WHERE user_id = auth.uid() AND is_read = FALSE;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION send_notification TO service_role;
GRANT EXECUTE ON FUNCTION send_notification_by_category TO service_role;
GRANT EXECUTE ON FUNCTION delete_notification TO authenticated;
GRANT EXECUTE ON FUNCTION delete_read_notifications TO authenticated;
GRANT EXECUTE ON FUNCTION get_unread_notification_count TO authenticated;

-- Create a helper function to insert notifications with the correct column structure
CREATE OR REPLACE FUNCTION create_notification(
    p_user_id UUID,
    p_type VARCHAR(50),
    p_message TEXT,
    p_related_id UUID,
    p_related_type VARCHAR(50),
    p_actor_id UUID DEFAULT NULL
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
    v_notification_id UUID;
BEGIN
    -- Insert notification with the current database structure
    INSERT INTO notifications (
        id, 
        user_id,
        type,
        content,
        related_id,
        related_type,
        is_read,
        created_at,
        updated_at
    )
    VALUES (
        gen_random_uuid(),
        p_user_id,
        p_type,
        p_message,
        p_related_id,
        p_related_type,
        FALSE,
        now(),
        now()
    )
    RETURNING id INTO v_notification_id;
    
    RETURN v_notification_id;
END;
$$ LANGUAGE plpgsql;
