-- Clean up duplicate and conflicting policies on businesses table
BEGIN;

-- 1. Drop all existing policies on businesses table
DROP POLICY IF EXISTS "Enable read access for all" ON public.businesses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.businesses;
DROP POLICY IF EXISTS "Enable update for business owners" ON public.businesses;
DROP POLICY IF EXISTS "Enable delete for business owners" ON public.businesses;
DROP POLICY IF EXISTS "Users can manage their own businesses" ON public.businesses;
DROP POLICY IF EXISTS "Users can view all businesses" ON public.businesses;
DROP POLICY IF EXISTS "Service role full access to businesses" ON public.businesses;

-- 2. Create simplified policies

-- Allow all authenticated users to view all businesses
CREATE POLICY "Enable read access for all"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

-- Allow service role full access
CREATE POLICY "Service role full access"
    ON public.businesses
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Allow owners to manage their own businesses
CREATE POLICY "Owners can manage their businesses"
    ON public.businesses
    FOR ALL
    TO authenticated
    USING (auth.uid() = owner_id)
    WITH CHECK (auth.uid() = owner_id);

-- Re-grant permissions
GRANT ALL ON public.businesses TO service_role;
GRANT ALL ON public.businesses TO authenticated;

-- 3. Enable RLS
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;

-- 4. Verify final policies
SELECT
    schemaname,
    tablename,
    policyname,
    roles,
    cmd
FROM
    pg_policies
WHERE
    tablename = 'businesses'
ORDER BY
    policyname;

-- 5. Verify access to related tables needed for nested queries
SELECT 
    pc.relname as table_name,
    has_table_privilege('authenticated', pc.oid, 'SELECT') as can_select,
    has_table_privilege('authenticated', pc.oid, 'INSERT') as can_insert,
    has_table_privilege('authenticated', pc.oid, 'UPDATE') as can_update,
    has_table_privilege('authenticated', pc.oid, 'DELETE') as can_delete
FROM 
    pg_class pc
    JOIN pg_namespace pn ON pc.relnamespace = pn.oid
WHERE 
    pn.nspname = 'public' 
    AND pc.relkind = 'r'
    AND pc.relname IN (
        'businesses',
        'industries',
        'locations',
        'netzero_categories',
        'business_relevant_industries',
        'business_service_locations'
    );

COMMIT;
