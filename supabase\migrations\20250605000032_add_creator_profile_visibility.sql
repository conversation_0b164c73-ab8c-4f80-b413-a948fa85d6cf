-- Add creator profile_visibility to events_with_creator view
-- This enables respecting privacy settings for event organizer profiles
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- UPDATE THE EVENTS_WITH_CREATOR VIEW TO INCLUDE PROFILE_VISIBILITY
-- ================================================================

-- Drop the existing view
DROP VIEW IF EXISTS public.events_with_creator CASCADE;

-- Recreate the view with creator profile_visibility setting
CREATE OR REPLACE VIEW public.events_with_creator AS
SELECT 
    e.id,
    e.creator_user_id,
    e.title,
    e.start_date,
    e.start_time,
    e.end_date,
    e.end_time,
    e.description,
    e.image_url,
    e.event_type,
    e.physical_location,
    e.meeting_url,
    e.event_category,
    e.tags,
    e.created_at,
    e.updated_at,
    e.industry_ids,
    e.netzero_category_ids,
    e.owner_id,
    e.status,
    e.capacity,
    e.is_private,
    e.category,
    e.industry,
    -- Enhanced creator information from profiles table with privacy controls
    concat(p.first_name, ' ', p.last_name) AS creator_name,
    p.avatar_url AS creator_avatar_url,
    p.title AS creator_title,
    p.organization AS creator_organization,
    p.profile_visibility AS creator_profile_visibility
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id;

-- Grant permissions to the view
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;

-- Add comment explaining the privacy-aware view
COMMENT ON VIEW public.events_with_creator IS 
'Events view with creator information including profile_visibility setting. Respects creator privacy preferences for profile linking.';

-- ================================================================
-- TEST THE UPDATED VIEW
-- ================================================================

-- Test the view with profile visibility information
SELECT 
    'PROFILE_VISIBILITY_TEST' as test_type,
    COUNT(*) as total_events,
    COUNT(CASE WHEN creator_profile_visibility = true THEN 1 END) as public_creator_profiles,
    COUNT(CASE WHEN creator_profile_visibility = false THEN 1 END) as private_creator_profiles,
    COUNT(CASE WHEN creator_profile_visibility IS NULL THEN 1 END) as unknown_creator_visibility
FROM public.events_with_creator;

-- Show sample data to verify profile visibility is included
SELECT 
    'SAMPLE_VISIBILITY_DATA' as test_type,
    id,
    title,
    creator_name,
    creator_profile_visibility,
    CASE 
        WHEN creator_profile_visibility = true THEN 'PUBLIC_PROFILE'
        WHEN creator_profile_visibility = false THEN 'PRIVATE_PROFILE'
        ELSE 'UNKNOWN_VISIBILITY'
    END as visibility_status
FROM public.events_with_creator
LIMIT 5;

COMMIT;
