# 💳 Stripe Integration Guide - NetZero Platform

## 🎯 **Simple Startup Monetization**

### **User Tiers:**
- **SEED (Free):** £0/month - 5 posts/week (images), 1 business, 1 job/event/month, 5 connections/week
- **SAPLING:** £5.99/month - Unlimited posts (images+videos), 5 jobs/events/month, 10 connections/week  
- **WOODLAND:** £10.99/month - Everything unlimited

### **Business Ads:**
- **SMALL:** £29.99/month - Sidebar banner
- **MEDIUM:** £59.99/month - Content area banner
- **LARGE:** £99.99/month - Top page banner

---

## 🚀 **Step 1: Set Up Stripe (Test Mode)**

### **1.1 Create Stripe Account:**
1. Go to [stripe.com](https://stripe.com)
2. Sign up for account
3. **Stay in TEST MODE** for now
4. Get your test API keys

### **1.2 Create Products in Stripe Dashboard:**

**User Subscriptions:**
```
Product: "Sapling Membership"
Price: £5.99/month
ID: price_sapling_monthly

Product: "Woodland Membership"  
Price: £10.99/month
ID: price_woodland_monthly
```

**Business Ads:**
```
Product: "Small Ad"
Price: £29.99/month
ID: price_small_ad_monthly

Product: "Medium Ad"
Price: £59.99/month  
ID: price_medium_ad_monthly

Product: "Large Ad"
Price: £99.99/month
ID: price_large_ad_monthly
```

---

## 🔧 **Step 2: Install Stripe in Your Project**

```bash
npm install @stripe/stripe-js stripe
```

### **2.1 Environment Variables:**
Add to your `.env.local`:
```
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

---

## 💻 **Step 3: Frontend Integration**

### **3.1 Create Stripe Context:**
```typescript
// src/contexts/StripeContext.tsx
import { loadStripe } from '@stripe/stripe-js';
import { createContext, useContext } from 'react';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

const StripeContext = createContext(stripePromise);

export const useStripe = () => useContext(StripeContext);

export const StripeProvider = ({ children }: { children: React.ReactNode }) => (
  <StripeContext.Provider value={stripePromise}>
    {children}
  </StripeContext.Provider>
);
```

### **3.2 Upgrade Component:**
```typescript
// src/components/billing/UpgradeButton.tsx
import { useState } from 'react';
import { useStripe } from '@/contexts/StripeContext';
import { supabase } from '@/lib/supabase';

interface UpgradeButtonProps {
  tier: 'sapling' | 'woodland';
  currentTier: string;
}

export const UpgradeButton = ({ tier, currentTier }: UpgradeButtonProps) => {
  const [loading, setLoading] = useState(false);
  const stripePromise = useStripe();

  const handleUpgrade = async () => {
    setLoading(true);
    
    try {
      // Call your API to create checkout session
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tier }),
      });
      
      const { sessionId } = await response.json();
      
      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      await stripe?.redirectToCheckout({ sessionId });
      
    } catch (error) {
      console.error('Upgrade failed:', error);
    } finally {
      setLoading(false);
    }
  };

  if (currentTier === tier) {
    return <span className="text-green-600">Current Plan</span>;
  }

  return (
    <button
      onClick={handleUpgrade}
      disabled={loading}
      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
    >
      {loading ? 'Processing...' : `Upgrade to ${tier.charAt(0).toUpperCase() + tier.slice(1)}`}
    </button>
  );
};
```

---

## 🔗 **Step 4: API Routes**

### **4.1 Create Checkout Session:**
```typescript
// pages/api/create-checkout-session.ts
import { NextApiRequest, NextApiResponse } from 'next';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { tier } = req.body;
    
    // Get user from session
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    // Price mapping
    const priceIds = {
      sapling: 'price_sapling_monthly',
      woodland: 'price_woodland_monthly',
    };

    const session = await stripe.checkout.sessions.create({
      customer_email: user.email,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceIds[tier as keyof typeof priceIds],
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${req.headers.origin}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.origin}/billing/cancel`,
      metadata: {
        user_id: user.id,
        tier: tier,
      },
    });

    res.status(200).json({ sessionId: session.id });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
```

### **4.2 Webhook Handler:**
```typescript
// pages/api/stripe-webhook.ts
import { NextApiRequest, NextApiResponse } from 'next';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const sig = req.headers['stripe-signature'] as string;
  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return res.status(400).json({ error: 'Invalid signature' });
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Update user's subscription tier
        await supabase
          .from('profiles')
          .update({
            subscription_tier: session.metadata?.tier,
            subscription_status: 'active',
            subscription_start_date: new Date().toISOString(),
            customer_id: session.customer,
          })
          .eq('id', session.metadata?.user_id);
        
        break;

      case 'customer.subscription.deleted':
        const subscription = event.data.object as Stripe.Subscription;
        
        // Downgrade user to free tier
        await supabase
          .from('profiles')
          .update({
            subscription_tier: 'seed',
            subscription_status: 'cancelled',
            subscription_end_date: new Date().toISOString(),
          })
          .eq('customer_id', subscription.customer);
        
        break;
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};
```

---

## 🧪 **Step 5: Testing**

### **5.1 Test Cards:**
```
Success: 4242 4242 4242 4242
Decline: 4000 0000 0000 0002
```

### **5.2 Test Flow:**
1. User clicks "Upgrade to Sapling"
2. Redirected to Stripe Checkout
3. Enter test card details
4. Webhook updates user tier in database
5. User can now access premium features

---

## 📊 **Step 6: Usage Checking**

### **6.1 Frontend Usage Check:**
```typescript
// Before allowing user to post
const checkUsage = async () => {
  const { data } = await supabase.rpc('can_user_perform_action', {
    action_type: 'post',
    content_type: 'image' // or 'video'
  });
  
  if (!data.allowed) {
    // Show upgrade prompt
    alert(data.reason);
    return false;
  }
  
  return true;
};

// After successful action
const incrementUsage = async () => {
  await supabase.rpc('increment_usage_counter', {
    action_type: 'post'
  });
};
```

---

## 🎯 **Quick Launch Checklist**

1. ✅ **Run the database migration** (`startup_monetization_system.sql`)
2. ✅ **Set up Stripe account** (test mode)
3. ✅ **Create products in Stripe**
4. ✅ **Add Stripe to your app**
5. ✅ **Create API routes**
6. ✅ **Add upgrade buttons**
7. ✅ **Test with test cards**
8. ✅ **Go live when ready!**

**This gives you a simple, working monetization system you can launch with today!** 🚀
