// Social Network V2: Clean, optimized hook for LinkedIn-style connections
// Purpose: Social feed visibility + Activity notifications (no messaging)

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// =====================================================
// TYPES
// =====================================================

export interface SocialConnection {
  connection_id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  avatar_url: string | null;
  title: string | null;
  organization: string | null;
  connected_at: string;
  mutual_connections: number;
}

export interface ConnectionRequest {
  connection_id: string;
  requester_id: string;
  first_name: string;
  last_name: string;
  avatar_url: string | null;
  title: string | null;
  organization: string | null;
  created_at: string;
}

export interface SentRequest {
  connection_id: string;
  recipient_id: string;
  first_name: string;
  last_name: string;
  avatar_url: string | null;
  title: string | null;
  organization: string | null;
  created_at: string;
}

export interface SuggestedConnection {
  user_id: string;
  first_name: string;
  last_name: string;
  avatar_url: string | null;
  title: string | null;
  organization: string | null;
  mutual_connections: number;
}

export interface ConnectionStats {
  total_connections: number;
  pending_requests: number;
  sent_requests: number;
}

// =====================================================
// MAIN HOOK
// =====================================================

export function useSocialNetworkV2() {
  const { toast } = useToast();
  
  // State
  const [connections, setConnections] = useState<SocialConnection[]>([]);
  const [connectionRequests, setConnectionRequests] = useState<ConnectionRequest[]>([]);
  const [sentRequests, setSentRequests] = useState<SentRequest[]>([]);
  const [suggestedConnections, setSuggestedConnections] = useState<SuggestedConnection[]>([]);
  const [stats, setStats] = useState<ConnectionStats>({ total_connections: 0, pending_requests: 0, sent_requests: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // =====================================================
  // QUERY FUNCTIONS
  // =====================================================

  const loadConnections = useCallback(async () => {
    try {
      const { data, error } = await supabase.rpc('get_user_connections');
      if (error) throw error;
      setConnections(data || []);
      return data || [];
    } catch (error) {
      console.error('Error loading connections:', error);
      toast({
        title: "Error",
        description: "Failed to load connections",
        variant: "destructive"
      });
      return [];
    }
  }, [toast]);

  const loadConnectionRequests = useCallback(async () => {
    try {
      const { data, error } = await supabase.rpc('get_connection_requests');
      if (error) throw error;
      setConnectionRequests(data || []);
      return data || [];
    } catch (error) {
      console.error('Error loading connection requests:', error);
      toast({
        title: "Error",
        description: "Failed to load connection requests",
        variant: "destructive"
      });
      return [];
    }
  }, [toast]);

  const loadSentRequests = useCallback(async () => {
    try {
      const { data, error } = await supabase.rpc('get_sent_requests');
      if (error) throw error;
      setSentRequests(data || []);
      return data || [];
    } catch (error) {
      console.error('Error loading sent requests:', error);
      toast({
        title: "Error",
        description: "Failed to load sent requests",
        variant: "destructive"
      });
      return [];
    }
  }, [toast]);

  const loadSuggestedConnections = useCallback(async (limit: number = 10) => {
    try {
      const { data, error } = await supabase.rpc('get_suggested_connections_v2', { suggestion_limit: limit });
      if (error) throw error;
      setSuggestedConnections(data || []);
      return data || [];
    } catch (error) {
      console.error('Error loading suggested connections:', error);
      toast({
        title: "Error",
        description: "Failed to load suggested connections",
        variant: "destructive"
      });
      return [];
    }
  }, [toast]);

  // =====================================================
  // MUTATION FUNCTIONS
  // =====================================================

  const sendConnectionRequest = useCallback(async (recipientId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('send_connection_request', { 
        recipient_user_id: recipientId 
      });
      
      if (error) throw error;
      
      if (!data.success) {
        throw new Error(data.error);
      }

      // Optimistic update: Remove from suggestions
      setSuggestedConnections(prev => prev.filter(s => s.user_id !== recipientId));
      
      // Refresh sent requests
      await loadSentRequests();
      
      toast({
        title: "Success",
        description: "Connection request sent successfully"
      });
      
      return true;
    } catch (error: any) {
      console.error('Error sending connection request:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to send connection request",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast, loadSentRequests]);

  const acceptConnectionRequest = useCallback(async (connectionId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('accept_connection_request', { 
        connection_id: connectionId 
      });
      
      if (error) throw error;
      
      if (!data.success) {
        throw new Error(data.error);
      }

      // Optimistic update: Remove from requests
      setConnectionRequests(prev => prev.filter(r => r.connection_id !== connectionId));
      
      // Refresh connections
      await loadConnections();
      
      toast({
        title: "Success",
        description: "Connection request accepted"
      });
      
      return true;
    } catch (error: any) {
      console.error('Error accepting connection request:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to accept connection request",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast, loadConnections]);

  const declineConnectionRequest = useCallback(async (connectionId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('decline_connection_request', { 
        connection_id: connectionId 
      });
      
      if (error) throw error;
      
      if (!data.success) {
        throw new Error(data.error);
      }

      // Optimistic update: Remove from requests
      setConnectionRequests(prev => prev.filter(r => r.connection_id !== connectionId));
      
      toast({
        title: "Success",
        description: "Connection request declined"
      });
      
      return true;
    } catch (error: any) {
      console.error('Error declining connection request:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to decline connection request",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const cancelConnectionRequest = useCallback(async (connectionId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('cancel_connection_request', { 
        connection_id: connectionId 
      });
      
      if (error) throw error;
      
      if (!data.success) {
        throw new Error(data.error);
      }

      // Optimistic update: Remove from sent requests
      setSentRequests(prev => prev.filter(r => r.connection_id !== connectionId));
      
      toast({
        title: "Success",
        description: "Connection request cancelled"
      });
      
      return true;
    } catch (error: any) {
      console.error('Error cancelling connection request:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to cancel connection request",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const removeConnection = useCallback(async (connectionId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('remove_connection', { 
        connection_id: connectionId 
      });
      
      if (error) throw error;
      
      if (!data.success) {
        throw new Error(data.error);
      }

      // Optimistic update: Remove from connections
      setConnections(prev => prev.filter(c => c.connection_id !== connectionId));
      
      toast({
        title: "Success",
        description: "Connection removed"
      });
      
      return true;
    } catch (error: any) {
      console.error('Error removing connection:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to remove connection",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // =====================================================
  // REFRESH FUNCTION
  // =====================================================

  const refreshAll = useCallback(async () => {
    setIsRefreshing(true);
    try {
      const [connectionsData, requestsData, sentData] = await Promise.all([
        loadConnections(),
        loadConnectionRequests(),
        loadSentRequests()
      ]);

      // Update stats
      setStats({
        total_connections: connectionsData.length,
        pending_requests: requestsData.length,
        sent_requests: sentData.length
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [loadConnections, loadConnectionRequests, loadSentRequests]);

  // Helper function to get display name
  const getDisplayName = useCallback((firstName: string, lastName: string) => {
    return [firstName, lastName].filter(Boolean).join(' ') || 'Professional';
  }, []);

  // Helper function to get role display
  const getRoleDisplay = useCallback((title: string | null, organization: string | null) => {
    return [title, organization].filter(Boolean).join(' at ') || 'Professional';
  }, []);

  // =====================================================
  // EFFECTS
  // =====================================================

  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  // Update stats when data changes
  useEffect(() => {
    setStats({
      total_connections: connections.length,
      pending_requests: connectionRequests.length,
      sent_requests: sentRequests.length
    });
  }, [connections.length, connectionRequests.length, sentRequests.length]);

  // =====================================================
  // RETURN
  // =====================================================

  return {
    // Data
    connections,
    connectionRequests,
    sentRequests,
    suggestedConnections,
    stats,

    // State
    isLoading,
    isRefreshing,

    // Actions
    sendConnectionRequest,
    acceptConnectionRequest,
    declineConnectionRequest,
    cancelConnectionRequest,
    removeConnection,

    // Loaders
    loadSuggestedConnections,
    refreshAll,

    // Helpers
    getDisplayName,
    getRoleDisplay
  };
}
