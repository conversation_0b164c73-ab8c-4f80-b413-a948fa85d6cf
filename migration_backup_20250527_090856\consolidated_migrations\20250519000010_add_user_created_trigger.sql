CREATE OR REPLACE FUNCTION public.handle_new_user()
RET<PERSON>NS TRIGGER AS $$
BEGIN
  -- Insert a new profile record for the new user with default values
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    avatar_url, 
    title, 
    organization, 
    bio, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NULL, -- avatar_url
    NULL, -- title
    NULL, -- organization
    NULL, -- bio    'private', -- social_visibility default
    'none', -- subscription_tier default
    'trial', -- subscription_status default
    NEW.created_at,
    NEW.created_at
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to call this function whenever a new user is created
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
