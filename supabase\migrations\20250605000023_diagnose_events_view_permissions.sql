-- Diagnose events_with_creator view permission issues
-- This migration will help us understand what's blocking access
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- PART 1: CHECK CURRENT STATE OF THE VIEW
-- ================================================================

DO $$
DECLARE
    view_exists BOOLEAN;
    view_def TEXT;
    permission_record RECORD;
    table_permission_record RECORD;
    rls_status TEXT;
BEGIN
    RAISE NOTICE '=== EVENTS_WITH_CREATOR VIEW DIAGNOSTIC ===';

    -- Check if view exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.views
        WHERE table_schema = 'public'
        AND table_name = 'events_with_creator'
    ) INTO view_exists;

    RAISE NOTICE 'View exists: %', view_exists;

    IF view_exists THEN
        -- Get view definition
        SELECT v.view_definition INTO view_def
        FROM information_schema.views v
        WHERE v.table_schema = 'public'
        AND v.table_name = 'events_with_creator';

        RAISE NOTICE 'View definition: %', view_def;
        
        -- Check view permissions
        RAISE NOTICE '--- VIEW PERMISSIONS ---';
        FOR permission_record IN 
            SELECT grantee, privilege_type, is_grantable
            FROM information_schema.table_privileges 
            WHERE table_schema = 'public' 
            AND table_name = 'events_with_creator'
        LOOP
            RAISE NOTICE 'Grantee: %, Privilege: %, Grantable: %', 
                permission_record.grantee, 
                permission_record.privilege_type, 
                permission_record.is_grantable;
        END LOOP;
        
        -- Check underlying table permissions
        RAISE NOTICE '--- UNDERLYING TABLE PERMISSIONS ---';
        
        -- Events table permissions
        RAISE NOTICE 'EVENTS table permissions:';
        FOR table_permission_record IN 
            SELECT grantee, privilege_type, is_grantable
            FROM information_schema.table_privileges 
            WHERE table_schema = 'public' 
            AND table_name = 'events'
            AND privilege_type = 'SELECT'
        LOOP
            RAISE NOTICE 'Events - Grantee: %, Privilege: %, Grantable: %', 
                table_permission_record.grantee, 
                table_permission_record.privilege_type, 
                table_permission_record.is_grantable;
        END LOOP;
        
        -- Profiles table permissions
        RAISE NOTICE 'PROFILES table permissions:';
        FOR table_permission_record IN 
            SELECT grantee, privilege_type, is_grantable
            FROM information_schema.table_privileges 
            WHERE table_schema = 'public' 
            AND table_name = 'profiles'
            AND privilege_type = 'SELECT'
        LOOP
            RAISE NOTICE 'Profiles - Grantee: %, Privilege: %, Grantable: %', 
                table_permission_record.grantee, 
                table_permission_record.privilege_type, 
                table_permission_record.is_grantable;
        END LOOP;
        
        -- Check RLS status
        RAISE NOTICE '--- RLS STATUS ---';
        SELECT
            CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END
        INTO rls_status
        FROM pg_tables
        WHERE schemaname = 'public' AND tablename = 'events';
        RAISE NOTICE 'Events RLS: %', rls_status;

        SELECT
            CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END
        INTO rls_status
        FROM pg_tables
        WHERE schemaname = 'public' AND tablename = 'profiles';
        RAISE NOTICE 'Profiles RLS: %', rls_status;
        
    END IF;
END $$;

-- ================================================================
-- PART 2: TRY TO CREATE A MINIMAL TEST VIEW
-- ================================================================

-- Drop test view if it exists
DROP VIEW IF EXISTS public.test_events_simple CASCADE;

-- Create a very simple test view
CREATE OR REPLACE VIEW public.test_events_simple AS
SELECT 
    e.id,
    e.title,
    e.creator_user_id
FROM events e
LIMIT 5;

-- Grant permissions to test view
GRANT SELECT ON public.test_events_simple TO authenticated;
GRANT SELECT ON public.test_events_simple TO anon;

-- Test the simple view
DO $$
DECLARE
    test_record RECORD;
    test_count INTEGER;
BEGIN
    RAISE NOTICE '=== TESTING SIMPLE VIEW ===';
    
    BEGIN
        SELECT COUNT(*) INTO test_count FROM public.test_events_simple;
        RAISE NOTICE 'Simple view test - Count: %', test_count;
        
        -- Try to get one record
        SELECT id, title INTO test_record FROM public.test_events_simple LIMIT 1;
        IF test_record.id IS NOT NULL THEN
            RAISE NOTICE 'Simple view test - Sample: % - %', test_record.id, test_record.title;
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Simple view test FAILED: %', SQLERRM;
    END;
END $$;

-- ================================================================
-- PART 3: TRY TO CREATE VIEW WITH PROFILES JOIN
-- ================================================================

-- Drop test view if it exists
DROP VIEW IF EXISTS public.test_events_with_profiles CASCADE;

-- Create test view with profiles join
CREATE OR REPLACE VIEW public.test_events_with_profiles AS
SELECT 
    e.id,
    e.title,
    e.creator_user_id,
    p.first_name,
    p.last_name
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id
LIMIT 5;

-- Grant permissions to test view
GRANT SELECT ON public.test_events_with_profiles TO authenticated;
GRANT SELECT ON public.test_events_with_profiles TO anon;

-- Test the profiles join view
DO $$
DECLARE
    test_record RECORD;
    test_count INTEGER;
BEGIN
    RAISE NOTICE '=== TESTING PROFILES JOIN VIEW ===';
    
    BEGIN
        SELECT COUNT(*) INTO test_count FROM public.test_events_with_profiles;
        RAISE NOTICE 'Profiles join view test - Count: %', test_count;
        
        -- Try to get one record
        SELECT id, title, first_name, last_name INTO test_record 
        FROM public.test_events_with_profiles LIMIT 1;
        
        IF test_record.id IS NOT NULL THEN
            RAISE NOTICE 'Profiles join view test - Sample: % - % by % %', 
                test_record.id, test_record.title, test_record.first_name, test_record.last_name;
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Profiles join view test FAILED: %', SQLERRM;
    END;
END $$;

-- ================================================================
-- PART 4: CHECK CURRENT USER CONTEXT
-- ================================================================

DO $$
DECLARE
    current_user_info TEXT;
    current_role TEXT;
BEGIN
    RAISE NOTICE '=== CURRENT USER CONTEXT ===';
    
    SELECT current_user INTO current_user_info;
    RAISE NOTICE 'Current user: %', current_user_info;
    
    SELECT current_role INTO current_role;
    RAISE NOTICE 'Current role: %', current_role;
    
    -- Check if we can access auth.uid()
    BEGIN
        IF auth.uid() IS NOT NULL THEN
            RAISE NOTICE 'Auth UID: %', auth.uid();
        ELSE
            RAISE NOTICE 'Auth UID: NULL (not authenticated)';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Cannot access auth.uid(): %', SQLERRM;
    END;
END $$;

COMMIT;
