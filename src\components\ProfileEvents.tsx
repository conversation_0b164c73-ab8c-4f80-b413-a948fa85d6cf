import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { Calendar, MapPin, ArrowRight, Users, ExternalLink, Star, UserCheck } from "lucide-react";
import { format, parseISO } from "date-fns";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";

// Simple event interface for this component
interface SimpleEvent {
  id: string;
  title: string;
  description?: string;
  start_date: string;
  end_date?: string;
  location?: string;
  event_type?: string;
  creator_user_id: string;
  created_at: string;
  updated_at: string;
}

interface ProfileEventsProps {
  userId: string;
}

const ProfileEvents = ({ userId }: ProfileEventsProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [createdEvents, setCreatedEvents] = useState<SimpleEvent[]>([]);
  const [attendingEvents, setAttendingEvents] = useState<SimpleEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [attendeeCounts, setAttendeeCounts] = useState<{[eventId: string]: number}>({});
  const [activeTab, setActiveTab] = useState("created");

  // Load events created by the specific user
  const loadCreatedEvents = async () => {
    try {
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .eq('creator_user_id', userId)
        .order('start_date', { ascending: false });

      if (error) throw error;
      setCreatedEvents(data || []);
    } catch (error) {
      console.error("Error loading created events:", error);
      toast({
        title: "Error",
        description: "Failed to load created events",
        variant: "destructive"
      });
    }
  };

  // Load events the user is attending (where they haven't hidden attendance)
  const loadAttendingEvents = async () => {
    try {
      // First get the event signups where user hasn't hidden attendance
      const { data: signups, error: signupsError } = await supabase
        .from('event_signups')
        .select('event_id')
        .eq('user_id', userId)
        .eq('hide_attendance', false); // Only show events where attendance is not hidden

      if (signupsError) throw signupsError;

      if (!signups || signups.length === 0) {
        setAttendingEvents([]);
        return;
      }

      // Then get the actual events
      const eventIds = signups.map(s => s.event_id);
      const { data: events, error: eventsError } = await supabase
        .from('events')
        .select('*')
        .in('id', eventIds)
        .neq('creator_user_id', userId) // Exclude events they created (those are in the "created" tab)
        .order('start_date', { ascending: false });

      if (eventsError) throw eventsError;
      
      setAttendingEvents(events || []);
    } catch (error) {
      console.error("Error loading attending events:", error);
      toast({
        title: "Error",
        description: "Failed to load attending events",
        variant: "destructive"
      });
    }
  };

  // Load attendee counts for events
  const loadAttendeeCounts = async (events: SimpleEvent[]) => {
    if (events.length === 0) return;
    
    try {
      for (const event of events) {
        const { count, error } = await supabase
          .from('event_signups')
          .select('*', { count: 'exact', head: true })
          .eq('event_id', event.id);

        if (!error) {
          setAttendeeCounts(prev => ({
            ...prev,
            [event.id]: count || 0
          }));
        }
      }
    } catch (error) {
      console.error("Error loading attendee counts:", error);
    }
  };

  useEffect(() => {
    const loadAllEvents = async () => {
      setLoading(true);
      if (userId) {
        await Promise.all([loadCreatedEvents(), loadAttendingEvents()]);
      }
      setLoading(false);
    };

    loadAllEvents();
  }, [userId]);

  // Load attendee counts when events change
  useEffect(() => {
    const allEvents = [...createdEvents, ...attendingEvents];
    if (allEvents.length > 0) {
      loadAttendeeCounts(allEvents);
    }
  }, [createdEvents, attendingEvents]);

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Handle redirects
  const handleViewEvent = (id: string) => {
    navigate(`/events/${id}`);
  };

  const handleViewAllEvents = () => {
    navigate('/events');
  };

  // Render event cards
  const renderEventCards = (events: SimpleEvent[], emptyMessage: string, emptyDescription: string) => {
    if (events.length === 0) {
      return (
        <Card>
          <CardContent className="text-center py-8">
            <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-lg font-medium mb-2">{emptyMessage}</p>
            <p className="text-gray-600 mb-4">{emptyDescription}</p>
            <Button variant="outline" onClick={handleViewAllEvents}>
              <ExternalLink className="mr-2 h-4 w-4" />
              View All Events
            </Button>
          </CardContent>
        </Card>
      );
    }

    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {events.map((event) => (
          <Card key={event.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-3">
                <h3 className="font-semibold text-lg line-clamp-1">{event.title}</h3>
              </div>
              
              {event.description && (
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {event.description}
                </p>
              )}
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-2" />
                  {formatDate(event.start_date)}
                </div>
                
                {event.location && (
                  <div className="flex items-center text-sm text-gray-500">
                    <MapPin className="h-4 w-4 mr-2" />
                    {event.location}
                  </div>
                )}
                
                <div className="flex items-center text-sm text-gray-500">
                  <Users className="h-4 w-4 mr-2" />
                  {attendeeCounts[event.id] || 0} attendees
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                {event.event_type && (
                  <Badge variant="secondary" className="text-xs">
                    {event.event_type.charAt(0).toUpperCase() + event.event_type.slice(1)}
                  </Badge>
                )}
                
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleViewEvent(event.id)}
                  className="ml-auto"
                >
                  View Details
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="text-center py-8">Loading events...</div>
    );
  }

  // If no events at all, show simple message
  if (createdEvents.length === 0 && attendingEvents.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <p className="text-lg font-medium mb-2">No events to display</p>
          <p className="text-gray-600 mb-4">This user hasn't created or attended any public events yet</p>
          <Button variant="outline" onClick={handleViewAllEvents}>
            <ExternalLink className="mr-2 h-4 w-4" />
            View All Events
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="created">
            <Star className="w-4 h-4 mr-2" />
            Events Created ({createdEvents.length})
          </TabsTrigger>
          <TabsTrigger value="attending">
            <UserCheck className="w-4 h-4 mr-2" />
            Events Attending ({attendingEvents.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="created" className="space-y-4">
          {renderEventCards(
            createdEvents,
            "No events created yet",
            "This user hasn't created any events yet"
          )}
        </TabsContent>

        <TabsContent value="attending" className="space-y-4">
          {renderEventCards(
            attendingEvents,
            "No events attending",
            "This user isn't publicly attending any events yet"
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProfileEvents;
