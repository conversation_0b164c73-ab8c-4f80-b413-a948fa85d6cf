-- Remove problematic connection triggers that reference old materialized views
-- Date: 2025-01-03

B<PERSON><PERSON>;

-- Drop the problematic triggers that are causing mv_active_connections errors
DROP TRIGGER IF EXISTS refresh_connection_views_trigger ON public.user_connections;
DROP TRIGGER IF EXISTS notify_connection_request_trigger ON public.user_connections;
DROP TRIGGER IF EXISTS notify_connection_accepted_trigger ON public.user_connections;

-- Drop the functions that these triggers were calling
DROP FUNCTION IF EXISTS public.refresh_connection_views_trigger() CASCADE;
DROP FUNCTION IF EXISTS public.notify_connection_request() CASCADE;
DROP FUNCTION IF EXISTS public.notify_connection_accepted() CASCADE;
DROP FUNCTION IF EXISTS public.refresh_connection_views() CASCADE;

-- Also drop any other functions that might reference the old materialized views
DROP FUNCTION IF EXISTS public.get_user_connections(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_pending_requests(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_sent_requests(UUID) CASCADE;

-- Make sure all old materialized views are completely gone
DROP MATERIALIZED VIEW IF EXISTS public.mv_active_connections CASCADE;
DROP MATERIALIZED VIEW IF EXISTS public.mv_pending_connection_requests CASCADE;
DROP MATERIALIZED VIEW IF EXISTS public.mv_sent_connection_requests CASCADE;

-- Verify our main RPC functions still exist and work
SELECT 
    'Verifying RPC functions' as check_name,
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_name IN (
    'request_connection',
    'respond_to_connection_request',
    'cancel_connection_request',
    'remove_connection'
)
AND routine_schema = 'public'
ORDER BY routine_name;

-- Test that remove_connection works now (should return auth error, which is expected)
SELECT 
    'Testing remove_connection' as check_name,
    public.remove_connection('00000000-0000-0000-0000-000000000000'::uuid) as result;

COMMIT;

-- Final verification - check that no triggers reference mv_active_connections
SELECT 
    'Final trigger check' as check_name,
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers
WHERE action_statement LIKE '%mv_active_connections%'
OR action_statement LIKE '%refresh_connection_views%';
