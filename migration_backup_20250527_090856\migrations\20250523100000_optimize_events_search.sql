-- Update the events table to add a tsvector column for efficient text search
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS text_search tsvector
  GENERATED ALWAYS AS (
    setweight(to_tsvector('english', coalesce(title, '')), 'A') ||
    setweight(to_tsvector('english', coalesce(description, '')), 'B') ||
    setweight(to_tsvector('english', coalesce(event_category, '')), 'C') ||
    setweight(to_tsvector('english', array_to_string(tags, ' ')), 'C')
  ) STORED;

-- Create a GIN index on the text_search column
CREATE INDEX IF NOT EXISTS events_text_search_idx ON public.events USING gin(text_search);

-- Create a view that includes search ranking
DROP VIEW IF EXISTS events_with_search;
CREATE VIEW events_with_search AS
SELECT
  e.*,
  coalesce(array_length(es.user_id, 1), 0) as signups_count,
  au.id IS NOT NULL as user_has_signed_up
FROM
  public.events e
LEFT JOIN LATERAL (
  SELECT array_agg(user_id) as user_id
  FROM event_signups
  WHERE event_id = e.id
  GROUP BY event_id
) es ON true
LEFT JOIN
  auth.users au ON au.id = auth.uid();
