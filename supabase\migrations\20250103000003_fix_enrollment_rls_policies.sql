-- Fix RLS policies for training_course_enrollments to work with profile_id
-- This ensures that users can access their enrollments using profile_id

BEGIN;

-- Drop existing policies that might reference user_id
DROP POLICY IF EXISTS "Users can create enrollment requests" ON public.training_course_enrollments;
DROP POLICY IF EXISTS "Users can enroll in courses" ON public.training_course_enrollments;
DROP POLICY IF EXISTS "Users can update their own enrollments" ON public.training_course_enrollments;
DROP POLICY IF EXISTS "Users can view their own enrollments" ON public.training_course_enrollments;

-- Create updated policies that use profile_id (since user_id column may not exist)
CREATE POLICY "Users can create enrollment requests" ON public.training_course_enrollments
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Users can enroll in courses" ON public.training_course_enrollments
    FOR INSERT TO PUBLIC
    WITH CHECK (profile_id = auth.uid());

CREATE POLICY "Users can update their own enrollments" ON public.training_course_enrollments
    FOR UPDATE TO PUBLIC
    USING (profile_id = auth.uid())
    WITH CHECK (
        (profile_id = auth.uid()) AND
        (status = ANY (ARRAY['pending'::text, 'cancelled'::text]))
    );

CREATE POLICY "Users can view their own enrollments" ON public.training_course_enrollments
    FOR SELECT TO PUBLIC
    USING (profile_id = auth.uid());

COMMIT;

-- Verify the fix
SELECT 'Enrollment RLS policies updated to support profile_id' as status;
