-- Simple diagnostic that returns data instead of NOTICES
-- This will help us understand the events_with_creator view issue
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- PART 1: CHECK IF VIEW EXISTS AND GET ITS DEFINITION
-- ================================================================

-- Check if events_with_creator view exists
SELECT 
    'VIEW_EXISTS' as check_type,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.views 
        WHERE table_schema = 'public' 
        AND table_name = 'events_with_creator'
    ) THEN 'TRUE' ELSE 'FALSE' END as result;

-- Get view definition if it exists
SELECT 
    'VIEW_DEFINITION' as check_type,
    COALESCE(view_definition, 'VIEW_NOT_FOUND') as result
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name = 'events_with_creator';

-- ================================================================
-- PART 2: CHECK PERMISSIONS ON VIEW AND UNDERLYING TABLES
-- ================================================================

-- Check view permissions
SELECT 
    'VIEW_PERMISSIONS' as check_type,
    grantee,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_schema = 'public' 
AND table_name = 'events_with_creator'
ORDER BY grantee, privilege_type;

-- Check events table permissions
SELECT 
    'EVENTS_TABLE_PERMISSIONS' as check_type,
    grantee,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_schema = 'public' 
AND table_name = 'events'
AND privilege_type = 'SELECT'
ORDER BY grantee;

-- Check profiles table permissions
SELECT 
    'PROFILES_TABLE_PERMISSIONS' as check_type,
    grantee,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
AND privilege_type = 'SELECT'
ORDER BY grantee;

-- ================================================================
-- PART 3: CHECK RLS STATUS
-- ================================================================

-- Check RLS status for events and profiles tables
SELECT 
    'RLS_STATUS' as check_type,
    tablename,
    CASE WHEN rowsecurity THEN 'ENABLED' ELSE 'DISABLED' END as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('events', 'profiles')
ORDER BY tablename;

-- ================================================================
-- PART 4: TEST SIMPLE QUERIES
-- ================================================================

-- Test if we can query events table directly
SELECT 
    'EVENTS_TABLE_TEST' as check_type,
    'SUCCESS' as result,
    COUNT(*) as event_count
FROM events;

-- Test if we can query profiles table directly
SELECT 
    'PROFILES_TABLE_TEST' as check_type,
    'SUCCESS' as result,
    COUNT(*) as profile_count
FROM profiles;

-- ================================================================
-- PART 5: TEST THE PROBLEMATIC VIEW
-- ================================================================

-- Try to query the events_with_creator view
-- This will either work or show us the exact error
SELECT 
    'VIEW_QUERY_TEST' as check_type,
    'SUCCESS' as result,
    COUNT(*) as view_count
FROM events_with_creator;

COMMIT;
