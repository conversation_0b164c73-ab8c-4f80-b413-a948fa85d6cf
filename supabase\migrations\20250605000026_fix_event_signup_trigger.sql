-- Fix event signup trigger that's causing "profile_id" field error
-- The trigger is trying to access NEW.profile_id but event_signups table has user_id
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- FIX THE PROBLEMATIC TRIGGER FUNCTION
-- ================================================================

-- Drop the problematic trigger function and recreate it correctly
DROP FUNCTION IF EXISTS public.notify_event_signup() CASCADE;

-- Recreate the function with correct column references
CREATE OR REPLACE FUNCTION public.notify_event_signup()
RETURNS TRIGGER AS $$
DECLARE
    should_notify BOOLEAN;
    event_creator_id UUID;
BEGIN
    -- Get the creator_user_id of the event (not creator_profile_id)
    SELECT creator_user_id INTO event_creator_id
    FROM public.events
    WHERE id = NEW.event_id;

    -- Don't notify if user signs up for their own event
    IF event_creator_id = NEW.user_id THEN
        RETURN NEW;
    END IF;

    -- Check if user has enabled notifications for event signups
    -- Use the event creator's user_id to find their profile_id for notification preferences
    SELECT COALESCE(np.event_signups, true) INTO should_notify
    FROM public.notification_preferences np
    INNER JOIN public.profiles p ON np.profile_id = p.id
    WHERE p.id = event_creator_id;

    -- If no notification preference found, default to true
    IF should_notify IS NULL THEN
        should_notify := true;
    END IF;

    IF should_notify THEN
        -- Create notification using the correct column names
        INSERT INTO public.notifications (
            profile_id,
            type,
            content,
            related_id,
            related_type,
            actor_id
        ) VALUES (
            event_creator_id,
            'event_signup',
            'signed up for your event',
            NEW.event_id,
            'event',
            NEW.user_id  -- Use NEW.user_id, not NEW.profile_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================================================
-- RECREATE THE TRIGGER
-- ================================================================

-- Drop existing triggers that might conflict
DROP TRIGGER IF EXISTS event_signup_notification_trigger ON public.event_signups;
DROP TRIGGER IF EXISTS notify_event_signup_trigger ON public.event_signups;

-- Create the trigger with the corrected function
CREATE TRIGGER event_signup_notification_trigger
    AFTER INSERT ON public.event_signups
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_event_signup();

-- ================================================================
-- ALSO FIX ANY OTHER PROBLEMATIC TRIGGER FUNCTIONS
-- ================================================================

-- Check if handle_new_event_signup exists and fix it too
CREATE OR REPLACE FUNCTION public.handle_new_event_signup()
RETURNS TRIGGER AS $$
BEGIN
    -- Simple function that just completes the insert
    -- Any complex logic should be in separate triggers
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure the trigger exists for handle_new_event_signup
DROP TRIGGER IF EXISTS on_event_signup_insert ON public.event_signups;
CREATE TRIGGER on_event_signup_insert
    AFTER INSERT ON public.event_signups
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_event_signup();

-- ================================================================
-- TEST THE FIX
-- ================================================================

-- Test that we can insert into event_signups without errors
-- This will help verify the trigger functions work correctly
DO $$
DECLARE
    test_event_id UUID;
    test_user_id UUID;
    test_signup_id UUID;
BEGIN
    -- Get a test event and user (if they exist)
    SELECT id INTO test_event_id FROM public.events LIMIT 1;
    SELECT id INTO test_user_id FROM public.profiles LIMIT 1;
    
    IF test_event_id IS NOT NULL AND test_user_id IS NOT NULL THEN
        -- Try a test insert (will be rolled back)
        INSERT INTO public.event_signups (event_id, user_id, gdpr_consent)
        VALUES (test_event_id, test_user_id, true)
        RETURNING id INTO test_signup_id;
        
        -- Clean up the test record
        DELETE FROM public.event_signups WHERE id = test_signup_id;
        
        RAISE NOTICE 'Event signup trigger test: SUCCESS';
    ELSE
        RAISE NOTICE 'Event signup trigger test: SKIPPED (no test data available)';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Event signup trigger test: FAILED - %', SQLERRM;
END $$;

COMMIT;
