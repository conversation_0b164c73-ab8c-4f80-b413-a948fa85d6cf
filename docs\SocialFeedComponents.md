# Social Feed Components and Hooks

This document provides a clean, organized overview of the components and hooks used in our social feed implementation, along with a brief explanation of their purpose.

## Components

### Core Components

| Component | Path | Purpose |
|-----------|------|---------|
| `SocialFeed` | `src/pages/SocialFeed.tsx` | Main page component that displays the social feed and orchestrates all other components |
| `SocialFeedPost` | `src/components/social/SocialFeedPost.tsx` | Displays an individual post including content, author info, likes, and comments |
| `PostCreator` | `src/components/social/PostCreator.tsx` | Form for creating new posts with text, media, and category selection |
| `PostComment` | `src/components/social/PostComment.tsx` | Displays individual comments with author info and like functionality |
| `CategoryFilter` | `src/components/social/CategoryFilter.tsx` | Allows filtering posts by category |

### Supporting Components

| Component | Path | Purpose |
|-----------|------|---------|
| `PostCategoriesDisplay` | `src/components/social/PostCategoriesDisplay.tsx` | Displays categories associated with a post as badges |
| `TrendingSidebar` | `src/components/social/TrendingSidebar.tsx` | Shows trending topics in a sidebar |
| `ConnectionsDrawer` | `src/components/social/ConnectionsDrawer.tsx` | Displays user connections in a sliding drawer |
| `ConnectionButton` | `src/components/social/ConnectionButton.tsx` | Button for managing user connections |

## Hooks

| Hook | Path | Purpose |
|------|------|---------|
| `useSocialFeed` | `src/hooks/useSocialFeed.tsx` | Core hook for managing social feed data, posts, comments, and likes |
| `usePostComments` | `src/hooks/social/usePostComments.tsx` | Manages comment functionality including adding and liking comments |
| `usePostLikes` | `src/hooks/social/usePostLikes.tsx` | Handles post like functionality |
| `useUserConnections` | `src/hooks/social/useUserConnections.tsx` | Manages user connection functionality |

## Data Flow

1. `SocialFeed` page uses `useSocialFeed` to fetch and manage posts
2. Posts are displayed using `SocialFeedPost` components
3. Users can interact with posts through:
   - Like buttons that use `usePostLikes`
   - Comment sections that use `usePostComments`
   - Category filters using `CategoryFilter`
   - Creating new posts via `PostCreator`

## Database Tables

The implementation uses these Supabase tables:

- `social_posts` - Stores post data
- `post_likes` - Tracks post likes
- `post_comments` - Stores post comments
- `comment_likes` - Tracks comment likes
- `post_categories` - Associates categories with posts
- `user_connections` - Manages user connections

## Implementation Notes

- Each component has a single responsibility
- Hooks separate data management from UI components
- Real-time updates are used for comments and likes where appropriate
- The system supports standard social media functionality like creating posts, commenting, liking, and user connections
