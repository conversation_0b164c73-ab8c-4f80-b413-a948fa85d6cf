# Comment Like Functionality Documentation

## Overview
This document explains the implementation of the comment like functionality in the NetZero Platform social feed.

## Features
- Users can like and unlike comments on posts
- Like counts are displayed for each comment
- Comment likes are persisted to the database
- Likes are visually indicated with a heart icon

## Technical Implementation

### Database Tables
- `post_comments`: Stores comment data with a `likes_count` column
- `comment_likes`: Junction table storing the relationship between users and the comments they've liked

### Database Functions
- `increment_comment_like_count`: Increments the like count for a specific comment
- `decrement_comment_like_count`: Decrements the like count for a specific comment
- `sync_comment_like_count`: Synchronizes the like count for a specific comment
- `sync_all_comment_like_counts`: Synchronizes like counts for all comments

### Components & Hooks
- `PostComment.tsx`: Renders a comment with like functionality
- `SocialFeedPost.tsx`: Passes the like handler to each comment
- `useSocialFeed.tsx`: Contains the `handleLikeComment` method to handle like/unlike operations

### User Flow
1. User clicks the heart icon on a comment
2. The `handleLikeComment` function in `useSocialFeed.tsx` is triggered
3. An optimistic UI update is performed
4. The like is stored in the database via Supabase
5. The comment's like count is updated using a database function

## Usage
To like a comment, users simply click the heart icon below any comment. The color changes to indicate the liked state.

## Performance Considerations
- Like counts are maintained in a separate column for efficient querying
- Batch operations are used to fetch like status for multiple comments at once
- Database functions efficiently update like counts without needing to read and write

## Future Enhancements
- Add notifications when a user's comment is liked
- Implement comment like analytics
- Add ability to see who liked a specific comment
