# Database Migrations

This folder contains all database migration files for the NetZero Platform. These migration files represent the full history of how the database schema has evolved over time, and they can be used to recreate the database from scratch if needed.

## Migration File Naming Convention

Migration files follow this naming convention:
```
YYYYMMDDHHMMSS_descriptive_name.sql
```

For example:
```
20250513090000_create_business_standards_table.sql
```

The timestamp prefix ensures migrations are applied in the correct order.

## Migration File Structure

Each migration file should:

1. Include a comment header that describes the purpose of the migration
2. Contain idempotent SQL when possible (using IF NOT EXISTS, etc.)
3. Include both "up" (apply) and "down" (rollback) logic when appropriate
4. Group related changes in a single migration

Example:
```sql
-- Migration: Create business_standards junction table
-- Description: Creates a junction table to store the NetZeroStandards a business selects

-- UP
CREATE TABLE IF NOT EXISTS public.business_standards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  -- columns...
);

-- Indexes
CREATE INDEX idx_business_standards_business_id ON public.business_standards(business_id);

-- <PERSON><PERSON> Policies
ALTER TABLE public.business_standards ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Policy name" ON public.business_standards FOR SELECT USING (true);

-- DOWN (if needed)
-- DROP TABLE IF EXISTS public.business_standards;
```

## Migration Best Practices

1. **One Change Per Migration**: Each migration should do one logical change to the schema
2. **Test Before Committing**: Test migrations on a development database first
3. **Never Edit Applied Migrations**: Once a migration has been applied, never edit it. Create a new migration instead
4. **Avoid Data Loss**: Write migrations that preserve existing data
5. **Document Complex Changes**: Add comments for complex logic

## Related Documentation

For each table created or modified in a migration, there should be corresponding documentation in:
```
c:\Users\<USER>\Projects\netzeroplatformv1\supabase\docs\tables\
```

For example, the migration `20250513090000_create_business_standards_table.sql` creates the `business_standards` table, which is documented in `business_standards.md`.