-- Add missing foreign key constraint between businesses.owner_id and profiles.id
BEGIN;
  -- First, let's confirm the structure of both tables
  SELECT column_name, data_type FROM information_schema.columns 
  WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'id';
  
  SELECT column_name, data_type FROM information_schema.columns 
  WHERE table_schema = 'public' AND table_name = 'businesses' AND column_name = 'owner_id';
  
  -- Add the missing foreign key constraint
  ALTER TABLE public.businesses
  ADD CONSTRAINT businesses_owner_id_fkey
  FOREIGN KEY (owner_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
  
  -- Verify the constraint was added
  SELECT
      tc.constraint_name,
      tc.table_name,
      kcu.column_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name
  FROM
      information_schema.table_constraints tc
  JOIN
      information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
  JOIN
      information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
  WHERE
      tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_name = 'businesses'
      AND kcu.column_name = 'owner_id';
COMMIT;
