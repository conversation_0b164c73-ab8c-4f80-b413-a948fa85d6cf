# Fixing User Categories Table in NetZeroHub Platform

## Problem
We need to create a proper junction table between users and netzero_categories to allow users to select their interests.

The application is trying to access a table called `user_categories` that doesn't exist in the database, resulting in:
```
relation "public.user_categories" does not exist
```

## Solution
I've created a new script (`FixUserCategoriesTable.sql`) that:

1. Creates the missing `user_categories` junction table with the correct structure:
   - References to both `auth.users` and `netzero_categories` tables
   - Proper foreign key constraints with cascading deletes
   - Unique constraint to prevent duplicate selections

2. Sets up appropriate RLS policies to secure the data:
   - Service role has full access
   - Public can view categories
   - Users can only modify their own selections

3. Grants necessary permissions:
   - SELECT for anonymous users
   - Full CRUD for authenticated users

## Table Structure

The `user_categories` table functions as a junction table with this structure:
- `id`: UUID primary key
- `user_id`: Reference to auth.users with ON DELETE CASCADE
- `category_id`: Reference to netzero_categories with ON DELETE CASCADE  
- `created_at`: Timestamp

This properly implements the many-to-many relationship where:
- A user can select multiple interest categories
- Each category can be selected by multiple users

## How to Apply the Fix

Run the `FixUserCategoriesTable.sql` script against your Supabase database:

1. Open the Supabase dashboard (https://supabase.com/dashboard/project/psowjyllxqzllhbiyjtn)
2. Go to the SQL Editor
3. Copy and paste the contents of `FixUserCategoriesTable.sql`
4. Run the script

## Verification

After applying the fix, the UserProfileForm should be able to:
1. Load and display selectable netzero categories
2. Allow users to select their interest categories
3. Save these selections to the database
4. Retrieve a user's selected categories when viewing their profile

This implementation uses the existing `netzero_categories` table without modifying its structure, properly addressing the original error.
