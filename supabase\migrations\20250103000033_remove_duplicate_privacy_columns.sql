-- Remove duplicate privacy/consent columns from profiles table
-- All privacy settings should be in user_consent_settings table only
-- Date: 2025-01-03

BEGIN;

-- First, ensure all profiles have corresponding user_consent_settings
-- This migrates any remaining data from profiles to user_consent_settings
INSERT INTO public.user_consent_settings (
    user_id,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    profile_visibility,
    email_notifications,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    created_at,
    updated_at
)
SELECT 
    p.id,
    false, -- Default to false for new email sharing fields
    false, -- Default to false for new email sharing fields
    false, -- Default to false for new contact sharing
    COALESCE(p.profile_visibility, true),
    COALESCE(p.email_notifications, true),
    COALESCE(p.newsletter_subscription, false),
    COALESCE(p.show_businesses, true),
    COALESCE(p.show_events, true),
    COALESCE(p.show_connections, true),
    now(),
    now()
FROM public.profiles p
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_consent_settings ucs 
    WHERE ucs.user_id = p.id
)
ON CONFLICT (user_id) DO UPDATE SET
    profile_visibility = COALESCE(EXCLUDED.profile_visibility, user_consent_settings.profile_visibility),
    email_notifications = COALESCE(EXCLUDED.email_notifications, user_consent_settings.email_notifications),
    newsletter_subscription = COALESCE(EXCLUDED.newsletter_subscription, user_consent_settings.newsletter_subscription),
    show_businesses = COALESCE(EXCLUDED.show_businesses, user_consent_settings.show_businesses),
    show_events = COALESCE(EXCLUDED.show_events, user_consent_settings.show_events),
    show_connections = COALESCE(EXCLUDED.show_connections, user_consent_settings.show_connections),
    updated_at = now();

-- First, update all dependent objects to use user_consent_settings instead of profiles

-- 1. Update RLS policies that depend on profile_visibility
DROP POLICY IF EXISTS "Allow profile access for events and users" ON public.profiles;

-- Recreate the policy to use user_consent_settings
CREATE POLICY "Allow profile access for events and users"
  ON public.profiles
  FOR SELECT
  USING (
    -- Users can always see their own profile
    auth.uid() = id
    OR
    -- Everyone can see profiles that are public according to consent settings
    EXISTS (
        SELECT 1 FROM user_consent_settings ucs
        WHERE ucs.user_id = profiles.id
        AND COALESCE(ucs.profile_visibility, true) = true
    )
    OR
    -- If no consent settings exist, default to public (backward compatibility)
    NOT EXISTS (
        SELECT 1 FROM user_consent_settings ucs
        WHERE ucs.user_id = profiles.id
    )
    OR
    -- Service role can see everything
    auth.jwt() ->> 'role' = 'service_role'
  );

-- 2. Update dependent views
-- Drop views that depend on profile_visibility column
DROP VIEW IF EXISTS public.professionals_with_search CASCADE;
DROP VIEW IF EXISTS public.events_with_creator CASCADE;
DROP VIEW IF EXISTS public.notifications_with_details CASCADE;

-- We'll recreate these views after dropping the columns

-- Update the public_professional_profiles view to use user_consent_settings
DROP VIEW IF EXISTS public.public_professional_profiles CASCADE;

CREATE VIEW public.public_professional_profiles AS 
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    p.created_at,
    p.updated_at,
    -- Only expose email if user has explicitly consented
    CASE 
        WHEN ucs.share_email_with_event_creators = true OR 
             ucs.share_email_with_attendees = true OR 
             ucs.share_contact_details = true
        THEN p.email 
        ELSE NULL 
    END AS email,
    -- Only show social links if profile is public
    CASE 
        WHEN COALESCE(ucs.profile_visibility, true) = true THEN p.linkedin
        ELSE NULL 
    END AS linkedin,
    CASE 
        WHEN COALESCE(ucs.profile_visibility, true) = true THEN p.twitter
        ELSE NULL 
    END AS twitter,
    CASE 
        WHEN COALESCE(ucs.profile_visibility, true) = true THEN p.website
        ELSE NULL 
    END AS website,
    p.location_id,
    p.main_industry_id,
    COALESCE(ucs.profile_visibility, true) AS profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    l.name AS location_name,
    i.name AS industry_name,
    -- Add the category_names column
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) AS category_names,
    -- Add professional_types column
    (
        SELECT array_agg(pt.name)
        FROM profile_professional_types ppt
        JOIN sustainability_professional_types pt ON pt.id = ppt.professional_type_id
        WHERE ppt.profile_id = p.id
    ) AS professional_types
FROM profiles p
LEFT JOIN user_consent_settings ucs ON ucs.user_id = p.id
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE COALESCE(ucs.profile_visibility, true) = true;

-- Grant permissions
GRANT SELECT ON public.public_professional_profiles TO authenticated;
GRANT SELECT ON public.public_professional_profiles TO anon;

-- Now safely drop the duplicate columns from profiles table
ALTER TABLE public.profiles DROP COLUMN IF EXISTS profile_visibility;
ALTER TABLE public.profiles DROP COLUMN IF EXISTS email_notifications;
ALTER TABLE public.profiles DROP COLUMN IF EXISTS newsletter_subscription;
ALTER TABLE public.profiles DROP COLUMN IF EXISTS show_businesses;
ALTER TABLE public.profiles DROP COLUMN IF EXISTS show_events;
ALTER TABLE public.profiles DROP COLUMN IF EXISTS show_connections;

-- Recreate the dependent views that were dropped
-- Note: These views will need to be updated to use user_consent_settings if they need privacy info

-- Recreate professionals_with_search view (simplified version without profile_visibility dependency)
CREATE OR REPLACE VIEW public.professionals_with_search AS
SELECT
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    p.linkedin,
    p.twitter,
    p.website,
    p.location_id,
    p.main_industry_id,
    p.subscription_tier,
    p.is_sustainability_professional,
    l.name AS location_name,
    i.name AS industry_name,
    -- Add search text for full-text search
    setweight(to_tsvector('english', COALESCE(p.first_name, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(p.last_name, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(p.organization, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(p.title, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(p.bio, '')), 'C') AS text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
-- Only include profiles that are public according to consent settings
WHERE EXISTS (
    SELECT 1 FROM user_consent_settings ucs
    WHERE ucs.user_id = p.id
    AND COALESCE(ucs.profile_visibility, true) = true
) OR NOT EXISTS (
    SELECT 1 FROM user_consent_settings ucs
    WHERE ucs.user_id = p.id
);

-- Recreate events_with_creator view
CREATE OR REPLACE VIEW public.events_with_creator AS
SELECT
    e.*,
    p.first_name AS creator_first_name,
    p.last_name AS creator_last_name,
    p.avatar_url AS creator_avatar_url,
    p.organization AS creator_organization,
    -- Only show creator email if they have consented
    CASE
        WHEN EXISTS (
            SELECT 1 FROM user_consent_settings ucs
            WHERE ucs.user_id = e.creator_user_id
            AND ucs.share_email_with_event_creators = true
        ) THEN p.email
        ELSE NULL
    END AS creator_email
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id;

-- Recreate notifications_with_details view (simplified)
CREATE OR REPLACE VIEW public.notifications_with_details AS
SELECT
    n.*,
    p.first_name,
    p.last_name,
    p.avatar_url
FROM notifications n
LEFT JOIN profiles p ON n.actor_id = p.id;

-- Grant permissions on recreated views
GRANT SELECT ON public.professionals_with_search TO authenticated;
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.notifications_with_details TO authenticated;

COMMIT;

-- Verify the cleanup
SELECT 
    'Cleanup verification' as status,
    COUNT(*) as profiles_count,
    (SELECT COUNT(*) FROM user_consent_settings) as consent_settings_count
FROM profiles;
