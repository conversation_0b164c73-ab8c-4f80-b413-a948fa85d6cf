-- Fix the repair function - resolve ambiguous column reference
-- Date: 2025-01-06

BEGIN;

-- Drop and recreate the repair function with fixed column references
DROP FUNCTION IF EXISTS public.repair_existing_users();

CREATE OR REPLACE FUNCTION public.repair_existing_users()
RETURNS TABLE(
  user_id UUID,
  profile_exists B<PERSON><PERSON><PERSON><PERSON>,
  consent_created BO<PERSON><PERSON><PERSON>,
  notification_prefs_created BOOLEAN,
  errors TEXT
) AS $$
DECLARE
  user_record RECORD;
  profile_exists BOOLEAN;
  consent_created BOOLEAN := FALSE;
  notification_prefs_created BOOLEAN := FALSE;
  error_msg TEXT := '';
BEGIN
  -- Loop through users who might be missing records
  FOR user_record IN 
    SELECT u.id, u.email, u.created_at, u.raw_user_meta_data
    FROM auth.users u
    WHERE u.created_at > '2025-01-01'  -- Recent users only
  LOOP
    -- Check if profile exists
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = user_record.id) INTO profile_exists;
    
    -- Create missing profile if needed
    IF NOT profile_exists THEN
      BEGIN
        INSERT INTO public.profiles (
          id, 
          first_name, 
          last_name, 
          email, 
          social_visibility, 
          subscription_tier, 
          subscription_status, 
          created_at, 
          updated_at
        )
        VALUES (
          user_record.id,
          user_record.raw_user_meta_data->>'first_name',
          user_record.raw_user_meta_data->>'last_name',
          user_record.email,
          'public',
          'none',
          'trial',
          user_record.created_at,
          user_record.created_at
        );
        profile_exists := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          error_msg := error_msg || 'Profile creation failed: ' || SQLERRM || '; ';
      END;
    END IF;
    
    -- Create missing consent settings (use qualified column names)
    IF NOT EXISTS (SELECT 1 FROM public.user_consent_settings ucs WHERE ucs.user_id = user_record.id) THEN
      BEGIN
        INSERT INTO public.user_consent_settings (
          user_id, profile_visibility, newsletter_subscription, 
          show_businesses, show_events, show_connections,
          created_at, updated_at
        )
        VALUES (
          user_record.id, true, true, true, true, true,
          user_record.created_at, user_record.created_at
        );
        consent_created := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          error_msg := error_msg || 'Consent settings failed: ' || SQLERRM || '; ';
      END;
    END IF;
    
    -- Create missing notification preferences (use qualified column names)
    IF profile_exists AND NOT EXISTS (SELECT 1 FROM public.notification_preferences np WHERE np.profile_id = user_record.id) THEN
      BEGIN
        INSERT INTO public.notification_preferences (
          profile_id, email_notifications, post_likes, post_comments,
          comment_replies, comment_likes, connection_requests, connection_accepted,
          event_signups, event_updates, system_notifications,
          created_at, updated_at
        )
        VALUES (
          user_record.id, true, true, true, true, true, true, true, true, true, true,
          user_record.created_at, user_record.created_at
        );
        notification_prefs_created := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          error_msg := error_msg || 'Notification prefs failed: ' || SQLERRM || '; ';
      END;
    END IF;
    
    -- Return results for this user
    RETURN QUERY SELECT 
      user_record.id,
      profile_exists,
      consent_created,
      notification_prefs_created,
      CASE WHEN error_msg = '' THEN NULL ELSE error_msg END;
      
    -- Reset for next iteration
    consent_created := FALSE;
    notification_prefs_created := FALSE;
    error_msg := '';
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.repair_existing_users() TO authenticated;

COMMIT;
