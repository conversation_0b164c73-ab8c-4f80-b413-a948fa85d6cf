-- Simple debug script to check connection data without auth context
-- Run this in Supabase SQL Editor

-- 1. Check all user_connections in the database
SELECT 
    'All connections in database' as section,
    id,
    user_id,
    connection_id,
    status,
    created_at
FROM public.user_connections
ORDER BY created_at DESC
LIMIT 20;

-- 2. Check for any duplicate IDs in the entire table
SELECT 
    'Duplicate IDs check' as section,
    id,
    COUNT(*) as count
FROM public.user_connections
GROUP BY id
HAVING COUNT(*) > 1;

-- 3. Check the specific ID that's causing issues
SELECT 
    'Specific ID check' as section,
    id,
    user_id,
    connection_id,
    status,
    created_at
FROM public.user_connections
WHERE id = '9109f126-e3f0-4937-a9e9-6f391911e031'::uuid;

-- 4. Check for any pending requests
SELECT 
    'All pending requests' as section,
    id,
    user_id,
    connection_id,
    status,
    created_at
FROM public.user_connections
WHERE status = 'pending'
ORDER BY created_at DESC;

-- 5. Check table structure
SELECT 
    'Table structure' as section,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'user_connections'
AND table_schema = 'public'
ORDER BY ordinal_position;
