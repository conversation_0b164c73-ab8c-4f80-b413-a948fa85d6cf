-- Fix the signup issue by removing email from profiles insert
-- The email column seems to be causing the issue
-- Date: 2025-01-03

BEGIN;

-- Update the handle_new_user function to exclude email from profiles insert
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert a new profile record for the new user WITHOUT email column
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  );

  -- Create default user_consent_settings for the new user
  INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    email_notifications,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    true,  -- profile_visibility - default to public
    true,  -- email_notifications - default to enabled
    false, -- newsletter_subscription - default to disabled
    true,  -- show_businesses - default to visible
    true,  -- show_events - default to visible
    true,  -- show_connections - default to visible
    false, -- share_email_with_event_creators - default to private
    false, -- share_email_with_attendees - default to private
    false, -- share_contact_details - default to private
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (user_id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates a profile record and user consent settings when a new user signs up - fixed email issue';

COMMIT;

-- Verify the function was updated
SELECT 'Signup function fixed - removed email column from profiles insert' as status;
