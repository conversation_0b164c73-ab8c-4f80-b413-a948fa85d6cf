-- Add comment likes to engagement score calculation for trending posts
-- This updates engagement_score in social_posts to include:
-- 1. Post likes (weight: 1.0)
-- 2. Comments count (weight: 2.0)
-- 3. Comment likes (weight: 0.5)

-- First, update engagement score function for post like counts
CREATE OR REPLACE FUNCTION public.update_post_likes_count() 
RETURNS TRIGGER AS $$
DECLARE
  likes_count INTEGER;
  comments_count INTEGER;
  comment_likes_count INTEGER;
BEGIN
  -- Ensure the function is executed in a trigger context
  IF TG_OP IS NULL THEN
    RAISE EXCEPTION 'update_post_likes_count() can only be called as a trigger';
  END IF;

  IF TG_OP = 'INSERT' OR TG_OP = 'DELETE' THEN
    -- Count likes
    SELECT COUNT(*) INTO likes_count
    FROM post_likes
    WHERE post_id = COALESCE(NEW.post_id, OLD.post_id);
    
    -- Get comments count
    SELECT comments_count INTO comments_count
    FROM social_posts
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);

    -- Get total comment likes for the post
    SELECT COALESCE(SUM(pc.likes_count), 0) INTO comment_likes_count
    FROM post_comments pc
    WHERE pc.post_id = COALESCE(NEW.post_id, OLD.post_id);
    
    -- Update post with new engagement score formula
    UPDATE social_posts
    SET 
      likes_count = likes_count,
      engagement_score = (likes_count * 1.0) + (comments_count * 2.0) + (comment_likes_count * 0.5)
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Update engagement score function for comments count
CREATE OR REPLACE FUNCTION public.update_post_comments_count() 
RETURNS TRIGGER AS $$
DECLARE
  comments_count INTEGER;
  likes_count INTEGER;
  comment_likes_count INTEGER;
BEGIN
  -- Ensure the function is executed in a trigger context
  IF TG_OP IS NULL THEN
    RAISE EXCEPTION 'update_post_comments_count() can only be called as a trigger';
  END IF;

  IF TG_OP = 'INSERT' OR TG_OP = 'DELETE' THEN
    -- Count top-level comments
    SELECT COUNT(*) INTO comments_count
    FROM post_comments
    WHERE post_id = COALESCE(NEW.post_id, OLD.post_id) AND parent_comment_id IS NULL;
    
    -- Get likes count
    SELECT likes_count INTO likes_count
    FROM social_posts
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);

    -- Get total comment likes for the post
    SELECT COALESCE(SUM(pc.likes_count), 0) INTO comment_likes_count
    FROM post_comments pc
    WHERE pc.post_id = COALESCE(NEW.post_id, OLD.post_id);
    
    -- Update post with new engagement score formula
    UPDATE social_posts
    SET 
      comments_count = comments_count,
      engagement_score = (likes_count * 1.0) + (comments_count * 2.0) + (comment_likes_count * 0.5)
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a function to update the parent post's engagement score when a comment is liked
CREATE OR REPLACE FUNCTION public.update_post_engagement_on_comment_like() 
RETURNS TRIGGER AS $$
DECLARE
  post_id_var UUID;
  likes_count INTEGER;
  comments_count INTEGER;
  comment_likes_count INTEGER;
BEGIN
  -- Get the post_id for the comment being liked
  SELECT post_id INTO post_id_var
  FROM post_comments
  WHERE id = COALESCE(NEW.comment_id, OLD.comment_id);

  -- Get likes count
  SELECT likes_count INTO likes_count
  FROM social_posts
  WHERE id = post_id_var;

  -- Get comments count
  SELECT comments_count INTO comments_count
  FROM social_posts
  WHERE id = post_id_var;

  -- Get total comment likes for the post
  SELECT COALESCE(SUM(pc.likes_count), 0) INTO comment_likes_count
  FROM post_comments pc
  WHERE pc.post_id = post_id_var;
  
  -- Update post with new engagement score formula
  UPDATE social_posts
  SET engagement_score = (likes_count * 1.0) + (comments_count * 2.0) + (comment_likes_count * 0.5)
  WHERE id = post_id_var;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for the new function
DROP TRIGGER IF EXISTS update_post_engagement_on_comment_like_trigger ON public.comment_likes;
CREATE TRIGGER update_post_engagement_on_comment_like_trigger
AFTER INSERT OR DELETE ON public.comment_likes
FOR EACH ROW
EXECUTE FUNCTION public.update_post_engagement_on_comment_like();

-- Update all posts to recalculate engagement score with comment likes
UPDATE social_posts sp
SET engagement_score = (
  sp.likes_count * 1.0 +
  sp.comments_count * 2.0 +
  COALESCE((
    SELECT SUM(pc.likes_count)
    FROM post_comments pc
    WHERE pc.post_id = sp.id
  ), 0) * 0.5
);
