-- Add debugging helper functions for account deletion

-- Function to safely execute a query and return results
CREATE OR REPLACE FUNCTION check_table_data(query_text text, user_id_param uuid)
RETURNS jsonb
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
    result jsonb;
BEGIN
    EXECUTE format('SELECT jsonb_agg(row_to_json(t)) FROM (%s) t', query_text)
    INTO result
    USING user_id_param;
    
    RETURN COALESCE(result, '[]'::jsonb);
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION check_table_data TO authenticated;

-- Add comment explaining the function
COMMENT ON FUNCTION check_table_data IS 'Safely executes a query to check for user data in various tables. Used for debugging account deletion issues.';

-- Create a function to check for foreign key references
CREATE OR REPLACE FUNCTION check_user_references(user_id uuid)
RETURNS TABLE (
    table_name text,
    column_name text,
    reference_count bigint
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.table_name::text,
        c.column_name::text,
        count(*)::bigint as reference_count
    FROM
        information_schema.columns c
    WHERE
        c.data_type = 'uuid'
        AND c.table_schema = 'public'
    GROUP BY
        c.table_name,
        c.column_name
    HAVING
        count(*) > 0;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION check_user_references TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION check_user_references IS 'Checks all tables for references to a user ID. Helpful for debugging deletion issues.';
