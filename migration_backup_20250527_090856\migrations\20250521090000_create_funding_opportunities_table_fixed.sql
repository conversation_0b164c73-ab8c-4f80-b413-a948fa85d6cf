-- Create funding opportunities table
CREATE TABLE IF NOT EXISTS "public"."funding_opportunities" (
  "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  "title" TEXT NOT NULL,
  "organization_name" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "eligibility" TEXT NOT NULL,
  "type" TEXT NOT NULL CHECK (type IN ('Grant', 'Tender')),
  "amount" TEXT NOT NULL,
  "deadline" DATE NOT NULL,
  "website_url" TEXT,
  "status" TEXT NOT NULL DEFAULT 'Published' CHECK (status IN ('Draft', 'Published', 'Closed', 'Archived')),
  "featured" BOOLEAN NOT NULL DEFAULT FALSE,
  "creator_id" UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  PRIMARY KEY ("id")
);

-- Set up Row Level Security (RLS)
ALTER TABLE "public"."funding_opportunities" ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can view published funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can insert their own funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Authenticated users can insert funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can update their own funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can delete their own funding opportunities" ON "public"."funding_opportunities";

-- Allow users to see all published opportunities
CREATE POLICY "Anyone can view published funding opportunities" 
ON "public"."funding_opportunities" FOR SELECT USING (status = 'Published');

-- Allow users to manage their own opportunities
CREATE POLICY "Users can insert their own funding opportunities" 
ON "public"."funding_opportunities" FOR INSERT WITH CHECK (auth.uid() = creator_id);

-- Ensure authenticated users can always insert funding opportunities
CREATE POLICY "Authenticated users can insert funding opportunities"
ON "public"."funding_opportunities" FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Users can update their own funding opportunities" 
ON "public"."funding_opportunities" FOR UPDATE USING (auth.uid() = creator_id);

CREATE POLICY "Users can delete their own funding opportunities" 
ON "public"."funding_opportunities" FOR DELETE USING (auth.uid() = creator_id);

-- Create table for regions associated with funding opportunities
CREATE TABLE IF NOT EXISTS "public"."funding_opportunity_regions" (
  "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  "funding_opportunity_id" UUID NOT NULL REFERENCES funding_opportunities(id) ON DELETE CASCADE,
  "location_id" TEXT NOT NULL,
  PRIMARY KEY ("id"),
  UNIQUE ("funding_opportunity_id", "location_id")
);

-- Create table for categories associated with funding opportunities
CREATE TABLE IF NOT EXISTS "public"."funding_opportunity_categories" (
  "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  "funding_opportunity_id" UUID NOT NULL REFERENCES funding_opportunities(id) ON DELETE CASCADE,
  "category_id" TEXT NOT NULL,
  PRIMARY KEY ("id"),
  UNIQUE ("funding_opportunity_id", "category_id")
);

-- Enable RLS on linking tables
ALTER TABLE "public"."funding_opportunity_regions" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."funding_opportunity_categories" ENABLE ROW LEVEL SECURITY;

-- Drop region policies if they exist
DROP POLICY IF EXISTS "Anyone can view funding opportunity regions" ON "public"."funding_opportunity_regions";
DROP POLICY IF EXISTS "Creator can insert funding opportunity regions" ON "public"."funding_opportunity_regions";
DROP POLICY IF EXISTS "Creator can update funding opportunity regions" ON "public"."funding_opportunity_regions";
DROP POLICY IF EXISTS "Creator can delete funding opportunity regions" ON "public"."funding_opportunity_regions";

-- Create policies for regions
CREATE POLICY "Anyone can view funding opportunity regions" 
ON "public"."funding_opportunity_regions" FOR SELECT TO authenticated USING (true);

CREATE POLICY "Creator can insert funding opportunity regions" 
ON "public"."funding_opportunity_regions" FOR INSERT 
WITH CHECK (EXISTS (
  SELECT 1 FROM funding_opportunities 
  WHERE id = funding_opportunity_id AND creator_id = auth.uid()
));

CREATE POLICY "Creator can update funding opportunity regions" 
ON "public"."funding_opportunity_regions" FOR UPDATE 
USING (EXISTS (
  SELECT 1 FROM funding_opportunities 
  WHERE id = funding_opportunity_id AND creator_id = auth.uid()
));

CREATE POLICY "Creator can delete funding opportunity regions" 
ON "public"."funding_opportunity_regions" FOR DELETE 
USING (EXISTS (
  SELECT 1 FROM funding_opportunities 
  WHERE id = funding_opportunity_id AND creator_id = auth.uid()
));

-- Drop category policies if they exist
DROP POLICY IF EXISTS "Anyone can view funding opportunity categories" ON "public"."funding_opportunity_categories";
DROP POLICY IF EXISTS "Creator can insert funding opportunity categories" ON "public"."funding_opportunity_categories";
DROP POLICY IF EXISTS "Creator can update funding opportunity categories" ON "public"."funding_opportunity_categories";
DROP POLICY IF EXISTS "Creator can delete funding opportunity categories" ON "public"."funding_opportunity_categories";

-- Create policies for categories
CREATE POLICY "Anyone can view funding opportunity categories" 
ON "public"."funding_opportunity_categories" FOR SELECT TO authenticated USING (true);

CREATE POLICY "Creator can insert funding opportunity categories" 
ON "public"."funding_opportunity_categories" FOR INSERT 
WITH CHECK (EXISTS (
  SELECT 1 FROM funding_opportunities 
  WHERE id = funding_opportunity_id AND creator_id = auth.uid()
));

CREATE POLICY "Creator can update funding opportunity categories" 
ON "public"."funding_opportunity_categories" FOR UPDATE 
USING (EXISTS (
  SELECT 1 FROM funding_opportunities 
  WHERE id = funding_opportunity_id AND creator_id = auth.uid()
));

CREATE POLICY "Creator can delete funding opportunity categories" 
ON "public"."funding_opportunity_categories" FOR DELETE 
USING (EXISTS (
  SELECT 1 FROM funding_opportunities 
  WHERE id = funding_opportunity_id AND creator_id = auth.uid()
));

-- Create a function to maintain the updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS update_funding_opportunities_updated_at ON funding_opportunities;

-- Create a trigger to update the timestamp
CREATE TRIGGER update_funding_opportunities_updated_at
BEFORE UPDATE ON funding_opportunities
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Create a function to automatically close funding opportunities after their deadline
CREATE OR REPLACE FUNCTION auto_close_expired_funding_opportunities()
RETURNS TRIGGER AS $$
BEGIN
    -- Update status to 'Closed' for opportunities past their deadline
    UPDATE funding_opportunities
    SET status = 'Closed'
    WHERE deadline < CURRENT_DATE
    AND status = 'Published';
    
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS check_expired_funding_opportunities ON funding_opportunities;

-- Create a trigger that runs daily to check for expired opportunities
CREATE TRIGGER check_expired_funding_opportunities
AFTER INSERT OR UPDATE ON funding_opportunities
FOR EACH STATEMENT
EXECUTE FUNCTION auto_close_expired_funding_opportunities();

-- Create a view that joins all the tables for easier querying
CREATE OR REPLACE VIEW funding_opportunities_view AS
SELECT 
  f.id,
  f.created_at,
  f.updated_at,
  f.title,
  f.organization_name,
  f.description,
  f.eligibility,
  f.type,
  f.amount,
  f.deadline,
  f.website_url,
  f.status,
  f.creator_id,
  (SELECT array_agg(fr.location_id) FROM funding_opportunity_regions fr WHERE fr.funding_opportunity_id = f.id) as regions,
  (SELECT array_agg(fc.category_id) FROM funding_opportunity_categories fc WHERE fc.funding_opportunity_id = f.id) as categories
FROM funding_opportunities f;

-- Grant necessary privileges to public role
GRANT SELECT ON funding_opportunities_view TO public;
