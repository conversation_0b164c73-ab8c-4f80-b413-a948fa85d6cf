-- Fix public_professional_profiles view to include category_names
-- Date: 2025-01-03

BEGIN;

-- Drop and recreate the public_professional_profiles view with category_names
DROP VIEW IF EXISTS public.public_professional_profiles;

CREATE OR REPLACE VIEW public.public_professional_profiles AS 
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    -- Only expose email if user has consented and with proper access control
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM user_consent_settings ucs 
            WHERE ucs.user_id = p.id AND 
                (ucs.share_email_with_event_creators = true OR 
                ucs.share_email_with_attendees = true OR 
                ucs.share_contact_details = true)
        ) THEN p.email 
        ELSE NULL 
    END as email,
    -- Only show social links if profile is public
    CASE 
        WHEN p.profile_visibility = true THEN p.linkedin
        ELSE NULL 
    END as linkedin,
    CASE 
        WHEN p.profile_visibility = true THEN p.twitter
        ELSE NULL 
    END as twitter,
    CASE 
        WHEN p.profile_visibility = true THEN p.website
        ELSE NULL 
    END as website,
    p.location_id,
    p.main_industry_id,
    p.profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    l.name AS location_name,
    i.name AS industry_name,
    -- Add the missing category_names column
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) AS category_names,
    -- Add professional_types column
    (
        SELECT array_agg(pt.name)
        FROM profile_professional_types ppt
        JOIN sustainability_professional_types pt ON pt.id = ppt.professional_type_id
        WHERE ppt.profile_id = p.id
    ) AS professional_types
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE p.profile_visibility = true;

-- Grant permissions
GRANT SELECT ON public.public_professional_profiles TO authenticated;
GRANT SELECT ON public.public_professional_profiles TO anon;

COMMIT;

-- Test the view
SELECT 
    id,
    first_name,
    last_name,
    category_names,
    professional_types
FROM public.public_professional_profiles
WHERE category_names IS NOT NULL
LIMIT 5;
