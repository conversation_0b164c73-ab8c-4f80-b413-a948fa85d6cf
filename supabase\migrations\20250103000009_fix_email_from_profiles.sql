-- Fix the get_event_attendee_emails function to use profiles.email instead of auth.users.email
-- Since event_signups.user_id references profiles.id and profiles has an email column

-- Drop the existing function
DROP FUNCTION IF EXISTS public.get_event_attendee_emails(UUID);

-- Create the correct function that gets email from profiles table
CREATE OR REPLACE FUNCTION public.get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow event creators to access emails
  IF NOT EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND creator_user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Access denied: You must be the event creator to view attendee emails';
  END IF;

  -- Return emails only for users who have given GDPR consent
  -- Get email from profiles table since that's where it's stored
  RETURN QUERY
  SELECT 
    es.user_id,
    p.email
  FROM 
    event_signups es
  JOIN 
    profiles p ON es.user_id = p.id
  WHERE 
    es.event_id = get_event_attendee_emails.event_id
    AND es.gdpr_consent = true
    AND p.email IS NOT NULL;  -- Only return rows where email exists
END;
$$;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION public.get_event_attendee_emails(UUID) TO authenticated;

-- Create a debug function to help troubleshoot
CREATE OR REPLACE FUNCTION public.debug_event_email_access(event_id UUID)
RETURNS TABLE (
    debug_info TEXT,
    user_id UUID,
    email TEXT,
    gdpr_consent BOOLEAN,
    is_creator BOOLEAN,
    user_name TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID := auth.uid();
    is_event_creator BOOLEAN;
BEGIN
  -- Check if current user is the event creator
  SELECT EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND creator_user_id = current_user_id
  ) INTO is_event_creator;

  -- Return debug information
  RETURN QUERY
  SELECT 
    CASE 
      WHEN is_event_creator THEN 'User is event creator'
      ELSE 'User is NOT event creator'
    END as debug_info,
    es.user_id,
    p.email,
    es.gdpr_consent,
    is_event_creator,
    COALESCE(p.first_name || ' ' || p.last_name, 'Unknown') as user_name
  FROM 
    event_signups es
  JOIN 
    profiles p ON es.user_id = p.id
  WHERE 
    es.event_id = debug_event_email_access.event_id;
END;
$$;

-- Grant access to the debug function
GRANT EXECUTE ON FUNCTION public.debug_event_email_access(UUID) TO authenticated;

-- Test the function works
SELECT 'Email function updated to use profiles.email successfully' as status;
