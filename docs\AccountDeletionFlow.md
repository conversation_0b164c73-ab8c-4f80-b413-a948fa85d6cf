# Account Deletion and Image Cleanup Flow

## Overview

The account deletion process handles the removal of user data, including media files, while respecting database foreign key constraints.

## Delete Order

1. **Post Likes** 
   - Table: `post_likes`
   - Reason: Must be deleted first as they reference both comments and posts

2. **Comments**
   - Table: `post_comments`
   - Reason: Must be deleted before posts as they reference posts

3. **Social Posts**
   - Table: `social_posts`
   - Triggers: Activates image deletion trigger for Cloudflare cleanup
   - Note: Handles post media automatically via trigger

4. **Businesses**
   - Table: `businesses`
   - Cascades: Deletes related business data
   - Note: Will trigger logo cleanup

5. **Profile**
   - Table: `profiles`
   - Cascades: Deletes user connections
   - Note: Must happen before auth.user deletion

6. **Auth User**
   - Table: `auth.users`
   - Final step: Done only after all other data is cleaned

## Image Deletion

### Automatic Image Cleanup (via Trigger)
- Trigger: `handle_deleted_post_media`
- Activated: When any post with an image is deleted
- Function: Calls Edge Function to delete Cloudflare image
- Usage: No manual steps needed - deleting a post automatically cleans up its image

### Edge Functions

1. `cloudflare-images`
   - Purpose: Main image handling Edge Function
   - Capabilities: Upload, delete, and manage Cloudflare images
   - Used by: The entire application for image management
   - Has built-in image deletion capability

2. `delete-account`
   - Purpose: Handle user account deletion
   - Verifies user identity via session token
   - Calls database function for database cleaning
   - Uses service role key securely server-side
   - Handles auth user deletion after database cleanup

### Image Deletion Flow
When deleting content with images:
1. Database trigger `handle_deleted_post_media` activates
2. Trigger calls `cloudflare-images` Edge Function with delete method
3. Images are automatically removed from Cloudflare
4. Works for both single post deletion and account deletion

### Configuration
- Uses Supabase's built-in configuration mechanisms
- Edge Function URL constructed from database name
- Service role key stored securely as database parameter
- No custom settings tables needed
- Environment variables for Cloudflare API tokens

## Code Components

### Frontend (UserSettings.tsx)
- Calls the secure `delete-account` Edge Function
- Passes user's session token for authentication
- Provides user feedback via toasts
- Redirects after successful deletion

```typescript
// UserSettings.tsx (Account Deletion Handler)
const handleDeleteAccount = async () => {
  // Get the current user's session
  const { data: { session } } = await supabase.auth.getSession();
  
  // Call Edge Function with user's token
  const response = await fetch(
    `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/delete-account`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      }
    }
  );

  // Handle response...
};
```

### Database Function (delete_user_account)
```sql
CREATE OR REPLACE FUNCTION delete_user_account(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Delete in correct order to handle foreign key constraints
  -- 1. Delete likes first as they reference comments and posts
  DELETE FROM post_likes WHERE user_id = $1;
  
  -- 2. Delete all comments as they reference posts
  DELETE FROM post_comments WHERE user_id = $1;
  
  -- 3. Delete posts (which will trigger image deletion via our trigger)
  DELETE FROM social_posts WHERE user_id = $1;
  
  -- 4. Delete user businesses (which will cascade to related tables)
  DELETE FROM businesses WHERE owner_id = $1;
  
  -- 5. Delete profile (which will cascade to connections)
  DELETE FROM profiles WHERE id = $1;
  
  -- Auth user deletion handled by Edge Function
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Database Triggers (SQL)
```sql
CREATE OR REPLACE FUNCTION handle_deleted_post_media()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the deleted post had an image
  IF OLD.media_type = 'image' AND OLD.media_url IS NOT NULL THEN
    -- Call edge function to delete Cloudflare image
    PERFORM pg_net.http_post(
      url := 'https://' || current_database() || '.supabase.co/functions/v1/cloudflare-images',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || NULLIF(current_setting('app.settings.service_role_key', true), '')::text
      ),
      body := jsonb_build_object(
        'method', 'delete',
        'record', jsonb_build_object(
          'media_url', OLD.media_url,
          'media_type', OLD.media_type
        )
      )
    );
  END IF;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_deleted_post_media_trigger
  AFTER DELETE ON social_posts
  FOR EACH ROW
  EXECUTE FUNCTION handle_deleted_post_media();
```

## Important Notes

1. Order is critical to avoid foreign key violations
2. Database function has SECURITY DEFINER to bypass RLS
3. Each step has error handling but continues flow
4. Image cleanup happens automatically via triggers
5. Successful deletion ends with sign out and redirect
6. Service role key is securely used in Edge Function only

## Common Issues

1. **Foreign Key Constraints**
   - Solution: Follow correct deletion order
   - Order: Likes → Comments → Posts → Businesses → Profile → Auth

2. **Image Cleanup Failures**
   - For single posts: Handled by database trigger
   - For account deletion: Images deleted via the database trigger
   - Non-blocking: continues even if image deletion fails
   - Logs errors for monitoring

3. **Auth User Deletion**
   - Must be last step
   - Requires all references removed first

4. **✅ Security Issue Fixed**
   - Previous implementation exposed the service role key in client-side code
   - Now service role key is only used server-side in Edge Functions
   - Database function with SECURITY DEFINER allows controlled elevated permissions
   - RLS policies ensure users can only delete their own data
