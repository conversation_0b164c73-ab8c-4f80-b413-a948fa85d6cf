-- Drop the incorrectly named table
DROP TABLE IF EXISTS public.net_zero_categories CASCADE;

-- Add parent_id to industries if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'industries' 
        AND column_name = 'parent_id'
    ) THEN
        ALTER TABLE public.industries 
        ADD COLUMN parent_id UUID REFERENCES public.industries(id);
        
        -- Create index for the new parent_id column
        CREATE INDEX idx_industries_parent_id ON public.industries(parent_id);
    END IF;
END $$;

-- Update migrations to use netzero_categories (without underscore)
UPDATE pg_catalog.pg_depend d
SET refobjid = (
    SELECT oid FROM pg_catalog.pg_class 
    WHERE relname = 'netzero_categories' AND relnamespace = (
        SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = 'public'
    )
)
WHERE refobjid = (
    SELECT oid FROM pg_catalog.pg_class 
    WHERE relname = 'net_zero_categories' AND relnamespace = (
        SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = 'public'
    )
);
