
-- Drop existing policies to recreate them with proper email protection
DROP POLICY IF EXISTS "Users can see their own email" ON public.profiles;

-- Basic profile information can be seen by any authenticated user
CREATE POLICY "Users can view basic profile info"
ON public.profiles
FOR SELECT
USING (
  -- Everyone can see basic profile info
  TRUE
);

-- Restrict columns that this policy applies to
ALTER TABLE public.profiles
  ALTER COLUMN email SET DEFAULT NULL,
  -- Make sure email always syncs from auth.users
  ADD CONSTRAINT email_matches_auth 
  CHECK (
    email = (SELECT email FROM auth.users WHERE id = profiles.id)
  );

-- Create policy for email visibility
CREATE POLICY "Users can only see their own email"
ON public.profiles
FOR SELECT
USING (
  -- Can only see email if you are the profile owner
  auth.uid() = id
)
WITH CHECK (
  auth.uid() = id
);

-- Create policy for event creators to see participant emails
CREATE POLICY "Event creators can see participant emails"
ON public.profiles
FOR SELECT
USING (
  -- Can see email if you are the creator of an event the profile owner is attending
  EXISTS (
    SELECT 1 
    FROM events e
    JOIN event_signups es ON e.id = es.event_id
    WHERE e.created_by = auth.uid()
    AND es.user_id = profiles.id
    AND es.gdpr_consent = true
  )
);

-- Make sure RLS is enabled
ALTER TABLE public.profiles FORCE ROW LEVEL SECURITY;

-- Create a function to check if a user can see another user's email
CREATE OR REPLACE FUNCTION can_view_user_email(target_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- User can always see their own email
  IF auth.uid() = target_user_id THEN
    RETURN TRUE;
  END IF;

  -- Event creators can see emails of consenting participants
  IF EXISTS (
    SELECT 1 
    FROM events e
    JOIN event_signups es ON e.id = es.event_id
    WHERE e.created_by = auth.uid()
    AND es.user_id = target_user_id
    AND es.gdpr_consent = true
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;

-- Grant usage on the function
GRANT EXECUTE ON FUNCTION can_view_user_email TO authenticated;

-- Update the event_signups_with_users view to use the new security function
CREATE OR REPLACE VIEW public.event_signups_with_users AS
SELECT 
  es.*,
  CONCAT(p.first_name, ' ', p.last_name) as full_name,
  p.avatar_url,
  p.title,
  p.organization,
  -- Only include email if user has permission to see it
  CASE 
    WHEN can_view_user_email(es.user_id) THEN p.email
    ELSE NULL
  END as email
FROM 
  event_signups es
LEFT JOIN 
  public.profiles p ON es.user_id = p.id;

-- Update the get_event_attendee_emails function to use the security function
CREATE OR REPLACE FUNCTION get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT
) 
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        es.user_id,
        CASE 
            WHEN can_view_user_email(es.user_id) THEN p.email
            ELSE NULL
        END as email
    FROM 
        event_signups es
    JOIN 
        public.profiles p ON es.user_id = p.id
    WHERE 
        es.event_id = event_id
        AND es.gdpr_consent = true;
END;
$$ LANGUAGE plpgsql;

-- Create an audit log table for email access
CREATE TABLE IF NOT EXISTS public.email_access_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    accessed_by UUID REFERENCES auth.users(id),
    accessed_email UUID REFERENCES profiles(id),
    access_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS on the audit log
ALTER TABLE public.email_access_logs ENABLE ROW LEVEL SECURITY;

-- Only allow inserts and the user can only see their own access logs
CREATE POLICY "Users can see their own access logs"
    ON public.email_access_logs
    FOR SELECT
    USING (accessed_by = auth.uid());

CREATE POLICY "System can insert logs"
    ON public.email_access_logs
    FOR INSERT
    WITH CHECK (true);

-- Create a function to log email access
CREATE OR REPLACE FUNCTION log_email_access()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    IF NEW.email IS NOT NULL AND OLD.email IS NULL THEN
        INSERT INTO public.email_access_logs (accessed_by, accessed_email, access_reason)
        VALUES (auth.uid(), NEW.id, 'Profile email access');
    END IF;
    RETURN NEW;
END;
$$;

-- Add trigger for logging email access
CREATE TRIGGER log_email_access_trigger
    AFTER UPDATE OF email ON public.profiles
    FOR EACH ROW
    WHEN (NEW.email IS NOT NULL AND OLD.email IS NULL)
    EXECUTE FUNCTION log_email_access();
