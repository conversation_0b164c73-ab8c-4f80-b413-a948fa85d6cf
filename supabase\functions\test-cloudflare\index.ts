// Test function to verify Cloudflare credentials and API access
// This helps debug Cloudflare integration issues

const CLOUDFLARE_API_TOKEN = Deno.env.get('CLOUDFLARE_API_TOKEN');
const CLOUDFLARE_ACCOUNT_ID = Deno.env.get('CLOUDFLARE_ACCOUNT_ID');

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

Deno.serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('Testing Cloudflare credentials...');

    // Check environment variables
    const envCheck = {
      hasApiToken: !!CLOUDFLARE_API_TOKEN,
      hasAccountId: !!CLOUDFLARE_ACCOUNT_ID,
      apiTokenLength: CLOUDFLARE_API_TOKEN?.length || 0,
      accountIdLength: CLOUDFLARE_ACCOUNT_ID?.length || 0
    };

    console.log('Environment check:', envCheck);

    if (!CLOUDFLARE_API_TOKEN || !CLOUDFLARE_ACCOUNT_ID) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing Cloudflare credentials',
          envCheck
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    // Test API access by listing images (limit to 1 to minimize impact)
    console.log('Testing Cloudflare API access...');
    
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/images/v1?per_page=1`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const responseText = await response.text();
    console.log(`Cloudflare API response: ${response.status} - ${responseText}`);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = { raw: responseText };
    }

    if (response.ok) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Cloudflare API access successful',
          envCheck,
          apiResponse: {
            status: response.status,
            success: responseData.success,
            resultCount: responseData.result?.length || 0,
            errors: responseData.errors || [],
            messages: responseData.messages || []
          }
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Cloudflare API access failed',
          envCheck,
          apiResponse: {
            status: response.status,
            data: responseData,
            rawResponse: responseText
          }
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

  } catch (error) {
    console.error('Error testing Cloudflare:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        stack: error.stack
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});
