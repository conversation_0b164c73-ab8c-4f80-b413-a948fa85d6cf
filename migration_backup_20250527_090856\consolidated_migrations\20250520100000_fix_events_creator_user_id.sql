-- Fix Events Table Creator ID Fields
-- This migration resolves inconsistencies between owner_id and creator_user_id fields

-- First, inspect the existing columns to understand current state
DO $$ 
DECLARE
  has_owner_id BOOLEAN;
  has_creator_user_id BOOLEAN;
BEGIN
  -- Check if owner_id exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'events' AND column_name = 'owner_id'
  ) INTO has_owner_id;
  
  -- Check if creator_user_id exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'events' AND column_name = 'creator_user_id'
  ) INTO has_creator_user_id;

  -- Log the current state
  RAISE NOTICE 'Events table current state: has_owner_id=%, has_creator_user_id=%', has_owner_id, has_creator_user_id;

  -- Case 1: Only owner_id exists - Add creator_user_id and copy data from owner_id
  IF has_owner_id AND NOT has_creator_user_id THEN
    RAISE NOTICE 'Adding creator_user_id column and copying data from owner_id';
    
    -- Add creator_user_id column
    ALTER TABLE public.events ADD COLUMN creator_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
    
    -- Copy data from owner_id to creator_user_id
    -- First copy directly for non-null values
    UPDATE public.events SET creator_user_id = owner_id WHERE owner_id IS NOT NULL;
    
    -- If there are any rows where owner_id is NULL but should have a creator_user_id,
    -- you might need additional logic here to retrieve the creator
    
    -- Add NOT NULL constraint after data migration
    ALTER TABLE public.events ALTER COLUMN creator_user_id SET NOT NULL;
  
  -- Case 2: Only creator_user_id exists - Add owner_id and copy data from creator_user_id  
  ELSIF has_creator_user_id AND NOT has_owner_id THEN
    RAISE NOTICE 'Adding owner_id column and copying data from creator_user_id';
    
    -- Add owner_id column
    ALTER TABLE public.events ADD COLUMN owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE;
    
    -- Copy data from creator_user_id to owner_id
    UPDATE public.events SET owner_id = creator_user_id;

  -- Case 3: Both columns exist - Ensure data consistency between them
  ELSIF has_creator_user_id AND has_owner_id THEN
    RAISE NOTICE 'Both columns exist - ensuring data consistency';
    
    -- Copy from creator_user_id to owner_id where owner_id is NULL
    UPDATE public.events SET owner_id = creator_user_id WHERE owner_id IS NULL;
    
    -- Copy from owner_id to creator_user_id where creator_user_id is NULL
    UPDATE public.events SET creator_user_id = owner_id WHERE creator_user_id IS NULL;
    
    -- For conflicting values (different in both columns), prefer creator_user_id as the source of truth
    -- as that's what the application uses
    UPDATE public.events SET owner_id = creator_user_id WHERE owner_id != creator_user_id;
  
  -- Case 4: Neither column exists - Create both columns (unlikely scenario)
  ELSE
    RAISE EXCEPTION 'Neither owner_id nor creator_user_id exists in the events table. Database structure appears damaged.';
  END IF;

  -- Update all RLS policies to use creator_user_id consistently
  -- First check if policies exist that use owner_id
  IF has_owner_id THEN    -- Check and update policies for CREATE
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Enable insert for authenticated users only') THEN
      DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.events;
      IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Users can create their own events') THEN
        CREATE POLICY "Users can create their own events" 
        ON public.events 
        FOR INSERT WITH CHECK (auth.uid() = creator_user_id);
      END IF;
    END IF;-- Check and update policies for UPDATE
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Enable update for users based on owner_id') THEN
      DROP POLICY IF EXISTS "Enable update for users based on owner_id" ON public.events;
      -- Only create if it doesn't exist yet
      IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Users can update their own events') THEN
        CREATE POLICY "Users can update their own events" 
        ON public.events 
        FOR UPDATE USING (auth.uid() = creator_user_id);
      END IF;
    END IF;    -- Check and update policies for DELETE
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Enable delete for users based on owner_id') THEN
      DROP POLICY IF EXISTS "Enable delete for users based on owner_id" ON public.events;
      -- Only create if it doesn't exist yet
      IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Users can delete their own events') THEN      IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Users can delete their own events') THEN
        CREATE POLICY "Users can delete their own events" 
          ON public.events 
          FOR DELETE USING (auth.uid() = creator_user_id);
      END IF;
      END IF;
    END IF;    -- Check and update any SELECT policies that rely on owner_id
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Enable read access for all users') THEN
      DROP POLICY IF EXISTS "Enable read access for all users" ON public.events;
      -- Only create if it doesn't exist yet
      IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'events' AND policyname = 'Everyone can view events') THEN
        CREATE POLICY "Everyone can view events" 
        ON public.events 
        FOR SELECT USING (true);
      END IF;
    END IF;
  END IF;

  -- Update views that rely on these columns
  IF EXISTS (SELECT 1 FROM pg_views WHERE viewname = 'events_with_creator') THEN
    DROP VIEW IF EXISTS public.events_with_creator;
    
    CREATE OR REPLACE VIEW public.events_with_creator AS
    SELECT 
      e.*,
      au.email as creator_email,
      CONCAT(p.first_name, ' ', p.last_name) as creator_name,
      p.avatar_url as creator_avatar_url
    FROM 
      public.events e
    JOIN 
      auth.users au ON e.creator_user_id = au.id
    LEFT JOIN 
      public.profiles p ON e.creator_user_id = p.id;

    -- Grant appropriate permissions on the view
    ALTER VIEW public.events_with_creator OWNER TO postgres;
    GRANT SELECT ON public.events_with_creator TO authenticated;
    GRANT SELECT ON public.events_with_creator TO service_role;
  END IF;

  -- Update any foreign keys in event_signups that might reference owner_id
  -- This is a precaution in case some installations have FK relationships to owner_id
  IF has_owner_id AND EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'event_signups'
  ) THEN
    -- Ensure all event signup policies use creator_user_id for any owner checks
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'event_signups' AND policyname LIKE '%owner%') THEN
      -- Drop and recreate any problematic policies (actual names may vary across installations)      DROP POLICY IF EXISTS "Enable update for users or event owners" ON public.event_signups;
      IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'event_signups' AND policyname = 'Enable update for users or event creators') THEN
        CREATE POLICY "Enable update for users or event creators" 
        ON public.event_signups 
        FOR UPDATE USING (
          user_id = auth.uid() OR 
          EXISTS (SELECT 1 FROM events WHERE id = event_id AND creator_user_id = auth.uid())
        );
      END IF;      DROP POLICY IF EXISTS "Enable delete for users or event owners" ON public.event_signups;
      IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'event_signups' AND policyname = 'Enable delete for users or event creators') THEN
        CREATE POLICY "Enable delete for users or event creators" 
        ON public.event_signups 
        FOR DELETE USING (
          user_id = auth.uid() OR 
          EXISTS (SELECT 1 FROM events WHERE id = event_id AND creator_user_id = auth.uid())
        );
      END IF;
    END IF;
  END IF;
END $$;

-- Create an index for faster lookups
CREATE INDEX IF NOT EXISTS events_creator_user_id_idx ON public.events(creator_user_id);

-- Comment to document the purpose and structure of the events table
COMMENT ON TABLE public.events IS 'Events created by users, with creator_user_id as the primary reference to the creator';
COMMENT ON COLUMN public.events.creator_user_id IS 'Reference to auth.users(id) identifying the creator of this event';
