# Connecting to Remote Supabase

This guide explains how to connect your local project to the remote Supabase instance.

## Prerequisites

1. Access to your Supabase project dashboard
2. Supabase project URL and API keys
3. Node.js and npm installed

## Setup Instructions

### Windows PowerShell

1. **Connect to Remote Supabase**
   ```powershell
   npm run supabase:connect-remote
   ```
   This will prompt you for:
   - Supabase URL (e.g., https://yourproject.supabase.co)
   - Supabase anon/public key
   - Supabase service role key

2. **Switch to Remote Environment**
   ```powershell
   npm run supabase:use-remote
   ```

3. **Build with Remote Configuration**
   ```powershell
   npm run build
   ```

4. **Switch Back to Local Environment**
   ```powershell
   npm run supabase:use-local
   ```

### Unix/Mac (Bash/Terminal)

1. **Connect to Remote Supabase**
   ```bash
   npm run supabase:connect-remote:unix
   ```

2. **Switch to Remote Environment**
   ```bash
   npm run supabase:use-remote:unix
   ```

3. **Build with Remote Configuration**
   ```bash
   npm run build
   ```

4. **Switch Back to Local Environment**
   ```bash
   npm run supabase:use-local:unix
   ```

## Environment Files

- `.env.local`: Contains local Supabase connection details
- `.env.production`: Contains remote Supabase connection details
- `.env`: Active environment file used by the application

## Troubleshooting

1. **Authentication Issues**
   - Verify your API keys in the Supabase dashboard
   - Ensure you're using the correct project URL

2. **Connection Problems**
   - Check network connectivity to Supabase servers
   - Verify that your IP is allowed in Supabase's network restrictions

3. **Build Errors**
   - Make sure environment variables are properly set
   - Check for any regional restrictions on API access

## Security Notes

- **Never commit `.env.production` to version control**
- Keep your service role key secure
- Use Row Level Security (RLS) policies to protect data
