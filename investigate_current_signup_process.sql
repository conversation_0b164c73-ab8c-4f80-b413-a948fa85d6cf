-- Investigation script to understand what's currently happening during signup
-- Run this to see the current state of triggers and what gets created

-- 1. Check what the current handle_new_user function does
SELECT 
    'handle_new_user function:' as info,
    pg_get_functiondef(p.oid) as current_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND p.proname = 'handle_new_user';

-- 2. Check all active triggers on auth.users
SELECT 
    'Active triggers on auth.users:' as info,
    t.tgname as trigger_name,
    CASE 
      WHEN t.tgenabled = 'O' THEN 'ENABLED'
      WHEN t.tgenabled = 'D' THEN 'DISABLED'
      ELSE 'OTHER'
    END as status,
    p.proname as function_name
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'auth' 
AND c.relname = 'users'
AND t.tgname NOT LIKE 'RI_ConstraintTrigger%'
ORDER BY t.tgname;

-- 3. Check if notification_preferences table exists and its structure
SELECT 
    'notification_preferences table structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'notification_preferences'
ORDER BY ordinal_position;

-- 4. Check if there are any triggers on profiles table that might create related records
SELECT 
    'Triggers on profiles table:' as info,
    t.tgname as trigger_name,
    CASE 
      WHEN t.tgenabled = 'O' THEN 'ENABLED'
      WHEN t.tgenabled = 'D' THEN 'DISABLED'
      ELSE 'OTHER'
    END as status,
    p.proname as function_name
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public' 
AND c.relname = 'profiles'
ORDER BY t.tgname;

-- 5. Check what functions exist that might create user_consent_settings
SELECT 
    'Functions that might create user_consent_settings:' as info,
    p.proname as function_name,
    CASE 
      WHEN pg_get_functiondef(p.oid) ILIKE '%user_consent_settings%' THEN 'REFERENCES_CONSENT_SETTINGS'
      ELSE 'NO_REFERENCE'
    END as references_consent
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND (
  p.proname ILIKE '%consent%' OR 
  p.proname ILIKE '%user%' OR
  pg_get_functiondef(p.oid) ILIKE '%user_consent_settings%'
);

-- 6. Test what happens when we look at a recent profile
SELECT 
    'Recent profiles (last 5):' as info,
    id,
    first_name,
    last_name,
    email,
    created_at
FROM public.profiles
ORDER BY created_at DESC
LIMIT 5;

-- 7. Check corresponding user_consent_settings
SELECT 
    'Recent user_consent_settings (last 5):' as info,
    user_id,
    profile_visibility,
    email_notifications,
    created_at
FROM public.user_consent_settings
ORDER BY created_at DESC
LIMIT 5;
