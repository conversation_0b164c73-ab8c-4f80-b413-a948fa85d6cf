-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Service role can do anything with business industries" ON public.business_relevant_industries;

-- Create policy for service role
CREATE POLICY "Service role can do anything with business industries"
    ON public.business_relevant_industries
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Grant permissions to service role
GRANT ALL ON public.business_relevant_industries TO service_role;
GRANT USAGE ON SCHEMA public TO service_role;

-- Ensure sequences are accessible
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- Verify foreign key relationships are properly set up
DO $$
BEGIN
    -- Check if the foreign key to businesses exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc 
        JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
        WHERE tc.table_name = 'business_relevant_industries' 
        AND tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'businesses'
    ) THEN
        RAISE NOTICE 'Foreign key to businesses table is missing';
    END IF;

    -- Check if the foreign key to industries exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc 
        JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
        WHERE tc.table_name = 'business_relevant_industries' 
        AND tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'industries'
    ) THEN
        RAISE NOTICE 'Foreign key to industries table is missing';
    END IF;
END
$$;
