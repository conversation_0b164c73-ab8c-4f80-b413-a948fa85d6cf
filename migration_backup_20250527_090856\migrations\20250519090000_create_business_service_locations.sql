-- Create business_service_locations junction table
CREATE TABLE IF NOT EXISTS public.business_service_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    business_id UUID REFERENCES public.businesses(id) ON DELETE CASCADE,
    location_id UUID REFERENCES public.locations(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(business_id, location_id)
);

-- Add RLS policies
ALTER TABLE public.business_service_locations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all users" ON public.business_service_locations
    FOR SELECT
    USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.business_service_locations
    FOR INSERT
    TO authenticated
    WITH CHECK (business_id IN (
        SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    ));

CREATE POLICY "Enable update for business owners" ON public.business_service_locations
    FOR UPDATE
    TO authenticated
    USING (business_id IN (
        SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    ))
    WITH CHECK (business_id IN (
        SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    ));

CREATE POLICY "Enable delete for business owners" ON public.business_service_locations
    FOR DELETE
    TO authenticated
    USING (business_id IN (
        SELECT id FROM public.businesses WHERE owner_id = auth.uid()
    ));

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS business_service_locations_business_id_idx ON public.business_service_locations(business_id);
CREATE INDEX IF NOT EXISTS business_service_locations_location_id_idx ON public.business_service_locations(location_id);
