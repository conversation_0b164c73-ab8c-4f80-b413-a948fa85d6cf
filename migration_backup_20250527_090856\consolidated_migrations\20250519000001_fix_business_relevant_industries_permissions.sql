-- Grant permissions to the service role
CREATE POLICY "Service role can do anything with business relevant industries"
    ON public.business_relevant_industries
    FOR ALL 
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Grant all privileges on the table to the authenticated role
GRANT ALL ON public.business_relevant_industries TO authenticated;

-- Grant all privileges on the table to the service role
GRANT ALL ON public.business_relevant_industries TO service_role;

-- Grant usage on the sequence to both roles if it exists
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;
