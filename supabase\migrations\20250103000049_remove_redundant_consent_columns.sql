-- Remove redundant consent columns from user_consent_settings
-- The gdpr_consent column in event_signups is the proper way to handle email consent
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: BACKUP DATA FOR AUDIT PURPOSES
-- ================================================================

-- Create a backup table to track what consent settings existed before removal
CREATE TABLE IF NOT EXISTS public.removed_consent_settings_backup AS
SELECT 
    user_id,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    updated_at as consent_last_updated,
    NOW() as backup_created_at,
    'Removed due to redundancy with event_signups.gdpr_consent' as removal_reason
FROM public.user_consent_settings
WHERE share_email_with_event_creators = true 
   OR share_email_with_attendees = true 
   OR share_contact_details = true;

-- ================================================================
-- PART 2: UPDATE FUNCTIONS TO REMOVE REFERENCES TO THESE COLUMNS
-- ================================================================

-- Drop the update_user_consent_settings function since it won't be needed
DROP FUNCTION IF EXISTS public.update_user_consent_settings(BOOLEAN, BOOLEAN, BOOLEAN);

-- Update get_event_attendee_emails to only use gdpr_consent (remove global consent fallback)
CREATE OR REPLACE FUNCTION public.get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow event creators to access emails
  IF NOT EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND creator_user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Access denied: You must be the event creator to view attendee emails';
  END IF;

  -- Return emails only for users who have given GDPR consent for this specific event
  -- This is the proper way - per-event consent, not global consent
  RETURN QUERY
  SELECT 
    es.user_id,
    p.email
  FROM 
    event_signups es
  JOIN 
    profiles p ON es.user_id = p.id
  WHERE 
    es.event_id = get_event_attendee_emails.event_id
    AND es.gdpr_consent = true;  -- Only per-event consent matters
END;
$$;

-- ================================================================
-- PART 3: UPDATE VIEWS TO REMOVE REFERENCES
-- ================================================================

-- Update any views that reference the removed columns
-- First check if events_with_creator view exists and update it
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname = 'public' AND viewname = 'events_with_creator') THEN
        -- Recreate the view without the consent check
        DROP VIEW public.events_with_creator;
        CREATE VIEW public.events_with_creator AS
        SELECT
            e.*,
            p.first_name AS creator_first_name,
            p.last_name AS creator_last_name,
            p.avatar_url AS creator_avatar_url,
            p.organization AS creator_organization,
            -- Remove email visibility based on global consent
            -- Event creators' emails should be visible through other means if needed
            NULL AS creator_email
        FROM events e
        LEFT JOIN profiles p ON e.creator_user_id = p.id;
        
        RAISE NOTICE 'Updated events_with_creator view to remove consent-based email visibility';
    END IF;
END $$;

-- Update events_with_search view if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_views WHERE schemaname = 'public' AND viewname = 'events_with_search') THEN
        -- Get the current view definition and update it
        DROP VIEW public.events_with_search;
        CREATE VIEW public.events_with_search AS 
        SELECT 
            e.id,
            e.title,
            e.description,
            e.start_date,
            e.physical_location,
            e.meeting_url,
            e.event_type,
            e.industry_ids,
            e.netzero_category_ids,
            e.creator_user_id,
            p.first_name AS creator_first_name,
            p.last_name AS creator_last_name,
            p.organization AS creator_organization,
            -- Remove consent-based email visibility
            NULL AS creator_email,
            (
                SELECT array_agg(c.name)
                FROM unnest(e.netzero_category_ids) category_id(category_id)
                JOIN netzero_categories c ON c.id = category_id.category_id::uuid
            ) AS category_names,
            to_tsvector('english', e.title || ' ' || e.description) AS search_vector
        FROM events e
        LEFT JOIN profiles p ON e.creator_user_id = p.id;
        
        RAISE NOTICE 'Updated events_with_search view to remove consent-based email visibility';
    END IF;
END $$;

-- ================================================================
-- PART 4: DROP DEPENDENT VIEWS BEFORE REMOVING COLUMNS
-- ================================================================

-- Drop views that depend on the columns we're about to remove
DROP VIEW IF EXISTS public.public_professional_profiles CASCADE;
DROP VIEW IF EXISTS public.user_privacy_settings_summary CASCADE;
DROP VIEW IF EXISTS public.notification_system_health CASCADE;
DROP VIEW IF EXISTS public.detailed_consent_breakdown CASCADE;
DROP VIEW IF EXISTS public.consent_audit_flags CASCADE;

-- ================================================================
-- PART 5: REMOVE THE REDUNDANT COLUMNS
-- ================================================================

-- Remove the redundant consent columns
ALTER TABLE public.user_consent_settings
DROP COLUMN IF EXISTS share_email_with_event_creators;

ALTER TABLE public.user_consent_settings
DROP COLUMN IF EXISTS share_email_with_attendees;

ALTER TABLE public.user_consent_settings
DROP COLUMN IF EXISTS share_contact_details;

-- ================================================================
-- PART 6: UPDATE FRONTEND-RELATED FUNCTIONS
-- ================================================================

-- Drop existing function first to avoid return type conflicts
DROP FUNCTION IF EXISTS public.get_user_consent_settings();

-- Create a simple function to get remaining user consent settings
CREATE OR REPLACE FUNCTION public.get_user_consent_settings()
RETURNS TABLE (
    user_id UUID,
    consent_updated_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        ucs.user_id,
        ucs.consent_updated_at,
        ucs.created_at,
        ucs.updated_at
    FROM public.user_consent_settings ucs
    WHERE ucs.user_id = auth.uid();

    -- If no record exists, return defaults
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT auth.uid(), NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ;
    END IF;
END;
$$;

-- ================================================================
-- PART 7: UPDATE DIAGNOSTIC VIEWS
-- ================================================================

-- Update the consent audit view to reflect the changes
DROP VIEW IF EXISTS public.consent_audit_flags;

-- Create a new simplified audit view
CREATE OR REPLACE VIEW public.gdpr_consent_audit AS
SELECT 
    p.id as user_id,
    p.first_name,
    p.last_name,
    p.email,
    COUNT(es.id) as total_event_signups,
    COUNT(CASE WHEN es.gdpr_consent = true THEN 1 END) as gdpr_consents_given,
    COUNT(CASE WHEN es.gdpr_consent = false THEN 1 END) as gdpr_consents_denied,
    CASE 
        WHEN COUNT(es.id) = 0 THEN 'No event signups'
        WHEN COUNT(CASE WHEN es.gdpr_consent = true THEN 1 END) = COUNT(es.id) THEN 'All consents given'
        WHEN COUNT(CASE WHEN es.gdpr_consent = false THEN 1 END) = COUNT(es.id) THEN 'All consents denied'
        ELSE 'Mixed consent status'
    END as consent_status
FROM public.profiles p
LEFT JOIN public.event_signups es ON p.id = es.user_id
GROUP BY p.id, p.first_name, p.last_name, p.email
ORDER BY total_event_signups DESC, gdpr_consents_given DESC;

-- Grant access to the audit view
GRANT SELECT ON public.gdpr_consent_audit TO authenticated;

-- Update the notification system health view
DROP VIEW IF EXISTS public.notification_system_health;

CREATE OR REPLACE VIEW public.notification_system_health AS
SELECT 
    'Notification Preferences' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN email_notifications = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN post_comments = false THEN 1 END) as post_comments_disabled_count,
    COUNT(CASE WHEN event_signups = false THEN 1 END) as event_signups_disabled_count
FROM public.notification_preferences
UNION ALL
SELECT 
    'GDPR Event Consents' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN gdpr_consent = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN gdpr_consent = false THEN 1 END) as post_comments_disabled_count,
    0 as event_signups_disabled_count
FROM public.event_signups;

-- Grant access to the updated view
GRANT SELECT ON public.notification_system_health TO authenticated;

-- Recreate user_privacy_settings_summary view without the removed columns
CREATE OR REPLACE VIEW public.user_privacy_settings_summary AS
SELECT
    p.id as profile_id,
    p.first_name,
    p.last_name,
    p.email,
    -- Consent settings (only remaining columns)
    ucs.consent_updated_at,
    ucs.created_at as consent_created_at,
    ucs.updated_at as consent_last_updated,
    -- Notification preferences
    np.email_notifications,
    np.post_likes,
    np.post_comments,
    np.comment_replies,
    np.comment_likes,
    np.connection_requests,
    np.connection_accepted,
    np.event_signups,
    np.event_updates,
    np.system_notifications,
    -- GDPR consent summary from event signups
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id) as total_event_signups,
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id AND es.gdpr_consent = true) as gdpr_consents_given
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
LEFT JOIN public.notification_preferences np ON p.id = np.profile_id;

-- Grant access to the updated view
GRANT SELECT ON public.user_privacy_settings_summary TO authenticated;

COMMIT;

-- ================================================================
-- VERIFICATION AND REPORTING
-- ================================================================

-- Report what was backed up
SELECT 'Consent settings backup summary:' as info;
SELECT 
    COUNT(*) as total_users_backed_up,
    COUNT(CASE WHEN share_email_with_event_creators = true THEN 1 END) as had_creator_consent,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as had_attendee_consent,
    COUNT(CASE WHEN share_contact_details = true THEN 1 END) as had_contact_consent
FROM public.removed_consent_settings_backup;

-- Show the new simplified consent system
SELECT 'New GDPR consent system (per-event basis):' as info;
SELECT * FROM public.gdpr_consent_audit LIMIT 5;

-- Show updated system health
SELECT 'Updated system health:' as info;
SELECT * FROM public.notification_system_health;
