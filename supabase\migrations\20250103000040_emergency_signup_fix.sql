-- Emergency fix for signup - absolute minimal approach
-- Remove all potential problem areas and get signups working
-- Date: 2025-01-03

BEGIN;

-- Create the simplest possible handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert only the absolute minimum into profiles
  INSERT INTO public.profiles (
    id,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.created_at,
    NEW.created_at
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Emergency minimal signup function - creates profile with ID only';

COMMIT;

-- Verify the function was updated
SELECT 'Emergency signup fix applied - minimal profile creation only' as status;
