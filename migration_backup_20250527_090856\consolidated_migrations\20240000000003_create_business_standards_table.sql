-- Create business_standards table
CREATE TABLE IF NOT EXISTS public.business_standards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
  standard_id UUID NOT NULL REFERENCES public.netzero_standards(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(business_id, standard_id)
);

-- Enable RLS on business_standards table
ALTER TABLE public.business_standards ENABLE ROW LEVEL SECURITY;

-- Create policy for viewing business standards
CREATE POLICY "Anyone can view business standards"
ON public.business_standards
FOR SELECT
USING (TRUE);

-- Create policy for inserting business standards
CREATE POLICY "Business owners can add business standards"
ON public.business_standards
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM public.businesses b
    WHERE b.id = business_id
    AND b.owner_id = auth.uid()
  )
);

-- Create policy for deleting business standards
CREATE POLICY "Business owners can delete their business standards"
ON public.business_standards
FOR DELETE
USING (
  EXISTS (
    SELECT 1
    FROM public.businesses b
    WHERE b.id = business_id
    AND b.owner_id = auth.uid()
  )
);
