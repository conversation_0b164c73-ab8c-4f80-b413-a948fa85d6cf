# Events Functionality Tasks

## Current Status
This document outlines the current state of the Events functionality and the tasks needed to complete it.

### Working Features
1. **Add Event Form** (`AddEventForm.tsx`)
   - Basic form for creating events is working
   - Events can be successfully saved to the database
   - Located at `/features/events/components/AddEventForm.tsx`
   - Form validation is implemented with Zod

2. **Event Viewing**
   - Events are viewable on the Event Directory page (`EventDirectory.tsx`)
   - Basic event display is functioning through `EventCard` components
   - Events can be listed with basic information

3. **Event Signups**
   - Users can sign up for events with GDPR consent
   - Users can withdraw from events they've signed up for
   - Signup status is tracked and displayed

4. **Event Details**
   - Individual event details can be viewed on a dedicated page
   - Some event information is properly displayed

### Systems in Place
1. **Data Management**
   - Core event data types are defined in `@/types/events.ts`
   - Event API functions are located in `@/lib/events.ts`
   - React hooks for event operations in `@/hooks/useEvents.ts`

2. **Database Structure**
   - Events table with core fields
   - Event signups table tracking registrations
   - Relations between users and events

### Tasks to Complete

## 1. Image Upload Integration
- [ ] Add Cloudflare image upload capability to the Event Form
  - Integrate with existing `ImageUploader.tsx` component
  - Update `AddEventForm.tsx` to use the Cloudflare uploader
  - Store image URLs in the events table
- [ ] Follow the same approach used elsewhere in the project
  - See `ImageUpload.tsx` for reference implementation
- [ ] Make sure images display correctly in event cards and details
  - Update `EventCard.tsx` to properly display uploaded images
  - Ensure responsive image handling in `EventDetails.tsx`

## 2. Event Directory & Filtering
- [ ] Fix filters in `EventDirectory.tsx`
  - Implement filter state management
  - Ensure filters are correctly applied to the Supabase queries
- [ ] Enhance search functionality
  - Add debouncing for search input
  - Search across title, description, and tags
- [ ] Fix filter UI components
  - Ensure date range filters work correctly
  - Add category and type filter dropdowns
- [ ] Store user filter preferences (optional)

## 3. Category Tagging
- [ ] Integrate NetZeroCategoriesAccordion into Event Form
  - Import and integrate the component from elsewhere in the project
  - Add to `AddEventForm.tsx`
- [ ] Integrate IndustriesAccordion into Event Form
  - Import and integrate the component
  - Update form validation schema
- [ ] Update database tables
  - Add category/industry columns to events table or create junction tables
  - Update Supabase queries in `events.ts`
- [ ] Update event display components to show categories/industries

## 4. Event Creator Dashboard
- [x] Complete `EventCreatorDashboard.tsx` (Removed as redundant)

## 5. Event Details Page
- [ ] Improve styling of `EventDetails.tsx`
  - Add consistent card layout
  - Format dates and times properly
- [x] Add navigation bar
  - Create breadcrumb navigation
  - Add back button to event directory
- [ ] Fix event details display
  - Ensure all event information is properly formatted
  - Add tags, categories display

## 6. iCal Integration
- [ ] Fix the `generateICSFile` function in `events.ts`
  - Ensure it creates valid iCalendar file format
  - Include all relevant event details
- [ ] Implement download functionality
  - Add download button to `EventDetails.tsx`
  - Correctly set file MIME type and filename
- [ ] Test generated files
  - Test with various calendar applications (Google Calendar, Outlook, etc.)

## Code Architecture Improvements
- [ ] Refactor `useEvents.ts` hook
  - Improve error handling
  - Add proper loading states
- [ ] Optimize Supabase queries in `events.ts`
  - Reduce duplicate queries
  - Implement pagination for large result sets
- [ ] Ensure consistent component structure
  - Make component props more consistent
  - Improve type definitions

## Schedule & Priorities
1. **High Priority**
   - Fix event signup display and functionality
   - Complete event details page with navigation
   - Fix filters on Event Directory
   - Add event management on User Dashboard

2. **Medium Priority**
   - Implement Cloudflare image upload
   - Add category tagging integration
   - Fix iCal functionality

3. **Lower Priority**
   - Refactor code architecture
   - Add additional user experience improvements
   - Advanced filtering options

## Implementation Details

### Current Database Structure
- **events table** - Core event information
- **event_signups table** - Tracks user registrations for events
- **events_with_creator view** - SQL view that joins events with creator information

### Current API Functions
| Function | Purpose | Status |
|----------|---------|--------|
| `fetchEvents` | Get list of events with optional filtering | ✅ Working but filters need fixes |
| `fetchEventById` | Get a single event with creator info | ✅ Working |
| `createEvent` | Create a new event | ✅ Working |
| `updateEvent` | Update an existing event | ✅ Working |
| `deleteEvent` | Delete an event | ✅ Working |
| `signUpForEvent` | Register a user for an event | ✅ Working |
| `withdrawEventSignup` | Withdraw a user's registration | ✅ Working |
| `fetchEventSignups` | Get list of users signed up for an event | ⚠️ Needs optimization |
| `checkUserSignedUp` | Check if current user is registered | ✅ Working |
| `generateICSFile` | Generate iCalendar file | ⚠️ Needs testing |

### Known Issues
1. **Event Directory Filtering**
   - Filters may not be correctly applied to the Supabase queries
   - UI components don't properly reflect filter state

2. **Event Signups Display**
   - Multiple API calls for each signup (inefficient)
   - Need to implement a more efficient join query

3. **Image Upload**
   - Currently missing Cloudflare integration
   - Event images fallback to placeholders

4. **Categories and Industries**
   - Currently using simple string tags instead of structured categories

5. **Event Creator Dashboard**
   - Incomplete implementation
   - Missing participant management features

## Resources
- Current event API functions: `src/lib/events.ts`
- Event types: `src/types/events.ts`
- Event hooks: `src/hooks/useEvents.ts`
- Main components:
  - `src/features/events/components/AddEventForm.tsx`
  - `src/features/events/components/EventDirectory.tsx`
  - `src/features/events/pages/EventDetails.tsx`
  - `src/features/events/components/EventCard.tsx`

## Next Steps
1. Address the inefficient query in `fetchEventSignups` - highest priority
2. Fix filtering on the Event Directory page
3. Implement Cloudflare image uploading
4. Complete the Event Details styling and navigation
