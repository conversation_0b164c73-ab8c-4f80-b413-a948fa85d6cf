-- Debug members visibility and email_notifications column issues
-- Check current state of user_consent_settings and profile visibility

-- 1. Check if email_notifications column still exists
SELECT 
    'email_notifications column check:' as info,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'user_consent_settings' 
            AND column_name = 'email_notifications'
            AND table_schema = 'public'
        ) THEN 'COLUMN_EXISTS'
        ELSE 'COLUMN_REMOVED'
    END as column_status;

-- 2. Check current user_consent_settings table structure
SELECT 
    'user_consent_settings table structure:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'user_consent_settings'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check profile_visibility settings for all users
SELECT 
    'Profile visibility breakdown:' as info,
    COUNT(*) as total_users,
    COUNT(CASE WHEN profile_visibility = true THEN 1 END) as profile_visible_true,
    COUNT(CASE WHEN profile_visibility = false THEN 1 END) as profile_visible_false,
    COUNT(CASE WHEN profile_visibility IS NULL THEN 1 END) as profile_visible_null
FROM public.user_consent_settings;

-- 4. Check if there are profiles without consent settings
SELECT 
    'Profiles without consent settings:' as info,
    COUNT(*) as profiles_without_consent
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
WHERE ucs.user_id IS NULL;

-- 5. Show actual profile visibility data for debugging
SELECT 
    'Sample profile visibility data:' as info,
    p.id as profile_id,
    p.first_name,
    p.last_name,
    p.email,
    ucs.profile_visibility,
    ucs.created_at as consent_created,
    ucs.updated_at as consent_updated
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
ORDER BY p.created_at DESC
LIMIT 10;

-- 6. Check if there are any views or functions that filter members by profile_visibility
SELECT 
    'Views that might filter by profile_visibility:' as info,
    viewname,
    definition
FROM pg_views
WHERE schemaname = 'public'
AND definition ILIKE '%profile_visibility%';

-- 7. Check functions that might filter by profile_visibility
SELECT 
    'Functions that might filter by profile_visibility:' as info,
    p.proname as function_name,
    pg_get_function_arguments(p.oid) as arguments
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND pg_get_functiondef(p.oid) ILIKE '%profile_visibility%';

-- 8. Check if there's a members or professionals view
SELECT 
    'Members/professionals related views:' as info,
    viewname,
    CASE 
        WHEN definition ILIKE '%profile_visibility%' THEN 'USES_PROFILE_VISIBILITY'
        ELSE 'NO_PROFILE_VISIBILITY_FILTER'
    END as visibility_filter_status
FROM pg_views
WHERE schemaname = 'public'
AND (
    viewname ILIKE '%member%' 
    OR viewname ILIKE '%professional%'
    OR viewname ILIKE '%public%'
    OR viewname ILIKE '%directory%'
);

-- 9. Check the profiles table structure to see if there's a visibility column there too
SELECT 
    'profiles table visibility columns:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'profiles'
AND table_schema = 'public'
AND column_name ILIKE '%visib%'
ORDER BY ordinal_position;

-- 10. Check if social_visibility in profiles is being used instead
SELECT 
    'Social visibility in profiles:' as info,
    social_visibility,
    COUNT(*) as user_count
FROM public.profiles
GROUP BY social_visibility
ORDER BY user_count DESC;
