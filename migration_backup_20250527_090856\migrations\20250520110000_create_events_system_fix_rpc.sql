-- Create an RPC function to run the events system fix
CREATE OR REPLACE FUNCTION public.run_events_system_fix()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = pg_catalog, pg_temp
AS $$
BEGIN
  -- Check and create events table if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM pg_catalog.pg_tables
    WHERE schemaname = 'public' AND tablename = 'events'
  ) THEN
    -- Create events table
    CREATE TABLE "public"."events" (
      "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
      "creator_user_id" uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      "title" text NOT NULL,
      "start_date" date NOT NULL,
      "start_time" time WITHOUT TIME ZONE NOT NULL,
      "end_date" date,
      "end_time" time WITHOUT TIME ZONE,
      "description" text,
      "image_url" text,
      "event_type" text NOT NULL CHECK (event_type IN ('in-person', 'remote', 'hybrid')),
      "physical_location" text,
      "meeting_url" text,
      "event_category" text NOT NULL,
      "tags" text[] DEFAULT '{}',
      "netzero_category_ids" uuid[] DEFAULT '{}',
      "industry_ids" uuid[] DEFAULT '{}',
      "created_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      "updated_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      PRIMARY KEY (id)
    );

    -- Enable Row Level Security
    ALTER TABLE "public"."events" ENABLE ROW LEVEL SECURITY;

    -- Create policies
    -- Everyone can view events
    CREATE POLICY "Everyone can view events" ON "public"."events"
    FOR SELECT USING (true);

    -- Users can create their own events
    CREATE POLICY "Users can create their own events" ON "public"."events"
    FOR INSERT WITH CHECK (auth.uid() = creator_user_id);

    -- Users can update their own events
    CREATE POLICY "Users can update their own events" ON "public"."events"
    FOR UPDATE USING (auth.uid() = creator_user_id);

    -- Users can delete their own events
    CREATE POLICY "Users can delete their own events" ON "public"."events"
    FOR DELETE USING (auth.uid() = creator_user_id);

    -- Create an index for faster event lookups by creator
    CREATE INDEX "events_creator_user_id_idx" ON "public"."events" (creator_user_id);

    -- Create an index for date-based querying
    CREATE INDEX "events_start_date_idx" ON "public"."events" (start_date);
  END IF;

  -- Check and create event_signups table if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM pg_catalog.pg_tables
    WHERE schemaname = 'public' AND tablename = 'event_signups'
  ) THEN
    -- Create event signups table
    CREATE TABLE "public"."event_signups" (
      "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
      "event_id" uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
      "user_id" uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      "gdpr_consent" boolean NOT NULL DEFAULT false,
      "hide_attendance" boolean NOT NULL DEFAULT false,
      "created_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      "updated_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      PRIMARY KEY (id),
      -- Prevent duplicate signups
      UNIQUE (event_id, user_id)
    );

    -- Enable Row Level Security
    ALTER TABLE "public"."event_signups" ENABLE ROW LEVEL SECURITY;

    -- Create policies
    -- Everyone can view event signups
    CREATE POLICY "Everyone can view event signups" ON "public"."event_signups"
    FOR SELECT USING (true);

    -- Users can sign up for events
    CREATE POLICY "Users can sign up for events" ON "public"."event_signups"
    FOR INSERT WITH CHECK (auth.uid() = user_id);

    -- Users can update their own signups
    CREATE POLICY "Users can update their own signups" ON "public"."event_signups"
    FOR UPDATE USING (auth.uid() = user_id);

    -- Users can delete their own signups
    CREATE POLICY "Users can delete their own signups" ON "public"."event_signups"
    FOR DELETE USING (auth.uid() = user_id);
    
    -- Add explicit comment for better joins and relations
    COMMENT ON COLUMN public.event_signups.user_id IS 'References auth.users(id)';
  END IF;

  -- Create or replace the views
  DROP VIEW IF EXISTS public.events_with_creator;
  CREATE VIEW public.events_with_creator AS
  SELECT 
    e.*,
    au.email as creator_email,
    CONCAT(p.first_name, ' ', p.last_name) as creator_name,
    p.avatar_url as creator_avatar_url
  FROM 
    public.events e
  JOIN 
    auth.users au ON e.creator_user_id = au.id
  LEFT JOIN 
    public.profiles p ON e.creator_user_id = p.id;

  -- Grant appropriate permissions on the view
  ALTER VIEW public.events_with_creator OWNER TO postgres;
  GRANT SELECT ON public.events_with_creator TO authenticated;
  GRANT SELECT ON public.events_with_creator TO service_role;
  
  DROP VIEW IF EXISTS public.event_signups_with_users;
  CREATE VIEW public.event_signups_with_users AS
  SELECT 
    es.*,
    au.email,
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    p.avatar_url
  FROM 
    public.event_signups es
  JOIN 
    auth.users au ON es.user_id = au.id
  LEFT JOIN 
    public.profiles p ON es.user_id = p.id;

  -- Grant appropriate permissions on the view
  ALTER VIEW public.event_signups_with_users OWNER TO postgres;
  GRANT SELECT ON public.event_signups_with_users TO authenticated;
  GRANT SELECT ON public.event_signups_with_users TO service_role;

  -- Successfully ran all fixes
  RETURN TRUE;
END;
$$;

-- Grant permissions on the function
GRANT EXECUTE ON FUNCTION public.run_events_system_fix() TO authenticated;
