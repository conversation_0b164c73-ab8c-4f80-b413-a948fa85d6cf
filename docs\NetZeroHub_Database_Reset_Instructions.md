# NetZeroHub Database Reset Instructions

This document provides instructions for resetting your NetZeroHub database to resolve authentication and other database-related issues.

## Prerequisites

1. Access to your Supabase project dashboard
2. Administrative access to run SQL queries
3. Backup of any critical data (optional, if you want to preserve existing data)

## Steps to Reset and Restore Database

### 1. Backup Your Data (Optional)

If you have important data that you want to preserve:

```sql
-- Create a backup of important tables (run in Supabase SQL Editor)
CREATE TABLE profiles_backup AS SELECT * FROM public.profiles;
CREATE TABLE businesses_backup AS SELECT * FROM public.businesses;
-- Add more tables as needed
```

### 2. Reset Your Database

There are two approaches:

#### Option A: Use Supabase Dashboard (Recommended for complete reset)

1. Go to your Supabase project dashboard
2. Navigate to Project Settings
3. Select "Database"
4. Find the "Database Reset" option
5. Follow the prompts to reset your database

#### Option B: Drop and Recreate Tables

If you prefer a controlled reset without affecting other parts of your database:

```sql
-- Drop existing tables (run in Supabase SQL Editor)
DROP TABLE IF EXISTS 
  public.notifications, 
  public.event_signups, 
  public.events, 
  public.comment_likes,
  public.post_comments,
  public.post_likes, 
  public.post_categories,
  public.social_posts, 
  public.user_connections,
  public.business_standards,
  public.businesses,
  public.profiles 
CASCADE;

-- Drop existing views
DROP VIEW IF EXISTS 
  public.featured_businesses,
  public.events_with_creator,
  public.event_signups_with_user_info 
CASCADE;

-- Remove existing triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_post_likes_count_trigger ON public.post_likes;
DROP TRIGGER IF EXISTS update_comment_likes_count_trigger ON public.comment_likes;

-- Drop existing functions
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.update_post_likes_count();
DROP FUNCTION IF EXISTS public.update_comment_likes_count();
```

### 3. Apply the Complete Schema

1. Go to the Supabase SQL Editor
2. Open the file `NetZeroHub_Complete_Schema.sql`
3. Run the entire script

### 4. Verify Installation

After applying the schema, verify that:

1. Basic tables are created:
   ```sql
   SELECT * FROM public.profiles LIMIT 1;
   ```

2. The trigger for new user creation works:
   ```sql
   SELECT * FROM pg_trigger WHERE tgname = 'on_auth_user_created';
   ```

### 5. Test User Registration

1. Try registering a new test user through your application
2. Verify that a profile gets created automatically in the `profiles` table

### Troubleshooting

If you encounter errors when running the schema:

1. **Trigger or policy already exists errors**: The schema should handle this automatically by dropping existing objects before creating new ones. If you still see these errors, you can manually drop the conflicting object:
   ```sql
   -- For trigger conflicts
   DROP TRIGGER IF EXISTS trigger_name ON table_name;
   
   -- For policy conflicts
   DROP POLICY IF EXISTS "Policy Name" ON table_name;
   
   -- For function conflicts
   DROP FUNCTION IF EXISTS function_name() CASCADE;
   ```

2. **Check Supabase logs** for detailed error messages

3. **Verify that the `handle_new_user` function** and its trigger are properly created:
   ```sql
   SELECT * FROM pg_trigger WHERE tgname = 'on_auth_user_created';
   ```

4. **Ensure your application's authentication code** is correctly configured

5. **Check that your Supabase URL and API key** are correctly set in your application

## Understanding the Schema

The NetZeroHub schema includes:

- **User Management**: profiles linked to Supabase auth
- **Business Profiles**: businesses and their certifications
- **Social Media**: posts, comments, likes, and connections
- **Events**: event management and user signups
- **Notifications**: user notification system

Each component has appropriate indexes, RLS policies, and triggers to ensure data integrity and security.

## Next Steps

After resetting your database:

1. Set up initial categories and standard reference data
2. Create admin user(s)
3. Test core functionality: registration, business creation, social posting, and events

For assistance with database operations, contact your database administrator or review the Supabase documentation.
