-- First drop the existing view
DROP VIEW IF EXISTS public.event_signups_with_users;

-- Then recreate the view with the new column
CREATE VIEW public.event_signups_with_users AS
SELECT 
    es.id,
    es.event_id,
    es.user_id,
    es.gdpr_consent,
    es.created_at,
    es.updated_at,
    es.hide_attendance,
    COALESCE(p.first_name || ' ' || p.last_name, 'Anonymous User') as full_name,
    p.avatar_url,
    au.email,
    p.title,
    p.organization
FROM 
    public.event_signups es
    LEFT JOIN public.profiles p ON es.user_id = p.id
    LEFT JOIN auth.users au ON es.user_id = au.id;

-- Add comment to explain the view
COMMENT ON VIEW public.event_signups_with_users IS 'View that joins event_signups with user profile data and includes hide_attendance flag';
