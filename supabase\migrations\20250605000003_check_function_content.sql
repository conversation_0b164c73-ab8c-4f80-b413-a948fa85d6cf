-- Check the exact content of handle_new_user function
-- This will help us see if it's still referencing push_notifications
-- Date: 2025-06-05

-- Get the complete function definition
SELECT 
    routine_definition
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- Also check if the function contains references to push_notifications
SELECT 
    'function_analysis' as check_type,
    CASE 
        WHEN routine_definition LIKE '%push_notifications%' THEN 'CONTAINS push_notifications - NEEDS UPDATE'
        ELSE 'NO push_notifications reference - GOOD'
    END as analysis,
    CASE 
        WHEN routine_definition LIKE '%notification_preferences%' THEN 'Creates notification preferences'
        ELSE 'Does NOT create notification preferences'
    END as creates_preferences
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';
