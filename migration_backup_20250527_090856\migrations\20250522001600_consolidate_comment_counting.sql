-- Migration to fix the double-counting issue in comments
-- This migration consolidates all comment counting logic into a single approach

-- First, drop ALL existing comment-related functions and triggers
DROP TRIGGER IF EXISTS update_post_comments_count_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS update_post_comments_count_insert_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS update_post_comments_count_delete_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS on_comment_added_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS on_comment_deleted_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS handle_comment_count_trigger ON public.post_comments;

-- Drop all existing comment count functions
DROP FUNCTION IF EXISTS public.update_post_comments_count();
DROP FUNCTION IF EXISTS public.on_comment_added();
DROP FUNCTION IF EXISTS public.on_comment_deleted();
DROP FUNCTION IF EXISTS public.handle_comment_count();
DROP FUNCTION IF EXISTS public.increment_post_comment_count(UUID);
DROP FUNCTION IF EXISTS public.decrement_post_comment_count(UUID);

-- Create a new single function to handle comment counting
CREATE OR REPLACE FUNCTION public.handle_comment_count() 
RETURNS TRIGGER AS $$
DECLARE 
    post_id_var UUID;
    current_count INTEGER;
BEGIN
    -- Get the relevant post_id
    post_id_var := CASE TG_OP
        WHEN 'INSERT' THEN NEW.post_id
        WHEN 'DELETE' THEN OLD.post_id
    END;

    -- Get current comment count for this post
    SELECT COUNT(*) 
    INTO current_count
    FROM post_comments 
    WHERE post_id = post_id_var;

    -- If this is an INSERT, we need to subtract 1 from the count
    -- because the trigger runs AFTER INSERT, so our count includes the new comment
    IF TG_OP = 'INSERT' THEN
        current_count := current_count - 1;
    END IF;

    -- Update the post with the new count
    UPDATE social_posts
    SET 
        comments_count = CASE TG_OP
            WHEN 'INSERT' THEN current_count + 1
            WHEN 'DELETE' THEN GREATEST(current_count, 0)
        END,
        engagement_score = (
            COALESCE(likes_count, 0) * 1.0 + 
            CASE TG_OP
                WHEN 'INSERT' THEN (current_count + 1)
                WHEN 'DELETE' THEN GREATEST(current_count, 0)
            END * 2.0
        )
    WHERE id = post_id_var;

    -- Log for debugging
    RAISE NOTICE 'Comment count update: post_id=%, op=%, old_count=%, new_count=%', 
        post_id_var, 
        TG_OP, 
        current_count, 
        CASE TG_OP
            WHEN 'INSERT' THEN current_count + 1
            WHEN 'DELETE' THEN GREATEST(current_count, 0)
        END;

    RETURN CASE TG_OP
        WHEN 'INSERT' THEN NEW
        WHEN 'DELETE' THEN OLD
    END;
END;
$$ LANGUAGE plpgsql;

-- Create a single trigger for both INSERT and DELETE
CREATE TRIGGER handle_comment_count_trigger
AFTER INSERT OR DELETE ON public.post_comments
FOR EACH ROW
EXECUTE FUNCTION public.handle_comment_count();

-- Fix any existing incorrect counts
UPDATE social_posts sp
SET 
    comments_count = COALESCE((
        SELECT COUNT(*)
        FROM post_comments pc
        WHERE pc.post_id = sp.id
    ), 0),
    engagement_score = (
        COALESCE(likes_count, 0) * 1.0 + 
        COALESCE((
            SELECT COUNT(*)
            FROM post_comments pc
            WHERE pc.post_id = sp.id
        ), 0) * 2.0
    );

-- Make sure comments_count column has proper constraints
ALTER TABLE social_posts 
    ALTER COLUMN comments_count SET DEFAULT 0,
    DROP CONSTRAINT IF EXISTS comments_count_non_negative,
    ADD CONSTRAINT comments_count_non_negative CHECK (comments_count >= 0);
