-- Modify the user connections RLS policy to explicitly allow users to update connections where they are the recipient
-- First drop the existing policy
DROP POLICY IF EXISTS "Users can manage their connections" ON user_connections;

-- Create a more specific policy
CREATE POLICY "Users can manage their own connections" 
ON user_connections
FOR ALL
TO authenticated
USING (
    -- User is either the sender or recipient of the connection
    (auth.uid() = user_id OR auth.uid() = connection_id)
)
WITH CHECK (
    -- For inserts/updates, user must be either the sender or recipient
    (auth.uid() = user_id OR auth.uid() = connection_id)
);

-- Grant the update permission to the RPC function
GRANT UPDATE ON user_connections TO service_role;
GRANT EXECUTE ON FUNCTION update_connection_status TO authenticated;
