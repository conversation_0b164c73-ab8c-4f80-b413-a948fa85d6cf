import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { UpgradeButton } from '@/components/billing/UpgradeButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const TestStripePage: React.FC = () => {
  const { user, profile } = useAuth();

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Please sign in to test Stripe integration</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Stripe Integration Test</h1>
          <p className="text-gray-600 mt-2">Test the subscription upgrade flow</p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle>Current Status</CardTitle>
              <CardDescription>Your current subscription details</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>User:</strong> {user.email}</p>
                <p><strong>Current Tier:</strong> {profile?.subscription_tier || 'seed'}</p>
                <p><strong>Status:</strong> {profile?.subscription_status || 'active'}</p>
                <p><strong>Customer ID:</strong> {profile?.customer_id || 'Not set'}</p>
              </div>
            </CardContent>
          </Card>

          {/* Upgrade Options */}
          <Card>
            <CardHeader>
              <CardTitle>Upgrade Options</CardTitle>
              <CardDescription>Test the Stripe checkout flow</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Sapling Plan - £5.99/month</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Unlimited posts (images + videos)</li>
                  <li>• 5 business/job/event posts per month</li>
                  <li>• 10 connections per week</li>
                  <li>• Priority support</li>
                </ul>
                <UpgradeButton 
                  tier="sapling" 
                  currentTier={profile?.subscription_tier || 'seed'}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold">Woodland Plan - £10.99/month</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Everything in Sapling</li>
                  <li>• Unlimited business/job/event posts</li>
                  <li>• Unlimited connections</li>
                  <li>• Multiple business listings</li>
                </ul>
                <UpgradeButton 
                  tier="woodland" 
                  currentTier={profile?.subscription_tier || 'seed'}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Test Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Test Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>Test Card Numbers:</strong></p>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li><strong>Success:</strong> 4242 4242 4242 4242</li>
                <li><strong>Decline:</strong> 4000 0000 0000 0002</li>
                <li><strong>Requires Authentication:</strong> 4000 0025 0000 3155</li>
              </ul>
              <p className="mt-4"><strong>Use any future date for expiry and any 3-digit CVC.</strong></p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestStripePage;
