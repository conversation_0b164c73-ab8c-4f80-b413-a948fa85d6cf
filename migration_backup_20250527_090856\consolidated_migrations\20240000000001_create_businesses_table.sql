-- Create businesses table
CREATE TABLE IF NOT EXISTS public.businesses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT,
  banner_url TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT,
  website TEXT,
  phone TEXT,
  email TEXT,
  industry TEXT,
  employee_count INTEGER,
  founded_year INTEGER,
  owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  tlr_score FLOAT,
  verified_tlr BOOLEAN DEFAULT false,
  is_verified BOOLEAN DEFAULT false,
  verification_date TIMESTAMP WITH TIME ZONE,
  verification_document_url TEXT,
  sponsorship_tier TEXT CHECK (sponsorship_tier IN ('none', 'bronze', 'silver', 'gold')),
  sponsorship_start_date TIMESTAMP WITH TIME ZONE,
  sponsorship_end_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS on businesses table
ALTER TABLE public.businesses ENABLE ROW LEVEL SECURITY;

-- Create policy for viewing businesses
CREATE POLICY "Anyone can view businesses"
ON public.businesses
FOR SELECT
USING (TRUE);

-- Create policy for updating own businesses
CREATE POLICY "Users can update their own businesses"
ON public.businesses
FOR UPDATE
USING (auth.uid() = owner_id)
WITH CHECK (auth.uid() = owner_id);

-- Create policy for deleting own businesses
CREATE POLICY "Users can delete their own businesses"
ON public.businesses
FOR DELETE
USING (auth.uid() = owner_id);
