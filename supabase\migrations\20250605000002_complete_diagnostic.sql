-- Complete diagnostic to understand current database state
-- Run each section separately to see detailed results
-- Date: 2025-06-05

-- 1. Check if notification_preferences table exists
SELECT 
    'table_exists' as check_type,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_name = 'notification_preferences' 
AND table_schema = 'public';

-- 2. Get complete table structure
SELECT 
    'column_info' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'notification_preferences' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check specifically for push_notifications column
SELECT 
    'push_notifications_check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'notification_preferences' 
            AND column_name = 'push_notifications'
            AND table_schema = 'public'
        ) THEN 'EXISTS - NEEDS REMOVAL'
        ELSE 'NOT EXISTS - GOOD'
    END as status;

-- 4. Check current handle_new_user function (get first 500 chars)
SELECT 
    'function_check' as check_type,
    routine_name,
    LEFT(routine_definition, 500) as function_start
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- 5. Count existing notification_preferences records
SELECT 
    'record_count' as check_type,
    COUNT(*) as total_records
FROM public.notification_preferences;

-- 6. Check for any recent signup errors in the logs
-- (This would need to be run in Supabase dashboard logs, not SQL)

-- 7. Test if we can create a minimal notification preference record
DO $$
DECLARE
    test_profile_id UUID;
    error_message TEXT;
    success BOOLEAN := FALSE;
BEGIN
    -- Get a real profile ID to test with
    SELECT id INTO test_profile_id 
    FROM public.profiles 
    LIMIT 1;
    
    IF test_profile_id IS NOT NULL THEN
        BEGIN
            -- Try the minimal insert that should work
            INSERT INTO public.notification_preferences (
                profile_id,
                email_notifications,
                created_at,
                updated_at
            )
            VALUES (
                gen_random_uuid(), -- Use random UUID to avoid conflicts
                true,
                NOW(),
                NOW()
            );
            
            success := TRUE;
            
            -- Clean up
            DELETE FROM public.notification_preferences 
            WHERE profile_id NOT IN (SELECT id FROM public.profiles);
            
        EXCEPTION
            WHEN OTHERS THEN
                error_message := SQLERRM;
                success := FALSE;
        END;
    END IF;
    
    RAISE NOTICE 'Minimal insert test: % (Error: %)', 
        CASE WHEN success THEN 'SUCCESS' ELSE 'FAILED' END,
        COALESCE(error_message, 'None');
END $$;
