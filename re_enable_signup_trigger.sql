-- Re-enable the signup triggers after testing

-- Re-enable the handle_new_user trigger
ALTER TABLE auth.users ENABLE TRIGGER on_auth_user_created;

-- Re-enable the welcome notification trigger
ALTER TABLE auth.users ENABLE TRIGGER create_welcome_notification_trigger;

-- Check which triggers are now enabled
SELECT 
    t.tgname as trigger_name,
    t.tgenabled as enabled,
    c.relname as table_name,
    p.proname as function_name
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE c.relname = 'users' 
AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'auth')
AND t.tgname IN ('on_auth_user_created', 'create_welcome_notification_trigger');

SELECT 'Signup triggers re-enabled' as status;
