import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Users, MessageCircle, Heart, Share2, Sparkles } from 'lucide-react';

const SocialSignUpPrompt: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Main Sign-Up Card */}
      <Card className="p-12 text-center bg-gradient-to-br from-primary/5 via-blue-50 to-green-50 border-2 border-primary/20 shadow-xl">
        <div className="max-w-2xl mx-auto">
          {/* Icon */}
          <div className="flex justify-center mb-6">
            <div className="bg-primary/10 p-6 rounded-full">
              <Users className="w-16 h-16 text-primary" />
            </div>
          </div>
          
          {/* Heading */}
          <h2 className="text-4xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent mb-4">
            Join the Net Zero Community
          </h2>
          
          {/* Description */}
          <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
            Connect with sustainability professionals, share your climate action journey, 
            and collaborate on projects that make a real difference for our planet.
          </p>
          
          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="flex flex-col items-center p-4">
              <div className="bg-blue-100 p-3 rounded-full mb-3">
                <MessageCircle className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-semibold text-lg mb-2">Share Ideas</h3>
              <p className="text-sm text-muted-foreground text-center">
                Post updates, insights, and collaborate on sustainability initiatives
              </p>
            </div>
            
            <div className="flex flex-col items-center p-4">
              <div className="bg-green-100 p-3 rounded-full mb-3">
                <Heart className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-lg mb-2">Connect</h3>
              <p className="text-sm text-muted-foreground text-center">
                Build meaningful connections with like-minded professionals
              </p>
            </div>
            
            <div className="flex flex-col items-center p-4">
              <div className="bg-purple-100 p-3 rounded-full mb-3">
                <Sparkles className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="font-semibold text-lg mb-2">Inspire</h3>
              <p className="text-sm text-muted-foreground text-center">
                Share your success stories and inspire others to take action
              </p>
            </div>
          </div>
          
          {/* Call to Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              asChild
              size="lg"
              className="bg-primary hover:bg-primary-dark text-white px-8 py-3 text-lg font-semibold"
            >
              <a href="/auth?mode=signup">
                Join the Community
              </a>
            </Button>
            
            <Button 
              asChild
              size="lg"
              variant="outline"
              className="border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 text-lg font-semibold"
            >
              <a href="/auth?mode=signin">
                Sign In
              </a>
            </Button>
          </div>
          
          {/* Additional Info */}
          <p className="text-sm text-muted-foreground mt-6">
            Already have an account? <a href="/auth?mode=signin" className="text-primary hover:underline font-medium">Sign in here</a>
          </p>
        </div>
      </Card>
      
      {/* Community Stats Teaser */}
      <Card className="p-6 bg-white/50 border border-gray-200">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Join thousands of sustainability professionals already making an impact
          </h3>
          <p className="text-sm text-muted-foreground">
            See the engagement stats above to discover how active our community is!
          </p>
        </div>
      </Card>
    </div>
  );
};

export default SocialSignUpPrompt;
