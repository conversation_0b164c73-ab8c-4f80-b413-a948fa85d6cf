-- Add policies to support nested queries
BEGIN;

-- Enable RLS on all relevant tables
ALTER TABLE public.business_relevant_industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_service_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.netzero_categories ENABLE ROW LEVEL SECURITY;

-- Add SELECT policies for nested queries
CREATE POLICY "Enable nested queries for business_relevant_industries"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable nested queries for business_service_locations"
    ON public.business_service_locations
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable nested queries for industries"
    ON public.industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable nested queries for locations"
    ON public.locations
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable nested queries for netzero_categories"
    ON public.netzero_categories
    FOR SELECT
    TO authenticated
    USING (true);

-- Grant explicit SELECT permissions
GRANT SELECT ON public.business_relevant_industries TO authenticated;
GRANT SELECT ON public.business_service_locations TO authenticated;
GRANT SELECT ON public.industries TO authenticated;
GRANT SELECT ON public.locations TO authenticated;
GRANT SELECT ON public.netzero_categories TO authenticated;

-- Add necessary foreign key indexes
CREATE INDEX IF NOT EXISTS idx_business_relevant_industries_industry_id 
    ON public.business_relevant_industries(industry_id);
CREATE INDEX IF NOT EXISTS idx_business_service_locations_location_id 
    ON public.business_service_locations(location_id);
CREATE INDEX IF NOT EXISTS idx_businesses_main_category_id 
    ON public.businesses(main_category_id);
CREATE INDEX IF NOT EXISTS idx_businesses_hq_location_id 
    ON public.businesses(hq_location_id);

-- Test the nested query directly
SELECT 
    b.*,
    (
        SELECT json_agg(json_build_object(
            'industries', (
                SELECT json_build_object('name', i.name)
                FROM industries i
                WHERE i.id = bri.industry_id
            )
        ))
        FROM business_relevant_industries bri
        WHERE bri.business_id = b.id
    ) as business_relevant_industries,
    (
        SELECT json_agg(json_build_object(
            'locations', (
                SELECT json_build_object('name', l.name)
                FROM locations l
                WHERE l.id = bsl.location_id
            )
        ))
        FROM business_service_locations bsl
        WHERE bsl.business_id = b.id
    ) as business_service_locations,
    (
        SELECT json_build_object('name', mc.name)
        FROM netzero_categories mc
        WHERE mc.id = b.main_category_id
    ) as main_category,
    (
        SELECT json_build_object('name', hl.name)
        FROM locations hl
        WHERE hl.id = b.hq_location_id
    ) as hq_location
FROM businesses b
ORDER BY b.name ASC
LIMIT 1;

-- Verify all policies are in place
SELECT
    schemaname,
    tablename,
    policyname,
    roles,
    cmd
FROM
    pg_policies
WHERE
    tablename IN (
        'businesses',
        'business_relevant_industries',
        'business_service_locations',
        'industries',
        'locations',
        'netzero_categories'
    )
ORDER BY
    tablename,
    policyname;

COMMIT;
