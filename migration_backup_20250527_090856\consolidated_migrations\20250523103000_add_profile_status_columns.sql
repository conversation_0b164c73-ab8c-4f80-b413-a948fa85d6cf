-- Add deleted_at and is_active columns to profiles table
DO $$ 
BEGIN
  -- Add deleted_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'profiles' 
    AND column_name = 'deleted_at'
  ) THEN
    ALTER TABLE public.profiles 
    ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
    
    CREATE INDEX idx_profiles_deleted_at ON public.profiles(deleted_at);
  END IF;

  -- Add is_active column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'profiles' 
    AND column_name = 'is_active'
  ) THEN
    ALTER TABLE public.profiles 
    ADD COLUMN is_active BOOLEAN DEFAULT true;
    
    CREATE INDEX idx_profiles_is_active ON public.profiles(is_active);
  END IF;
END $$;
