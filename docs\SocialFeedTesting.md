# Social Feed Integration Testing Plan

This document outlines the plan for testing the social feed functionality after the component consolidation.

## Prerequisites

- Ensure the database has test data for posts, comments, and user connections
- Ensure a test user is set up with appropriate permissions

## Test Cases

### 1. Viewing the Social Feed

| Test Case | Steps | Expected Result |
|-----------|-------|-----------------|
| Load feed | Navigate to `/social` | Page loads with posts from the database |
| Pagination | Scroll to bottom of feed | Additional posts load if available |
| Empty state | Clear database of posts | "No posts found" message displays |

### 2. Creating Posts

| Test Case | Steps | Expected Result |
|-----------|-------|-----------------|
| Create text post | Enter text in PostCreator and submit | New post appears in feed |
| Create post with categories | Select categories and submit post | Post appears with selected categories |
| Create post with media | Upload media and submit post | Post appears with media displayed |
| Post validation | Submit empty post | Error message appears |

### 3. Post Interactions

| Test Case | Steps | Expected Result |
|-----------|-------|-----------------|
| Like post | Click like button on post | Like count increases, button state changes |
| Unlike post | Click like button again | Like count decreases, button state changes back |
| Add comment | Add comment to post | Comment appears under post |
| Delete comment | Delete a comment (if permitted) | Comment is removed from post |

### 4. Comment Interactions

| Test Case | Steps | Expected Result |
|-----------|-------|-----------------|
| Like comment | Click like button on comment | Like count increases |
| Reply to comment | Add reply to existing comment | Reply appears nested under comment |
| View replies | Click to expand replies | Replies are displayed |

### 5. Category Filtering

| Test Case | Steps | Expected Result |
|-----------|-------|-----------------|
| Filter by category | Click on a category | Posts filtered to show only that category |
| Clear filter | Click "All" or clear filter | All posts are shown again |
| No results with filter | Filter by unused category | "No posts found" message with filter info |

### 6. User Connections

| Test Case | Steps | Expected Result |
|-----------|-------|-----------------|
| View connections | Open connections drawer | List of connections displays |
| Send connection request | Click connect on suggested user | Request sent confirmation |
| Accept connection | Accept incoming request | User moves to connections list |
| Reject connection | Reject incoming request | Request disappears |

## Database Validations

For each action above, verify the following database operations:

1. **Creating a post**
   - New row in `social_posts` table
   - New rows in `post_categories` if categories selected

2. **Liking a post**
   - New row in `post_likes` table
   - Removal of row when unliked

3. **Commenting on a post**
   - New row in `post_comments` table
   - Parent-child relationship if replying to a comment

4. **User connections**
   - New row in `user_connections` table with appropriate status

## Implementation Checklist

After fixing any issues found in testing, ensure:

1. All duplicate components are removed
2. All imports point to the correct components
3. All database interactions are working correctly
4. The UI renders correctly on both desktop and mobile

## Performance Considerations

- Check load time of the feed with many posts
- Verify proper pagination is working
- Ensure media loads efficiently
- Check for any unnecessary re-renders
