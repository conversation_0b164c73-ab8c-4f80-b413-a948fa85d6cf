-- Proper single-step fix for signup - everything in database trigger
-- This ensures profile is created successfully before notification preferences
-- Date: 2025-06-05

BEGIN;

-- Create a bulletproof handle_new_user function that does everything in the right order
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  user_profile_id UUID;
  profile_created BOOLEAN := FALSE;
BEGIN
  -- Step 1: Create profile record with proper error handling
  -- Try the full insert first
  BEGIN
    INSERT INTO public.profiles (
      id, 
      first_name, 
      last_name, 
      email, 
      social_visibility, 
      subscription_tier, 
      subscription_status, 
      created_at, 
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.raw_user_meta_data->>'first_name',
      NEW.raw_user_meta_data->>'last_name',
      NEW.email,
      'public',
      'none',
      'trial',
      NEW.created_at,
      NEW.created_at
    );
    
    user_profile_id := NEW.id;
    profile_created := TRUE;

  EXCEPTION
    WHEN OTHERS THEN
      -- If full profile creation fails, try minimal profile
      BEGIN
        INSERT INTO public.profiles (id, created_at, updated_at)
        VALUES (NEW.id, NEW.created_at, NEW.created_at);

        user_profile_id := NEW.id;
        profile_created := TRUE;
        
      EXCEPTION
        WHEN OTHERS THEN
          -- If even minimal profile fails, we can't continue
          -- But we still return NEW so auth.users gets created
          RETURN NEW;
      END;
  END;

  -- Step 2: Create user consent settings (only if profile was created)
  IF profile_created THEN
    BEGIN
      INSERT INTO public.user_consent_settings (
        user_id,
        profile_visibility,
        newsletter_subscription,
        show_businesses,
        show_events,
        show_connections,
        created_at,
        updated_at
      )
      VALUES (
        NEW.id,
        true, false, true, true, true,
        NEW.created_at, NEW.created_at
      )
      ON CONFLICT (user_id) DO NOTHING;
    EXCEPTION
      WHEN OTHERS THEN
        -- Log but continue
        NULL;
    END;
  END IF;

  -- Step 3: Create notification preferences (only if profile was created)
  IF profile_created THEN
    BEGIN
      -- Double-check that profile exists before creating notification preferences
      IF EXISTS (SELECT 1 FROM public.profiles WHERE id = user_profile_id) THEN
        INSERT INTO public.notification_preferences (
          profile_id,
          email_notifications,
          post_likes,
          post_comments,
          comment_replies,
          comment_likes,
          connection_requests,
          connection_accepted,
          event_signups,
          event_updates,
          system_notifications,
          created_at,
          updated_at
        )
        VALUES (
          user_profile_id,
          true, true, true, true, true, true, true, true, true, true,
          NEW.created_at, NEW.created_at
        )
        ON CONFLICT (profile_id) DO NOTHING;
      END IF;
    EXCEPTION
      WHEN OTHERS THEN
        -- Log but don't fail the signup
        NULL;
    END;
  END IF;

  -- Always return NEW to ensure signup succeeds
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates profile, user consent settings, and notification preferences for new users - proper single-step approach';

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

COMMIT;
