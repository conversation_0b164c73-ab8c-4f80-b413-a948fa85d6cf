-- Fix cancel_connection_request function to handle enum types and provide better debugging
-- Date: 2025-01-03

BEGIN;

-- Drop and recreate the cancel function with better error handling
DROP FUNCTION IF EXISTS public.cancel_connection_request(UUID) CASCADE;

CREATE OR REPLACE FUNCTION public.cancel_connection_request(
    request_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    connection_record RECORD;
    deleted_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- First, check if the connection exists and get its details
    SELECT * INTO connection_record
    FROM public.user_connections
    WHERE id = request_id_param;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found',
            'debug', jsonb_build_object(
                'request_id', request_id_param,
                'current_user', current_user_id
            )
        );
    END IF;
    
    -- Check if current user is the sender
    IF connection_record.user_id != current_user_id THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'You can only cancel requests you sent',
            'debug', jsonb_build_object(
                'request_id', request_id_param,
                'current_user', current_user_id,
                'request_sender', connection_record.user_id,
                'request_recipient', connection_record.connection_id
            )
        );
    END IF;
    
    -- Check if status is pending (handle both text and enum types)
    IF connection_record.status::text != 'pending' THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Can only cancel pending requests',
            'debug', jsonb_build_object(
                'request_id', request_id_param,
                'current_status', connection_record.status::text,
                'expected_status', 'pending'
            )
        );
    END IF;
    
    -- Delete the connection request
    DELETE FROM public.user_connections
    WHERE id = request_id_param 
      AND user_id = current_user_id 
      AND status::text = 'pending';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Failed to delete connection request',
            'debug', jsonb_build_object(
                'request_id', request_id_param,
                'current_user', current_user_id,
                'deleted_count', deleted_count
            )
        );
    END IF;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'id', request_id_param,
            'deleted_count', deleted_count
        )
    );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.cancel_connection_request TO authenticated;

-- Test the function with a non-existent ID to see the error format
SELECT 
    'Testing cancel function' as test_name,
    public.cancel_connection_request('00000000-0000-0000-0000-000000000000'::uuid) as result;

COMMIT;
