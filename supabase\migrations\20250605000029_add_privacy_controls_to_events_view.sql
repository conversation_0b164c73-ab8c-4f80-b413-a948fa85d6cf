-- Add privacy controls to events_with_creator view
-- Only show creator email when appropriate privacy conditions are met
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- UPDATE THE VIEW WITH PRIVACY CONTROLS FOR EMAIL
-- ================================================================

-- Drop the existing view
DROP VIEW IF EXISTS public.events_with_creator CASCADE;

-- Recreate the view with privacy-aware email handling
CREATE OR REPLACE VIEW public.events_with_creator AS
SELECT 
    e.id,
    e.creator_user_id,
    e.title,
    e.start_date,
    e.start_time,
    e.end_date,
    e.end_time,
    e.description,
    e.image_url,
    e.event_type,
    e.physical_location,
    e.meeting_url,
    e.event_category,
    e.tags,
    e.created_at,
    e.updated_at,
    e.industry_ids,
    e.netzero_category_ids,
    e.owner_id,
    e.status,
    e.capacity,
    e.is_private,
    e.category,
    e.industry,
    -- Creator information from profiles table
    concat(p.first_name, ' ', p.last_name) AS creator_name,
    p.avatar_url AS creator_avatar_url,
    -- Only show creator email in specific circumstances:
    -- 1. For the creator themselves (they can see their own email)
    -- 2. For authenticated users viewing public profiles
    -- 3. Never for anonymous users
    CASE 
        WHEN auth.uid() = e.creator_user_id THEN p.email  -- Creator can see their own email
        WHEN auth.uid() IS NOT NULL THEN p.email          -- Authenticated users can see emails for now
        ELSE NULL                                          -- Anonymous users cannot see emails
    END AS creator_email
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id;

-- Grant permissions to the view
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;

-- Add comment explaining the privacy controls
COMMENT ON VIEW public.events_with_creator IS 
'Events view with creator information. Creator email is only visible to authenticated users and the creator themselves for privacy protection.';

-- ================================================================
-- TEST THE PRIVACY CONTROLS
-- ================================================================

-- Test the view with different user contexts
SELECT 
    'PRIVACY_TEST' as test_type,
    COUNT(*) as total_events,
    COUNT(CASE WHEN creator_email IS NOT NULL THEN 1 END) as events_with_email,
    COUNT(CASE WHEN creator_email IS NULL THEN 1 END) as events_without_email
FROM public.events_with_creator;

-- Show sample data to verify privacy controls
SELECT 
    'SAMPLE_PRIVACY' as test_type,
    id,
    title,
    creator_name,
    CASE 
        WHEN creator_email IS NOT NULL THEN 'EMAIL_VISIBLE'
        ELSE 'EMAIL_HIDDEN'
    END as email_status
FROM public.events_with_creator
LIMIT 3;

COMMIT;
