-- Enable RLS on profile_professional_types
ALTER TABLE profile_professional_types ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read any profile's professional types
CREATE POLICY "Allow users to read any profile's professional types"
  ON profile_professional_types
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow users to manage their own professional types
CREATE POLICY "Allow users to manage their own professional types"
  ON profile_professional_types
  FOR ALL
  TO authenticated
  USING (profile_id = auth.uid())
  WITH CHECK (profile_id = auth.uid());

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON profile_professional_types TO authenticated;
