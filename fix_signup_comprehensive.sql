-- EMERGENCY SIGNUP FIX - Based on diagnostic results
-- Date: 2025-01-06
-- ISSUE: notification_preferences table missing or has wrong structure

BEGIN;

-- =====================================================
-- 0. CHECK AND CREATE NOTIFICATION_PREFERENCES TABLE
-- =====================================================

-- First, check if notification_preferences table exists with correct structure
DO $$
BEGIN
  -- Check if table exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'notification_preferences'
  ) THEN
    -- Create the table if it doesn't exist
    CREATE TABLE public.notification_preferences (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
      email_notifications BOOLEAN DEFAULT true,
      post_likes BOOLEAN DEFAULT true,
      post_comments BOOLEAN DEFAULT true,
      comment_replies BOOLEAN DEFAULT true,
      comment_likes BOOLEAN DEFAULT true,
      connection_requests BOOLEAN DEFAULT true,
      connection_accepted BOOLEAN DEFAULT true,
      event_signups BOOLEAN DEFAULT true,
      event_updates BOOLEAN DEFAULT true,
      system_notifications BOOLEAN DEFAULT true,
      created_at TIMESTAMPTZ DEFAULT now(),
      updated_at TIMESTAMPTZ DEFAULT now(),
      UNIQUE(profile_id)
    );

    -- Enable RLS
    ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;

    -- Create RLS policy
    CREATE POLICY "Users can manage their own notification preferences"
    ON public.notification_preferences
    FOR ALL TO authenticated
    USING (profile_id = auth.uid())
    WITH CHECK (profile_id = auth.uid());

    RAISE NOTICE 'Created notification_preferences table';
  ELSE
    -- Check if table has the required columns
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'notification_preferences'
      AND column_name = 'post_likes'
    ) THEN
      -- Add missing columns
      ALTER TABLE public.notification_preferences
      ADD COLUMN IF NOT EXISTS email_notifications BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS post_likes BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS post_comments BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS comment_replies BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS comment_likes BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS connection_requests BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS connection_accepted BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS event_signups BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS event_updates BOOLEAN DEFAULT true,
      ADD COLUMN IF NOT EXISTS system_notifications BOOLEAN DEFAULT true;

      RAISE NOTICE 'Added missing columns to notification_preferences table';
    END IF;
  END IF;
END $$;

-- =====================================================
-- 1. CREATE BULLETPROOF SIGNUP FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
  error_context TEXT;
  profiles_table_exists BOOLEAN;
  notification_prefs_table_exists BOOLEAN;
  consent_settings_table_exists BOOLEAN;
BEGIN
  -- Check if required tables exist
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'profiles'
  ) INTO profiles_table_exists;
  
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'notification_preferences'
  ) INTO notification_prefs_table_exists;
  
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'user_consent_settings'
  ) INTO consent_settings_table_exists;

  -- STEP 1: Create profile record (CRITICAL - must succeed)
  IF profiles_table_exists THEN
    BEGIN
      -- Try full profile creation first
      INSERT INTO public.profiles (
        id, 
        first_name, 
        last_name, 
        email, 
        social_visibility, 
        subscription_tier, 
        subscription_status, 
        created_at, 
        updated_at
      )
      VALUES (
        NEW.id,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name',
        NEW.email,
        'public',
        'none',
        'trial',
        NEW.created_at,
        NEW.created_at
      )
      RETURNING id INTO profile_id;
      
      RAISE NOTICE 'Profile created successfully for user %', NEW.id;
      
    EXCEPTION
      WHEN OTHERS THEN
        -- If full profile creation fails, try minimal insert
        error_context := 'Full profile creation failed: ' || SQLERRM;
        RAISE WARNING '%', error_context;
        
        BEGIN
          INSERT INTO public.profiles (id, created_at, updated_at)
          VALUES (NEW.id, NEW.created_at, NEW.created_at)
          RETURNING id INTO profile_id;
          
          RAISE NOTICE 'Minimal profile created for user %', NEW.id;
          
        EXCEPTION
          WHEN OTHERS THEN
            error_context := 'Minimal profile creation failed: ' || SQLERRM;
            RAISE WARNING '%', error_context;
            profile_id := NEW.id;
        END;
    END;
  ELSE
    RAISE WARNING 'Profiles table does not exist';
    profile_id := NEW.id;
  END IF;

  -- STEP 2: Create notification preferences
  IF notification_prefs_table_exists AND profile_id IS NOT NULL THEN
    BEGIN
      INSERT INTO public.notification_preferences (
        profile_id,
        email_notifications,
        post_likes,
        post_comments,
        comment_replies,
        comment_likes,
        connection_requests,
        connection_accepted,
        event_signups,
        event_updates,
        system_notifications,
        created_at,
        updated_at
      )
      VALUES (
        profile_id,
        true, true, true, true, true, true, true, true, true, true,
        NEW.created_at, NEW.created_at
      )
      ON CONFLICT (profile_id) DO NOTHING;
      
      RAISE NOTICE 'Notification preferences created for user %', NEW.id;
      
    EXCEPTION
      WHEN OTHERS THEN
        error_context := 'Notification preferences creation failed: ' || SQLERRM;
        RAISE WARNING '%', error_context;
    END;
  ELSE
    RAISE WARNING 'Notification preferences table does not exist or profile_id is null';
  END IF;

  -- STEP 3: Create user consent settings
  IF consent_settings_table_exists THEN
    BEGIN
      INSERT INTO public.user_consent_settings (
        user_id,
        profile_visibility,
        newsletter_subscription,
        show_businesses,
        show_events,
        show_connections,
        created_at,
        updated_at
      )
      VALUES (
        NEW.id,
        true,   -- profile_visibility - default true
        true,   -- newsletter_subscription - default true (as per user preference)
        true,   -- show_businesses - default true
        true,   -- show_events - default true
        true,   -- show_connections - default true
        NEW.created_at,
        NEW.created_at
      )
      ON CONFLICT (user_id) DO NOTHING;
      
      RAISE NOTICE 'User consent settings created for user %', NEW.id;
      
    EXCEPTION
      WHEN OTHERS THEN
        error_context := 'User consent settings creation failed: ' || SQLERRM;
        RAISE WARNING '%', error_context;
    END;
  ELSE
    RAISE WARNING 'User consent settings table does not exist';
  END IF;

  -- Always return NEW to ensure signup succeeds
  RETURN NEW;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the signup
    RAISE WARNING 'Critical error in handle_new_user function: %', SQLERRM;
    -- Still return NEW so the user signup doesn't fail
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update function comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Comprehensive user signup handler - creates profile, notification preferences, and consent settings';

-- =====================================================
-- 2. ENSURE TRIGGER EXISTS AND IS ACTIVE
-- =====================================================

-- Drop and recreate the trigger to ensure it's using the updated function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 3. CREATE REPAIR FUNCTION FOR EXISTING USERS
-- =====================================================

CREATE OR REPLACE FUNCTION public.repair_missing_user_records()
RETURNS TABLE(
  user_id UUID,
  profile_created BOOLEAN,
  notification_prefs_created BOOLEAN,
  consent_settings_created BOOLEAN,
  errors TEXT[]
) AS $$
DECLARE
  user_record RECORD;
  profile_created BOOLEAN;
  notification_prefs_created BOOLEAN;
  consent_settings_created BOOLEAN;
  errors TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Loop through all users who might be missing records
  FOR user_record IN 
    SELECT u.id, u.email, u.created_at, u.raw_user_meta_data
    FROM auth.users u
    WHERE u.created_at > '2025-01-01'  -- Only recent users
  LOOP
    profile_created := FALSE;
    notification_prefs_created := FALSE;
    consent_settings_created := FALSE;
    errors := ARRAY[]::TEXT[];
    
    -- Check and create profile if missing
    IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = user_record.id) THEN
      BEGIN
        INSERT INTO public.profiles (
          id, 
          first_name, 
          last_name, 
          email, 
          social_visibility, 
          subscription_tier, 
          subscription_status, 
          created_at, 
          updated_at
        )
        VALUES (
          user_record.id,
          user_record.raw_user_meta_data->>'first_name',
          user_record.raw_user_meta_data->>'last_name',
          user_record.email,
          'public',
          'none',
          'trial',
          user_record.created_at,
          user_record.created_at
        );
        profile_created := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          errors := array_append(errors, 'Profile creation failed: ' || SQLERRM);
      END;
    END IF;
    
    -- Check and create notification preferences if missing
    IF NOT EXISTS (SELECT 1 FROM public.notification_preferences WHERE profile_id = user_record.id) THEN
      BEGIN
        INSERT INTO public.notification_preferences (
          profile_id,
          email_notifications,
          post_likes,
          post_comments,
          comment_replies,
          comment_likes,
          connection_requests,
          connection_accepted,
          event_signups,
          event_updates,
          system_notifications,
          created_at,
          updated_at
        )
        VALUES (
          user_record.id,
          true, true, true, true, true, true, true, true, true, true,
          user_record.created_at, user_record.created_at
        );
        notification_prefs_created := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          errors := array_append(errors, 'Notification preferences creation failed: ' || SQLERRM);
      END;
    END IF;
    
    -- Check and create consent settings if missing
    IF NOT EXISTS (SELECT 1 FROM public.user_consent_settings WHERE user_id = user_record.id) THEN
      BEGIN
        INSERT INTO public.user_consent_settings (
          user_id,
          profile_visibility,
          newsletter_subscription,
          show_businesses,
          show_events,
          show_connections,
          created_at,
          updated_at
        )
        VALUES (
          user_record.id,
          true, true, true, true, true,
          user_record.created_at, user_record.created_at
        );
        consent_settings_created := TRUE;
      EXCEPTION
        WHEN OTHERS THEN
          errors := array_append(errors, 'Consent settings creation failed: ' || SQLERRM);
      END;
    END IF;
    
    -- Return results for this user
    RETURN QUERY SELECT 
      user_record.id,
      profile_created,
      notification_prefs_created,
      consent_settings_created,
      errors;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.repair_missing_user_records() TO authenticated;

COMMIT;
