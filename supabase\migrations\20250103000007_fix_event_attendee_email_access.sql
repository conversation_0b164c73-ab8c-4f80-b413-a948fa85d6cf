-- Fix event attendee email access to ensure GDPR compliance
-- This ensures only event creators can access emails of users who have given consent

BEGIN;

-- Drop existing function if it exists (try all possible signatures)
DROP FUNCTION IF EXISTS public.get_event_attendee_emails(UUID);
DROP FUNCTION IF EXISTS public.get_event_attendee_emails(event_id UUID);
DROP FUNCTION IF EXISTS get_event_attendee_emails(UUID);
DROP FUNCTION IF EXISTS get_event_attendee_emails(event_id UUID);

-- Create the secure function to get event attendee emails
-- Only event creators can access emails, and only for users who have given GDPR consent
CREATE OR REPLACE FUNCTION public.get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow event creators to access emails
  IF NOT EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND creator_user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Access denied: You must be the event creator to view attendee emails';
  END IF;

  -- Return emails only for users who have given GDPR consent
  RETURN QUERY
  SELECT 
    es.user_id,
    au.email
  FROM 
    event_signups es
  JOIN 
    auth.users au ON es.user_id = au.id
  WHERE 
    es.event_id = get_event_attendee_emails.event_id
    AND es.gdpr_consent = true;  -- Only include users who have given GDPR consent
END;
$$;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION public.get_event_attendee_emails(UUID) TO authenticated;

-- Add comment explaining the function
COMMENT ON FUNCTION public.get_event_attendee_emails(UUID) IS 
'Secure function to get attendee emails for event creators. Only returns emails for users who have given GDPR consent.';

-- Create a test function to help debug email access issues
CREATE OR REPLACE FUNCTION public.debug_event_email_access(event_id UUID)
RETURNS TABLE (
    debug_info TEXT,
    user_id UUID,
    email TEXT,
    gdpr_consent BOOLEAN,
    is_creator BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID := auth.uid();
    is_event_creator BOOLEAN;
BEGIN
  -- Check if current user is the event creator
  SELECT EXISTS (
    SELECT 1 FROM events
    WHERE id = event_id
    AND creator_user_id = current_user_id
  ) INTO is_event_creator;

  -- Return debug information
  RETURN QUERY
  SELECT
    CASE
      WHEN is_event_creator THEN 'User is event creator'
      ELSE 'User is NOT event creator'
    END as debug_info,
    es.user_id,
    au.email,
    es.gdpr_consent,
    is_event_creator
  FROM
    event_signups es
  JOIN
    auth.users au ON es.user_id = au.id
  WHERE
    es.event_id = debug_event_email_access.event_id;
END;
$$;

-- Grant access to the debug function
GRANT EXECUTE ON FUNCTION public.debug_event_email_access(UUID) TO authenticated;

COMMIT;

-- Verify the function works correctly
SELECT 'Event attendee email access function updated successfully' as status;
