-- Debug script to find any references to "posts" table that should be "social_posts"
-- Run this to identify what's causing the "relation 'public.posts' does not exist" error

-- Check for functions that might reference "posts" table
SELECT 
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_definition ILIKE '%posts%'
AND routine_definition NOT ILIKE '%social_posts%'
ORDER BY routine_name;

-- Check for triggers that might reference "posts" table
SELECT 
    trigger_name,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND (action_statement ILIKE '%posts%' AND action_statement NOT ILIKE '%social_posts%')
ORDER BY trigger_name;

-- Check for views that might reference "posts" table
SELECT 
    table_name,
    view_definition
FROM information_schema.views 
WHERE table_schema = 'public'
AND view_definition ILIKE '%posts%'
AND view_definition NOT ILIKE '%social_posts%'
ORDER BY table_name;

-- Check what tables actually exist
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public'
AND table_name ILIKE '%post%'
ORDER BY table_name;

-- Check for any constraints that might reference "posts"
SELECT 
    constraint_name,
    table_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE constraint_schema = 'public'
AND constraint_name ILIKE '%posts%'
ORDER BY table_name, constraint_name;
