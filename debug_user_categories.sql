-- Debug script to check user_categories and netzero_categories tables
-- Run this in Supabase SQL Editor while logged in

-- 1. Check if tables exist
SELECT 
    'Table existence check' as section,
    tablename,
    schemaname
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('user_categories', 'netzero_categories')
ORDER BY tablename;

-- 2. Check netzero_categories data
SELECT 
    'Net Zero Categories' as section,
    id,
    name,
    parent_id,
    created_at
FROM public.netzero_categories
ORDER BY parent_id NULLS FIRST, name
LIMIT 20;

-- 3. Check user_categories for current user
SELECT 
    'My Categories' as section,
    uc.id,
    uc.user_id,
    uc.category_id,
    nc.name as category_name,
    nc.parent_id,
    uc.created_at
FROM public.user_categories uc
LEFT JOIN public.netzero_categories nc ON uc.category_id = nc.id
WHERE uc.user_id = auth.uid()
ORDER BY uc.created_at DESC;

-- 4. Check total user_categories count
SELECT 
    'Total User Categories' as section,
    COUNT(*) as total_user_categories,
    COUNT(DISTINCT user_id) as unique_users_with_categories
FROM public.user_categories;

-- 5. Check foreign key constraints
SELECT 
    'Foreign Key Constraints' as section,
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = 'user_categories'
    AND tc.table_schema = 'public';

-- 6. Check RLS policies
SELECT 
    'RLS Policies' as section,
    policyname,
    cmd,
    roles,
    permissive
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'user_categories'
ORDER BY policyname;
