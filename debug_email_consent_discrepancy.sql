-- Debug email consent discrepancy
-- Investigate where email consent is being set and why counts don't match

-- 1. Check the actual user_consent_settings data
SELECT 
    'Raw user_consent_settings data:' as info,
    user_id,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    created_at,
    updated_at
FROM public.user_consent_settings
ORDER BY created_at DESC;

-- 2. Check if there are any NULL values affecting the count
SELECT 
    'Consent settings breakdown:' as info,
    COUNT(*) as total_records,
    COUNT(CASE WHEN share_email_with_event_creators = true THEN 1 END) as creator_email_true,
    COUNT(CASE WHEN share_email_with_event_creators = false THEN 1 END) as creator_email_false,
    COUNT(CASE WHEN share_email_with_event_creators IS NULL THEN 1 END) as creator_email_null,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as attendee_email_true,
    COUNT(CASE WHEN share_email_with_attendees = false THEN 1 END) as attendee_email_false,
    COUNT(CASE WHEN share_email_with_attendees IS NULL THEN 1 END) as attendee_email_null
FROM public.user_consent_settings;

-- 3. Check if there are profiles without consent settings
SELECT 
    'Profiles without consent settings:' as info,
    COUNT(*) as profiles_without_consent
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
WHERE ucs.user_id IS NULL;

-- 4. Check for any consent settings without corresponding profiles
SELECT 
    'Consent settings without profiles:' as info,
    COUNT(*) as orphaned_consent_settings
FROM public.user_consent_settings ucs
LEFT JOIN public.profiles p ON ucs.user_id = p.id
WHERE p.id IS NULL;

-- 5. Look for any functions that might be setting consent to TRUE
SELECT 
    'Functions that might set email consent:' as info,
    p.proname as function_name,
    pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND (
    pg_get_functiondef(p.oid) ILIKE '%share_email_with_event_creators%true%'
    OR pg_get_functiondef(p.oid) ILIKE '%share_email_with_attendees%true%'
);

-- 6. Check event signup process for consent handling
SELECT 
    'Event signups with GDPR consent:' as info,
    COUNT(*) as total_signups,
    COUNT(CASE WHEN gdpr_consent = true THEN 1 END) as gdpr_consent_true,
    COUNT(CASE WHEN gdpr_consent = false THEN 1 END) as gdpr_consent_false,
    COUNT(CASE WHEN gdpr_consent IS NULL THEN 1 END) as gdpr_consent_null
FROM public.event_signups;

-- 7. Check if there are any triggers that might be setting consent
SELECT 
    'Triggers that might affect consent:' as info,
    t.tgname as trigger_name,
    c.relname as table_name,
    pg_get_triggerdef(t.oid) as trigger_definition
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
AND (
    pg_get_triggerdef(t.oid) ILIKE '%share_email%'
    OR c.relname IN ('user_consent_settings', 'event_signups', 'profiles')
);

-- 8. Check the notification_system_health view definition to see if there's an issue
SELECT 
    'Notification system health view definition:' as info,
    definition
FROM pg_views
WHERE schemaname = 'public'
AND viewname = 'notification_system_health';

-- 9. Manual recount to verify the discrepancy
SELECT 
    'Manual recount verification:' as info,
    'User Consent Settings' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN share_email_with_event_creators = true THEN 1 END) as creator_email_consent_count,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as attendee_email_consent_count,
    COUNT(CASE WHEN share_contact_details = true THEN 1 END) as contact_details_consent_count
FROM public.user_consent_settings;

-- 10. Check if there are any RPC functions being called that set consent
SELECT 
    'RPC functions for consent:' as info,
    p.proname as function_name,
    p.proargtypes,
    pg_get_function_arguments(p.oid) as arguments
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND p.proname ILIKE '%consent%';
