-- ENHANCE NOTIFICATION SYSTEM WITH RELATED ENTITY INFORMATION
-- This migration extends the simplified notification system to include actual content from related entities

-- First, drop the existing view to avoid conflicts
DROP VIEW IF EXISTS user_notifications_with_details;

-- Create an enhanced version of the view that joins with related entities
CREATE OR REPLACE VIEW user_notifications_with_details AS
SELECT 
    n.id,
    n.user_id,
    n.type,
    n.related_id AS related_entity_id,
    n.related_type AS related_entity_type,
    n.content AS message,
    -- Parse content as JSON in case it contains structured data
    CASE 
      WHEN n.content ~ '^\\s*\\{.*\\}\\s*$' THEN n.content::jsonb
      ELSE NULL::jsonb
    END AS data,
    n.is_read,
    n.created_at,
    n.updated_at,
    -- Actor information (from profiles if available)
    actor_p.first_name AS actor_first_name,
    actor_p.last_name AS actor_last_name,
    actor_p.avatar_url AS actor_avatar_url,
    
    -- Related entity details based on type
    CASE 
        -- For posts, include the post title/content
        WHEN n.related_type = 'post' THEN 
            jsonb_build_object(
                'post_title', post.title,
                'post_content', left(post.content, 100),
                'post_image_url', post.image_url
            )
        -- For events, include event details
        WHEN n.related_type = 'event' THEN 
            jsonb_build_object(
                'event_title', event.title,
                'event_description', left(event.description, 100),
                'event_start_date', event.start_date,
                'event_location', event.location
            )
        -- For conversations, include conversation details if available
        WHEN n.related_type = 'conversation' THEN 
            jsonb_build_object(
                'conversation_id', n.related_id,
                'message_preview', left(msg.content, 100),
                'sender_name', COALESCE(sender_p.first_name || ' ' || sender_p.last_name, 'Someone')
            )
        -- For connections, include connection info
        WHEN n.related_type = 'connection' THEN 
            jsonb_build_object(
                'connection_name', COALESCE(conn_p.first_name || ' ' || conn_p.last_name, 'Someone'),
                'connection_avatar', conn_p.avatar_url
            )
        ELSE NULL::jsonb
    END AS entity_details
FROM notifications n
-- Left join to actor's profile if actor_id is available
LEFT JOIN profiles actor_p ON actor_p.id = n.user_id
-- Left join to social posts for post-related notifications
LEFT JOIN social_posts post ON n.related_type = 'post' AND n.related_id = post.id
-- Left join to events for event-related notifications
LEFT JOIN events event ON n.related_type = 'event' AND n.related_id = event.id
-- Left join for conversation/message related notifications
LEFT JOIN (
    SELECT DISTINCT ON (conversation_id) 
        conversation_id, 
        content, 
        sender_id, 
        created_at
    FROM messages
    ORDER BY conversation_id, created_at DESC
) msg ON n.related_type = 'conversation' AND n.related_id = msg.conversation_id
LEFT JOIN profiles sender_p ON msg.sender_id = sender_p.id
-- Left join for connection-related notifications
LEFT JOIN user_connections conn ON n.related_type = 'connection' AND n.related_id = conn.id
LEFT JOIN profiles conn_p ON (
    CASE 
        WHEN conn.user_id = n.user_id THEN conn.connection_id
        ELSE conn.user_id
    END = conn_p.id
);

-- Grant permissions to the view
GRANT SELECT ON user_notifications_with_details TO authenticated;

-- Update the get detailed message function to use entity details
CREATE OR REPLACE FUNCTION get_notification_detailed_message(p_notification_id UUID)
RETURNS TEXT
SECURITY DEFINER
AS $$
DECLARE
    v_message TEXT;
    v_type VARCHAR(50);
    v_entity_details JSONB;
BEGIN
    -- Get the notification details
    SELECT 
        message, 
        type, 
        entity_details INTO v_message, v_type, v_entity_details
    FROM user_notifications_with_details
    WHERE id = p_notification_id AND user_id = auth.uid();
    
    -- If no notification found or message is already detailed enough
    IF v_message IS NULL THEN
        RETURN 'Notification not found';
    END IF;
    
    -- If we have entity details, create a more detailed message
    IF v_entity_details IS NOT NULL THEN
        CASE v_type
            WHEN 'post_like' THEN
                RETURN 'Someone liked your post: ' || 
                       COALESCE(v_entity_details->>'post_title', 'a post');
            WHEN 'post_comment' THEN
                RETURN 'Someone commented on your post: ' || 
                       COALESCE(v_entity_details->>'post_title', 'a post');
            WHEN 'event_signup' THEN
                RETURN 'Someone signed up for your event: ' || 
                       COALESCE(v_entity_details->>'event_title', 'an event');
            WHEN 'connection_request' THEN
                RETURN COALESCE(v_entity_details->>'connection_name', 'Someone') || 
                       ' wants to connect with you';
            WHEN 'new_message' THEN
                RETURN 'New message from ' || 
                       COALESCE(v_entity_details->>'sender_name', 'someone') || 
                       ': ' || COALESCE(v_entity_details->>'message_preview', '');
            ELSE
                -- For other types, just return the original message
                RETURN v_message;
        END CASE;
    END IF;
    
    -- Fall back to the original message
    RETURN v_message;
END;
$$ LANGUAGE plpgsql;

-- Grant permission to authenticated users
GRANT EXECUTE ON FUNCTION get_notification_detailed_message TO authenticated;
