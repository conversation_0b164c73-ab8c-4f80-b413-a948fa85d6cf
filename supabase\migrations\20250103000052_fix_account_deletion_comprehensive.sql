-- Comprehensive fix for account deletion process
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: ENSURE ALL FOREIGN KEYS CASCADE FROM auth.users
-- ================================================================

-- Fix profiles table - this is the main table that should cascade from auth.users
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'profiles_id_fkey'
        AND table_name = 'profiles'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.profiles DROP CONSTRAINT profiles_id_fkey;
    END IF;
    
    -- Add the correct constraint
    ALTER TABLE public.profiles 
    ADD CONSTRAINT profiles_id_fkey 
    FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
    
    RAISE NOTICE 'Fixed profiles foreign key constraint';
END $$;

-- Fix notification_preferences table
DO $$
BEGIN
    -- Check if table exists and has the right column
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'notification_preferences' 
        AND table_schema = 'public'
    ) THEN
        -- Check if it references profile_id or user_id
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'notification_preferences' 
            AND column_name = 'profile_id'
            AND table_schema = 'public'
        ) THEN
            -- It references profiles, which is correct since profiles cascades from auth.users
            RAISE NOTICE 'notification_preferences correctly references profiles';
        ELSIF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'notification_preferences' 
            AND column_name = 'user_id'
            AND table_schema = 'public'
        ) THEN
            -- Fix constraint to reference auth.users directly
            ALTER TABLE public.notification_preferences 
            DROP CONSTRAINT IF EXISTS notification_preferences_user_id_fkey;
            
            ALTER TABLE public.notification_preferences 
            ADD CONSTRAINT notification_preferences_user_id_fkey 
            FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
            
            RAISE NOTICE 'Fixed notification_preferences foreign key constraint';
        END IF;
    END IF;
END $$;

-- Fix user_consent_settings table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'user_consent_settings' 
        AND table_schema = 'public'
    ) THEN
        -- This should reference profiles.id since profiles cascades from auth.users
        ALTER TABLE public.user_consent_settings 
        DROP CONSTRAINT IF EXISTS user_consent_settings_user_id_fkey;
        
        ALTER TABLE public.user_consent_settings 
        ADD CONSTRAINT user_consent_settings_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed user_consent_settings foreign key constraint';
    END IF;
END $$;

-- Fix event_signups table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'event_signups' 
        AND table_schema = 'public'
    ) THEN
        -- Check if it has user_id or profile_id
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'event_signups' 
            AND column_name = 'user_id'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE public.event_signups 
            DROP CONSTRAINT IF EXISTS event_signups_user_id_fkey;
            
            ALTER TABLE public.event_signups 
            ADD CONSTRAINT event_signups_user_id_fkey 
            FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
            
            RAISE NOTICE 'Fixed event_signups foreign key constraint';
        END IF;
    END IF;
END $$;

-- Fix training_course_enrollments table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'training_course_enrollments'
        AND table_schema = 'public'
    ) THEN
        -- This table should reference profiles.id
        IF EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'training_course_enrollments'
            AND column_name = 'profile_id'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE public.training_course_enrollments
            DROP CONSTRAINT IF EXISTS training_course_enrollments_profile_id_fkey;

            ALTER TABLE public.training_course_enrollments
            ADD CONSTRAINT training_course_enrollments_profile_id_fkey
            FOREIGN KEY (profile_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

            RAISE NOTICE 'Fixed training_course_enrollments foreign key constraint';
        END IF;

        -- Also check for user_id column (older versions)
        IF EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'training_course_enrollments'
            AND column_name = 'user_id'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE public.training_course_enrollments
            DROP CONSTRAINT IF EXISTS training_course_enrollments_user_id_fkey;

            ALTER TABLE public.training_course_enrollments
            ADD CONSTRAINT training_course_enrollments_user_id_fkey
            FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

            RAISE NOTICE 'Fixed training_course_enrollments user_id foreign key constraint';
        END IF;
    END IF;
END $$;

-- Fix social_posts table (should reference profiles.id since profiles cascades from auth.users)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'social_posts'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.social_posts
        DROP CONSTRAINT IF EXISTS social_posts_user_id_fkey;

        ALTER TABLE public.social_posts
        ADD CONSTRAINT social_posts_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed social_posts foreign key constraint';
    END IF;
END $$;

-- Fix post_comments table (should reference profiles.id)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'post_comments'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.post_comments
        DROP CONSTRAINT IF EXISTS post_comments_user_id_fkey;

        ALTER TABLE public.post_comments
        ADD CONSTRAINT post_comments_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed post_comments foreign key constraint';
    END IF;
END $$;

-- Fix post_likes table (should reference profiles.id)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'post_likes'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.post_likes
        DROP CONSTRAINT IF EXISTS post_likes_user_id_fkey;

        ALTER TABLE public.post_likes
        ADD CONSTRAINT post_likes_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed post_likes foreign key constraint';
    END IF;
END $$;

-- Fix comment_likes table (should reference profiles.id)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'comment_likes'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.comment_likes
        DROP CONSTRAINT IF EXISTS comment_likes_user_id_fkey;

        ALTER TABLE public.comment_likes
        ADD CONSTRAINT comment_likes_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed comment_likes foreign key constraint';
    END IF;
END $$;

-- Fix businesses table (should reference profiles.id)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'businesses'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.businesses
        DROP CONSTRAINT IF EXISTS businesses_owner_id_fkey;

        ALTER TABLE public.businesses
        ADD CONSTRAINT businesses_owner_id_fkey
        FOREIGN KEY (owner_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed businesses foreign key constraint';
    END IF;
END $$;

-- Fix events table (check for creator_user_id or owner_id)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'events'
        AND table_schema = 'public'
    ) THEN
        -- Check for creator_user_id column
        IF EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'events'
            AND column_name = 'creator_user_id'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE public.events
            DROP CONSTRAINT IF EXISTS events_creator_user_id_fkey;

            ALTER TABLE public.events
            ADD CONSTRAINT events_creator_user_id_fkey
            FOREIGN KEY (creator_user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

            RAISE NOTICE 'Fixed events creator_user_id foreign key constraint';
        END IF;

        -- Check for owner_id column
        IF EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'events'
            AND column_name = 'owner_id'
            AND table_schema = 'public'
        ) THEN
            ALTER TABLE public.events
            DROP CONSTRAINT IF EXISTS events_owner_id_fkey;

            ALTER TABLE public.events
            ADD CONSTRAINT events_owner_id_fkey
            FOREIGN KEY (owner_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

            RAISE NOTICE 'Fixed events owner_id foreign key constraint';
        END IF;
    END IF;
END $$;

-- Fix user_connections table (should reference profiles.id)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'user_connections'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.user_connections
        DROP CONSTRAINT IF EXISTS user_connections_user_id_fkey;

        ALTER TABLE public.user_connections
        ADD CONSTRAINT user_connections_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        ALTER TABLE public.user_connections
        DROP CONSTRAINT IF EXISTS user_connections_connection_id_fkey;

        ALTER TABLE public.user_connections
        ADD CONSTRAINT user_connections_connection_id_fkey
        FOREIGN KEY (connection_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed user_connections foreign key constraints';
    END IF;
END $$;

-- Fix user_categories table (should reference profiles.id)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'user_categories'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.user_categories
        DROP CONSTRAINT IF EXISTS user_categories_user_id_fkey;

        ALTER TABLE public.user_categories
        ADD CONSTRAINT user_categories_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed user_categories foreign key constraint';
    END IF;
END $$;

-- ================================================================
-- PART 2: CREATE SAFE USER DATA CLEANUP FUNCTION
-- ================================================================

-- Create a comprehensive cleanup function that handles all user data
CREATE OR REPLACE FUNCTION public.cleanup_user_data_safe(target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    result BOOLEAN := FALSE;
    cleanup_count INTEGER := 0;
BEGIN
    -- Only proceed if user exists
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = target_user_id) THEN
        RAISE NOTICE 'User % does not exist', target_user_id;
        RETURN FALSE;
    END IF;

    RAISE NOTICE 'Starting comprehensive cleanup for user %', target_user_id;

    -- Strategy: Delete the profile first, which should CASCADE to most tables
    -- Then clean up any remaining direct references to auth.users

    -- Step 1: Delete profile (this should cascade to most profile-referenced tables)
    DELETE FROM public.profiles WHERE id = target_user_id;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % profile records', cleanup_count;

    -- Step 2: Clean up any remaining direct references to auth.users
    -- These tables might reference auth.users directly instead of profiles

    -- Clean up notification_preferences (might reference auth.users directly)
    DELETE FROM public.notification_preferences WHERE user_id = target_user_id;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    IF cleanup_count > 0 THEN
        RAISE NOTICE 'Deleted % notification_preferences records', cleanup_count;
    END IF;

    -- Clean up event_signups (might reference auth.users directly)
    DELETE FROM public.event_signups WHERE user_id = target_user_id;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    IF cleanup_count > 0 THEN
        RAISE NOTICE 'Deleted % event_signups records', cleanup_count;
    END IF;

    -- Clean up training_course_enrollments (might have user_id referencing auth.users)
    DELETE FROM public.training_course_enrollments WHERE user_id = target_user_id;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    IF cleanup_count > 0 THEN
        RAISE NOTICE 'Deleted % training_course_enrollments records', cleanup_count;
    END IF;

    -- Clean up events if they reference auth.users directly via creator_user_id
    DELETE FROM public.events WHERE creator_user_id = target_user_id;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    IF cleanup_count > 0 THEN
        RAISE NOTICE 'Deleted % events records', cleanup_count;
    END IF;

    -- Clean up any remaining user_categories that might reference auth.users directly
    DELETE FROM public.user_categories WHERE user_id = target_user_id;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    IF cleanup_count > 0 THEN
        RAISE NOTICE 'Deleted % user_categories records', cleanup_count;
    END IF;

    result := TRUE;
    RAISE NOTICE 'Comprehensive cleanup completed for user %', target_user_id;

    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error during cleanup for user %: %', target_user_id, SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment explaining the function
COMMENT ON FUNCTION public.cleanup_user_data_safe IS 
'Safely cleans up all user data before auth.users deletion. Uses CASCADE constraints where possible and manual cleanup for safety.';

-- ================================================================
-- PART 3: CREATE DIAGNOSTIC FUNCTION
-- ================================================================

-- Create a function to diagnose deletion issues
CREATE OR REPLACE FUNCTION public.diagnose_user_deletion_issues(target_user_id UUID)
RETURNS TABLE (
    table_name TEXT,
    column_name TEXT,
    constraint_name TEXT,
    constraint_type TEXT,
    record_count BIGINT,
    issue_description TEXT
) AS $$
BEGIN
    -- Check profiles table
    RETURN QUERY
    SELECT 
        'profiles'::TEXT,
        'id'::TEXT,
        'profiles_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        (SELECT COUNT(*) FROM public.profiles WHERE id = target_user_id)::BIGINT,
        CASE 
            WHEN EXISTS (SELECT 1 FROM public.profiles WHERE id = target_user_id) 
            THEN 'Profile exists'
            ELSE 'No profile found'
        END::TEXT;
    
    -- Check notification_preferences
    RETURN QUERY
    SELECT 
        'notification_preferences'::TEXT,
        COALESCE(
            (SELECT 'profile_id' WHERE EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'notification_preferences' AND column_name = 'profile_id'
            )),
            (SELECT 'user_id' WHERE EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'notification_preferences' AND column_name = 'user_id'
            )),
            'none'
        )::TEXT,
        'notification_preferences_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE(
            (SELECT COUNT(*) FROM public.notification_preferences WHERE profile_id = target_user_id),
            (SELECT COUNT(*) FROM public.notification_preferences WHERE user_id = target_user_id),
            0
        )::BIGINT,
        'Notification preferences records'::TEXT;
        
    -- Check user_consent_settings
    RETURN QUERY
    SELECT 
        'user_consent_settings'::TEXT,
        'user_id'::TEXT,
        'user_consent_settings_user_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.user_consent_settings WHERE user_id = target_user_id), 0)::BIGINT,
        'User consent settings records'::TEXT;
        
    -- Check event_signups
    RETURN QUERY
    SELECT 
        'event_signups'::TEXT,
        'user_id'::TEXT,
        'event_signups_user_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.event_signups WHERE user_id = target_user_id), 0)::BIGINT,
        'Event signup records'::TEXT;
        
    -- Check training_course_enrollments
    RETURN QUERY
    SELECT
        'training_course_enrollments'::TEXT,
        COALESCE(
            (SELECT 'profile_id' WHERE EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'training_course_enrollments' AND column_name = 'profile_id'
            )),
            (SELECT 'user_id' WHERE EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'training_course_enrollments' AND column_name = 'user_id'
            )),
            'none'
        )::TEXT,
        'training_course_enrollments_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE(
            (SELECT COUNT(*) FROM public.training_course_enrollments WHERE profile_id = target_user_id),
            (SELECT COUNT(*) FROM public.training_course_enrollments WHERE user_id = target_user_id),
            0
        )::BIGINT,
        'Training course enrollment records'::TEXT;

    -- Check social_posts
    RETURN QUERY
    SELECT
        'social_posts'::TEXT,
        'user_id'::TEXT,
        'social_posts_user_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.social_posts WHERE user_id = target_user_id), 0)::BIGINT,
        'Social post records'::TEXT;

    -- Check post_comments
    RETURN QUERY
    SELECT
        'post_comments'::TEXT,
        'user_id'::TEXT,
        'post_comments_user_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.post_comments WHERE user_id = target_user_id), 0)::BIGINT,
        'Post comment records'::TEXT;

    -- Check post_likes
    RETURN QUERY
    SELECT
        'post_likes'::TEXT,
        'user_id'::TEXT,
        'post_likes_user_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.post_likes WHERE user_id = target_user_id), 0)::BIGINT,
        'Post like records'::TEXT;

    -- Check businesses
    RETURN QUERY
    SELECT
        'businesses'::TEXT,
        'owner_id'::TEXT,
        'businesses_owner_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.businesses WHERE owner_id = target_user_id), 0)::BIGINT,
        'Business records'::TEXT;

    -- Check events
    RETURN QUERY
    SELECT
        'events'::TEXT,
        COALESCE(
            (SELECT 'creator_user_id' WHERE EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'events' AND column_name = 'creator_user_id'
            )),
            (SELECT 'owner_id' WHERE EXISTS (
                SELECT 1 FROM information_schema.columns
                WHERE table_name = 'events' AND column_name = 'owner_id'
            )),
            'none'
        )::TEXT,
        'events_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE(
            (SELECT COUNT(*) FROM public.events WHERE creator_user_id = target_user_id),
            (SELECT COUNT(*) FROM public.events WHERE owner_id = target_user_id),
            0
        )::BIGINT,
        'Event records'::TEXT;

    -- Check user_connections
    RETURN QUERY
    SELECT
        'user_connections'::TEXT,
        'user_id'::TEXT,
        'user_connections_user_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.user_connections WHERE user_id = target_user_id OR connection_id = target_user_id), 0)::BIGINT,
        'User connection records'::TEXT;

    -- Check user_categories
    RETURN QUERY
    SELECT
        'user_categories'::TEXT,
        'user_id'::TEXT,
        'user_categories_user_id_fkey'::TEXT,
        'FOREIGN KEY'::TEXT,
        COALESCE((SELECT COUNT(*) FROM public.user_categories WHERE user_id = target_user_id), 0)::BIGINT,
        'User category records'::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;

-- ================================================================
-- VERIFICATION
-- ================================================================

-- Test the diagnostic function
SELECT 'Diagnostic function created successfully' as status;

-- Show foreign key constraints for key tables
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
LEFT JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('profiles', 'notification_preferences', 'user_consent_settings', 'event_signups', 'training_course_enrollments')
ORDER BY tc.table_name, tc.constraint_name;
