# NetZero Platform Database Migrations

This directory contains the consolidated database migrations for the NetZero Platform project. The migrations are applied in order based on the timestamp prefix in the filename.

## Migration Organization

Migrations are organized chronologically with the following naming convention:

```
YYYYMMDDHHMMSS_descriptive_name.sql
```

Where:
- `YYYYMMDDHHMMSS` is the timestamp that determines the order of execution
- `descriptive_name` is a brief description of what the migration does

## Base Structure

- `20240000000000_create_profiles_table.sql` - Creates the profiles table
- `20240000000001_create_businesses_table.sql` - Creates the businesses table
- `20240000000002_create_netzero_standards_table.sql` - Creates the netzero standards table
- `20240000000003_create_business_standards_table.sql` - Creates the business standards table

## Migration Categories

The migrations generally fall into these categories:

1. **Base Table Creation** - Establishes fundamental database structure
2. **Feature Implementations** - Adds new features and functionality
3. **Fixes and Enhancements** - Corrects issues or improves existing features
4. **Optimization** - Performance improvements and index creation
5. **Security** - RLS policies and permission management

## Running Migrations

Migrations are automatically applied by the Supabase CLI and deployment processes. To manually apply migrations:

```bash
supabase migration up
```

## Adding New Migrations

When adding new migrations:

1. Create a new file with the current timestamp as prefix
2. Ensure it has proper up/down migrations when applicable
3. Test locally before committing

## Important Notes

- Never modify existing migrations that have been applied to any environment
- Always create a new migration to modify existing structures
- Include comments in your SQL to explain complex operations

## Notes on Search System

The search functionality is consolidated in the `20250524000000_final_search_migration.sql` file,
which implements optimized search views, text search indexes, and user consent settings.

## Migration Consolidation

This directory contains the consolidated migrations from multiple source locations. Previously,
migrations were split across:
- `migrations/` directory
- `migrations/misc_migrations/` directory
- `supabase/migrations/` directory
- `supabase/migrations/archive/` directory

All migrations have been consolidated here with duplicate timestamp issues resolved and
proper sequencing maintained.
