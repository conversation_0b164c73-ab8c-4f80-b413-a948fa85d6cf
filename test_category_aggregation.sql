-- Test the category aggregation query used in the views
-- Run this in Supabase SQL Editor

-- Test 1: Check if the view exists and has data
SELECT COUNT(*) as total_profiles_in_view 
FROM public_professional_profiles;

-- Test 2: Check category aggregation for all users
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) AS category_names_test
FROM profiles p
WHERE p.profile_visibility = true
AND EXISTS (
    SELECT 1 FROM user_categories uc WHERE uc.user_id = p.id
)
LIMIT 10;

-- Test 3: Check the actual view data
SELECT 
    id,
    first_name,
    last_name,
    category_names
FROM public_professional_profiles
WHERE category_names IS NOT NULL
LIMIT 10;
