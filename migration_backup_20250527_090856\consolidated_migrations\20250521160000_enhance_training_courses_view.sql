-- Drop the existing view first
DROP VIEW IF EXISTS training_courses_view;

-- Recreate the view with enhanced functionality
CREATE OR REPLACE VIEW training_courses_view AS
SELECT 
    t.*,
    nc.name as category_name,
    p.first_name || ' ' || p.last_name as creator_name,
    p.avatar_url as creator_avatar_url,
    (
        SELECT COUNT(*)
        FROM training_course_enrollments 
        WHERE course_id = t.id AND status = 'approved'
    ) as enrollment_count
FROM training_courses t
LEFT JOIN netzero_categories nc ON nc.id = t.category_id
LEFT JOIN profiles p ON p.id = t.creator_id
WHERE t.status = 'Published' OR auth.uid() = t.creator_id;

-- Grant permissions on the enhanced view
GRANT SELECT ON training_courses_view TO authenticated;
GRANT SELECT ON training_courses_view TO anon;

-- Add comment for the enhanced view
COMMENT ON VIEW training_courses_view IS 'Enhanced view of training courses including category name, creator information, and enrollment count';
