-- Update 20250522103002_create_jobs_reference_tables.sql to use correct table name
-- =========================================================

-- Drop the incorrect table name
DROP TABLE IF EXISTS public.net_zero_categories CASCADE;

-- Update references in foreign keys
ALTER TABLE IF EXISTS public.user_job_preferences 
    DROP CONSTRAINT IF EXISTS user_job_preferences_net_zero_category_id_fkey,
    ADD CONSTRAINT user_job_preferences_netzero_category_id_fkey 
    FOREIGN KEY (net_zero_category_id) 
    REFERENCES public.netzero_categories(id) 
    ON DELETE CASCADE;
