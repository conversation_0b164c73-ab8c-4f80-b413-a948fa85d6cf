-- Migration to fix ambiguous column references in likes count trigger
-- This fixes the 42702 error by using explicit variable names

-- Drop existing trigger first
DROP TRIGGER IF EXISTS update_post_likes_count_trigger ON public.post_likes;

-- Replace function with fixed version that avoids ambiguous column references
CREATE OR REPLACE FUNCTION public.update_post_likes_count() 
RETURNS TRIGGER AS $$
DECLARE
  likes_count_var INTEGER;
  comments_count_var INTEGER;
BEGIN
  -- Ensure the function is executed in a trigger context
  IF TG_OP IS NULL THEN
    RAISE EXCEPTION 'update_post_likes_count() can only be called as a trigger';
  END IF;

  IF TG_OP = 'INSERT' THEN
    -- Count likes
    SELECT COUNT(*) INTO likes_count_var
    FROM post_likes
    WHERE post_id = NEW.post_id;
    
    -- Get comments count
    SELECT comments_count INTO comments_count_var
    FROM social_posts
    WHERE id = NEW.post_id;
    
    -- Update post - use explicit variable names to avoid ambiguity
    UPDATE social_posts
    SET 
      likes_count = likes_count_var,
      engagement_score = (likes_count_var * 1.0) + (comments_count_var * 2.0)
    WHERE id = NEW.post_id;
  ELSIF TG_OP = 'DELETE' THEN
    -- Count likes
    SELECT COUNT(*) INTO likes_count_var
    FROM post_likes
    WHERE post_id = OLD.post_id;
    
    -- Get comments count
    SELECT comments_count INTO comments_count_var
    FROM social_posts
    WHERE id = OLD.post_id;
    
    -- Update post - use explicit variable names to avoid ambiguity
    UPDATE social_posts
    SET 
      likes_count = likes_count_var,
      engagement_score = (likes_count_var * 1.0) + (comments_count_var * 2.0)
    WHERE id = OLD.post_id;
  END IF;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Recreate trigger with fixed function
CREATE TRIGGER update_post_likes_count_trigger
AFTER INSERT OR DELETE ON public.post_likes
FOR EACH ROW
EXECUTE FUNCTION public.update_post_likes_count();

-- Sync all counts to ensure consistency
SELECT sync_all_post_like_counts();
