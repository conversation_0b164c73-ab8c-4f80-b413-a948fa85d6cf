-- Create junction tables for jobs and their multiple attributes

-- Create tables for jobs' multiple selections
CREATE TABLE IF NOT EXISTS public.job_industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(job_id, industry_id)
);

CREATE TABLE IF NOT EXISTS public.job_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
    net_zero_category_id UUID NOT NULL REFERENCES public.net_zero_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(job_id, net_zero_category_id)
);

CREATE TABLE IF NOT EXISTS public.job_locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES public.locations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(job_id, location_id)
);

-- Enable RLS on junction tables
ALTER TABLE public.job_industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.job_locations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for job_industries
CREATE POLICY "Anyone can view job industries" ON public.job_industries
    FOR SELECT USING (true);

CREATE POLICY "Job creators can insert job industries" ON public.job_industries
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

CREATE POLICY "Job creators can update job industries" ON public.job_industries
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

CREATE POLICY "Job creators can delete job industries" ON public.job_industries
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

-- Create RLS policies for job_categories
CREATE POLICY "Anyone can view job categories" ON public.job_categories
    FOR SELECT USING (true);

CREATE POLICY "Job creators can insert job categories" ON public.job_categories
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

CREATE POLICY "Job creators can update job categories" ON public.job_categories
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

CREATE POLICY "Job creators can delete job categories" ON public.job_categories
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

-- Create RLS policies for job_locations
CREATE POLICY "Anyone can view job locations" ON public.job_locations
    FOR SELECT USING (true);

CREATE POLICY "Job creators can insert job locations" ON public.job_locations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

CREATE POLICY "Job creators can update job locations" ON public.job_locations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

CREATE POLICY "Job creators can delete job locations" ON public.job_locations
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.jobs
            WHERE id = job_id AND created_by = auth.uid()
        )
    );

-- Add indexes for performance
CREATE INDEX idx_job_industries_job ON public.job_industries(job_id);
CREATE INDEX idx_job_industries_industry ON public.job_industries(industry_id);
CREATE INDEX idx_job_categories_job ON public.job_categories(job_id);
CREATE INDEX idx_job_categories_category ON public.job_categories(net_zero_category_id);
CREATE INDEX idx_job_locations_job ON public.job_locations(job_id);
CREATE INDEX idx_job_locations_location ON public.job_locations(location_id);

-- Create a view to make it easier to query jobs with their related data
CREATE OR REPLACE VIEW public.jobs_with_details AS
SELECT 
    j.*,
    array_agg(DISTINCT i.name) as industries,
    array_agg(DISTINCT c.name) as net_zero_categories,
    array_agg(DISTINCT l.name) as locations
FROM public.jobs j
LEFT JOIN public.job_industries ji ON j.id = ji.job_id
LEFT JOIN public.industries i ON ji.industry_id = i.id
LEFT JOIN public.job_categories jc ON j.id = jc.job_id
LEFT JOIN public.net_zero_categories c ON jc.net_zero_category_id = c.id
LEFT JOIN public.job_locations jl ON j.id = jl.job_id
LEFT JOIN public.locations l ON jl.location_id = l.id
GROUP BY j.id;
