-- Create function to handle post deletions and trigger edge function
CREATE OR <PERSON><PERSON>LACE FUNCTION handle_deleted_post_media()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the deleted post had an image
  IF OLD.media_type = 'image' AND OLD.media_url IS NOT NULL THEN    -- Call edge function to delete the image from Cloudflare    PERFORM pg_net.http_post(
      url := 'https://' || current_database() || '.supabase.co/functions/v1/cloudflare-images',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || NULLIF(current_setting('app.settings.service_role_key', true), '')::text
      ),
      body := jsonb_build_object(
        'record', jsonb_build_object(
          'media_url', OLD.media_url,
          'media_type', OLD.media_type
        )
      )
    );
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS handle_deleted_post_media_trigger ON social_posts;

-- Create the trigger
CREATE TRIGGER handle_deleted_post_media_trigger
  AFTER DELETE ON social_posts
  FOR EACH ROW
  EXECUTE FUNCTION handle_deleted_post_media();

-- Set up the service role key as a database parameter
-- This will be set by the application during startup
ALTER DATABASE CURRENT_DATABASE SET app.settings.service_role_key = '';
