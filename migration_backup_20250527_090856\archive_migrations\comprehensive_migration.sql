-- ==========================================
-- COMPREHENSIVE MIGRATION SCRIPT
-- Includes consent settings and search optimization
-- ==========================================

-- ==========================================
-- CONSENT SETTINGS IMPLEMENTATION
-- ==========================================

-- ==========================================
-- 20250522100000_implement_event_email_consent_system.sql
-- ==========================================


-- Create a dedicated user consent settings table
CREATE TABLE IF NOT EXISTS public.user_consent_settings (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  share_email_with_event_creators BOOLEAN NOT NULL DEFAULT FALSE,
  share_email_with_attendees BOOLEAN NOT NULL DEFAULT FALSE,
  share_contact_details BOOLEAN NOT NULL DEFAULT FALSE,
  consent_updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS to the consent settings table
ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

-- Users can only view and edit their own consent settings
CREATE POLICY view_own_consent_settings 
  ON public.user_consent_settings 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY update_own_consent_settings 
  ON public.user_consent_settings 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Add a trigger to automatically create default consent settings for new users
CREATE OR REPLACE FUNCTION create_default_user_consent_settings()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_consent_settings (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add trigger to create default consent settings whenever a profile is created
CREATE TRIGGER create_consent_settings_on_profile_creation
  AFTER INSERT ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION create_default_user_consent_settings();

-- Add consent field to event_signups table to track specific event consent
ALTER TABLE public.event_signups 
ADD COLUMN IF NOT EXISTS gdpr_consent BOOLEAN DEFAULT FALSE;

-- Update the existing view to properly handle consent settings
DROP VIEW IF EXISTS public.event_signups_with_users;

CREATE VIEW public.event_signups_with_users AS
  SELECT 
    es.id,
    es.event_id,
    es.user_id,
    es.signup_date,
    es.status,
    es.hide_attendance,
    es.notes,
    es.gdpr_consent,
    es.created_at,
    es.updated_at,
    -- Only expose identifiable information, never emails directly through the view
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    p.avatar_url,
    p.title,
    p.organization
  FROM 
    public.event_signups es
  JOIN 
    auth.users au ON es.user_id = au.id
  LEFT JOIN 
    public.profiles p ON es.user_id = p.id;

-- Grant appropriate permissions on the view
ALTER VIEW public.event_signups_with_users OWNER TO postgres;
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.event_signups_with_users TO service_role;

-- Create a secure function to get event attendee emails ONLY for event creators
CREATE OR REPLACE FUNCTION get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
  user_id UUID,
  full_name TEXT,
  email TEXT,
  consent_status TEXT
) 
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow event creators to access emails
  IF EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND created_by = auth.uid()
  ) THEN
    RETURN QUERY
    SELECT 
      es.user_id,
      CONCAT(p.first_name, ' ', p.last_name) as full_name,
      CASE 
        WHEN (es.gdpr_consent = TRUE OR ucs.share_email_with_event_creators = TRUE) THEN p.email
        ELSE NULL
      END as email,
      CASE 
        WHEN es.gdpr_consent = TRUE THEN 'event specific consent'
        WHEN ucs.share_email_with_event_creators = TRUE THEN 'global consent'
        ELSE 'no consent'
      END as consent_status
    FROM 
      event_signups es
    JOIN 
      profiles p ON es.user_id = p.id
    LEFT JOIN
      user_consent_settings ucs ON es.user_id = ucs.user_id
    WHERE 
      es.event_id = event_id;
  ELSE
    RAISE EXCEPTION 'Access denied: You must be the event creator to view participant emails';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION get_event_attendee_emails TO authenticated;

-- Create a secure function to update a user's event-specific consent
CREATE OR REPLACE FUNCTION update_event_gdpr_consent(p_event_id UUID, p_consent BOOLEAN)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
  v_updated BOOLEAN;
BEGIN
  UPDATE event_signups
  SET gdpr_consent = p_consent,
      updated_at = now()
  WHERE event_id = p_event_id
    AND user_id = auth.uid();
    
  GET DIAGNOSTICS v_updated = ROW_COUNT;
  
  RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION update_event_gdpr_consent TO authenticated;

-- Create a secure function to update a user's global consent settings
CREATE OR REPLACE FUNCTION update_user_consent_settings(
  p_share_email_with_event_creators BOOLEAN,
  p_share_email_with_attendees BOOLEAN,
  p_share_contact_details BOOLEAN
)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
  v_updated BOOLEAN;
BEGIN
  INSERT INTO user_consent_settings (
    user_id,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    consent_updated_at,
    updated_at
  )
  VALUES (
    auth.uid(),
    p_share_email_with_event_creators,
    p_share_email_with_attendees,
    p_share_contact_details,
    now(),
    now()
  )
  ON CONFLICT (user_id) 
  DO UPDATE SET
    share_email_with_event_creators = p_share_email_with_event_creators,
    share_email_with_attendees = p_share_email_with_attendees,
    share_contact_details = p_share_contact_details,
    consent_updated_at = now(),
    updated_at = now();
    
  GET DIAGNOSTICS v_updated = ROW_COUNT;
  
  RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION update_user_consent_settings TO authenticated;

-- Function for event attendees to see other attendees' emails (only if both parties consented)
CREATE OR REPLACE FUNCTION get_event_attendee_contact_details(p_event_id UUID)
RETURNS TABLE (
  user_id UUID,
  full_name TEXT,
  email TEXT
)
SECURITY DEFINER
AS $$
BEGIN
  -- First check if the user is actually attending this event
  IF NOT EXISTS (
    SELECT 1 FROM event_signups
    WHERE event_id = p_event_id AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Access denied: You must be an attendee of this event';
    RETURN;
  END IF;
  
  -- Check if the user has consented to share their email with attendees
  -- Only return emails of other attendees who have also consented
  RETURN QUERY
  SELECT 
    es.user_id,
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    CASE 
      -- Only return email if both the requester and the attendee have consented
      WHEN (es.gdpr_consent = TRUE OR ucs.share_email_with_attendees = TRUE) AND
           EXISTS (
             SELECT 1 FROM user_consent_settings 
             WHERE user_id = auth.uid() AND share_email_with_attendees = TRUE
           )
      THEN p.email
      ELSE NULL
    END as email
  FROM 
    event_signups es
  JOIN 
    profiles p ON es.user_id = p.id
  LEFT JOIN
    user_consent_settings ucs ON es.user_id = ucs.user_id
  WHERE 
    es.event_id = p_event_id;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION get_event_attendee_contact_details TO authenticated;

-- Add RLS to profiles table to protect emails
DO $$ 
BEGIN
  -- Check if RLS is already enabled
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'profiles' 
    AND rowsecurity = true
  ) THEN
    -- Enable RLS
    ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    
    -- Create policy for users to see their own profile
    CREATE POLICY view_own_profile
      ON public.profiles
      FOR SELECT
      USING (auth.uid() = id);
      
    -- Create policy for users to update their own profile
    CREATE POLICY update_own_profile
      ON public.profiles
      FOR UPDATE
      USING (auth.uid() = id);
      
    -- Create policy for admin access if needed
    -- CREATE POLICY admin_access_profiles...
  END IF;
END $$;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_event_signups_user_id ON public.event_signups(user_id);
CREATE INDEX IF NOT EXISTS idx_event_signups_event_id ON public.event_signups(event_id);
CREATE INDEX IF NOT EXISTS idx_user_consent_settings_user_id ON public.user_consent_settings(user_id);


-- ==========================================
-- SEARCH OPTIMIZATION IMPLEMENTATION
-- ==========================================

-- ==========================================
-- 20250523100000_optimize_search.sql
-- ==========================================

-- Create optimized view for events
CREATE OR REPLACE VIEW events_with_search AS
SELECT 
    e.*,
    (
        SELECT count(*)
        FROM event_signups es
        WHERE es.event_id = e.id
    ) as signups_count,
    (
        SELECT array_agg(c.name)
        FROM netzero_categories c
        WHERE c.id = ANY(e.netzero_category_ids)
    ) as category_names,
    (
        SELECT array_agg(i.name)
        FROM industries i
        WHERE i.id = ANY(e.industry_ids)
    ) as industry_names,
    to_tsvector('english', 
        coalesce(e.title, '') || ' ' || 
        coalesce(e.description, '') || ' ' || 
        coalesce(e.event_type::text, '') || ' ' ||
        coalesce(e.event_category::text, '') || ' ' ||
        coalesce(array_to_string(e.tags, ' '), '')
    ) as text_search
FROM events e;

-- Create indexes for search performance
CREATE INDEX IF NOT EXISTS idx_events_title_trgm ON events USING gin (title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_description_trgm ON events USING gin (description gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_text_search ON events_with_search USING gin (text_search);

-- Create optimized view for professionals
CREATE OR REPLACE VIEW professionals_with_search AS
SELECT 
    p.*,
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) as category_names,
    l.name as location_name,
    i.name as industry_name,
    to_tsvector('english', 
        coalesce(p.first_name, '') || ' ' || 
        coalesce(p.last_name, '') || ' ' || 
        coalesce(p.bio, '') || ' ' || 
        coalesce(p.title, '') || ' ' ||
        coalesce(p.organization, '')
    ) as text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE p.profile_visibility = true;

-- Create indexes for professionals search
CREATE INDEX IF NOT EXISTS idx_profiles_name_trgm ON profiles USING gin ((first_name || ' ' || last_name) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_profiles_bio_trgm ON profiles USING gin (bio gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_professionals_text_search ON professionals_with_search USING gin (text_search);

-- Grant necessary permissions
GRANT SELECT ON events_with_search TO authenticated;
GRANT SELECT ON events_with_search TO anon;
GRANT SELECT ON professionals_with_search TO authenticated;
GRANT SELECT ON professionals_with_search TO anon;


-- ==========================================
-- 20250523101000_create_secure_profile_view.sql
-- ==========================================

-- Create a secure view that only exposes public profile data
CREATE OR REPLACE VIEW public_professional_profiles AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,    -- Only expose email if user has consented and with proper access control
    CASE 
        WHEN ucs.share_email_with_event_creators = true OR ucs.share_email_with_attendees = true OR ucs.share_contact_details = true THEN p.email 
        ELSE NULL 
    END as email,
    -- Only expose phone if user has consented to share contact details
    CASE 
        WHEN ucs.share_contact_details = true THEN p.phone
        ELSE NULL 
    END as phone,
    -- Only show social links if profile is public
    CASE 
        WHEN p.profile_visibility = true THEN p.linkedin
        ELSE NULL 
    END as linkedin,
    CASE 
        WHEN p.profile_visibility = true THEN p.twitter
        ELSE NULL 
    END as twitter,
    CASE 
        WHEN p.profile_visibility = true THEN p.website
        ELSE NULL 
    END as website,
    p.location_id,
    p.main_industry_id,
    p.profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    l.name as location_name,
    i.name as industry_name,
    -- Include category names but not IDs
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) as category_names,
    -- Include professional type names but not IDs
    (
        SELECT array_agg(pt.name)
        FROM profile_professional_types ppt
        JOIN sustainability_professional_types pt ON pt.id = ppt.professional_type_id
        WHERE ppt.profile_id = p.id
    ) as professional_types,
    -- Create a text search vector excluding sensitive data
    to_tsvector('english', 
        coalesce(p.first_name, '') || ' ' || 
        coalesce(p.last_name, '') || ' ' || 
        coalesce(p.bio, '') || ' ' || 
        coalesce(p.title, '') || ' ' ||
        coalesce(p.organization, '')
    ) as text_search
FROM profiles p
LEFT JOIN user_consent_settings ucs ON ucs.user_id = p.id
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    p.profile_visibility = true
    -- Exclude deleted or inactive profiles
    AND p.deleted_at IS NULL
    AND p.is_active = true;

-- Create indexes for efficient searching
CREATE INDEX IF NOT EXISTS idx_public_professionals_text_search 
    ON public_professional_profiles USING gin(text_search);
CREATE INDEX IF NOT EXISTS idx_public_professionals_name 
    ON public_professional_profiles USING gin((first_name || ' ' || last_name) gin_trgm_ops);

-- Set up Row Level Security for the view
ALTER VIEW public_professional_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy that only allows reading public data
CREATE POLICY "Allow reading public professional profiles"
    ON public_professional_profiles
    FOR SELECT
    TO authenticated
    USING (true);  -- Since we filtered in the view, we can allow all rows

-- Grant access to the view
GRANT SELECT ON public_professional_profiles TO authenticated;
GRANT SELECT ON public_professional_profiles TO anon;


-- ==========================================
-- 20250523102000_secure_profile_view_fix.sql
-- ==========================================

-- Check if the user_consent_settings table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_consent_settings') THEN
        -- Create the user consent settings table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.user_consent_settings (
            user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            share_email_with_event_creators BOOLEAN NOT NULL DEFAULT FALSE,
            share_email_with_attendees BOOLEAN NOT NULL DEFAULT FALSE,
            share_contact_details BOOLEAN NOT NULL DEFAULT FALSE,
            consent_updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Add RLS to the consent settings table
        ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

        -- Users can only view and edit their own consent settings
        CREATE POLICY view_own_consent_settings 
            ON public.user_consent_settings 
            FOR SELECT
            TO authenticated
            USING (auth.uid() = user_id);

        CREATE POLICY update_own_consent_settings 
            ON public.user_consent_settings 
            FOR UPDATE
            TO authenticated
            USING (auth.uid() = user_id);

        -- Create index for user_id to speed up joins
        CREATE INDEX IF NOT EXISTS idx_user_consent_settings_user_id ON public.user_consent_settings(user_id);

        -- Add a trigger to automatically create default consent settings for new users
        CREATE OR REPLACE FUNCTION create_default_user_consent_settings()
        RETURNS TRIGGER AS $$
        BEGIN
            INSERT INTO public.user_consent_settings (user_id)
            VALUES (NEW.id)
            ON CONFLICT (user_id) DO NOTHING;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Add trigger to create default consent settings whenever a profile is created
        DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
        CREATE TRIGGER on_auth_user_created
            AFTER INSERT ON auth.users
            FOR EACH ROW
            EXECUTE FUNCTION create_default_user_consent_settings();
    END IF;
END
$$;

-- Now create the secure profile view, checking for the existence of the table
CREATE OR REPLACE VIEW public_professional_profiles AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    -- Only expose email if user has explicitly consented (with null check for missing consent settings)
    CASE 
        WHEN (
            EXISTS (
                SELECT 1 FROM user_consent_settings ucs 
                WHERE ucs.user_id = p.id AND 
                (ucs.share_email_with_event_creators = true OR 
                 ucs.share_email_with_attendees = true OR 
                 ucs.share_contact_details = true)
            )
        ) THEN p.email 
        ELSE NULL 
    END as email,
    -- Only expose phone if user has explicitly consented (with null check for missing consent settings)
    CASE 
        WHEN (
            EXISTS (
                SELECT 1 FROM user_consent_settings ucs 
                WHERE ucs.user_id = p.id AND ucs.share_contact_details = true
            )
        ) THEN p.phone
        ELSE NULL 
    END as phone,
    -- Only show social links if profile is public
    CASE 
        WHEN p.profile_visibility = true THEN p.linkedin
        ELSE NULL 
    END as linkedin,
    CASE 
        WHEN p.profile_visibility = true THEN p.twitter
        ELSE NULL 
    END as twitter,
    CASE 
        WHEN p.profile_visibility = true THEN p.website
        ELSE NULL 
    END as website,
    p.location_id,
    p.main_industry_id,
    p.profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    l.name as location_name,
    i.name as industry_name,
    -- Include category names but not IDs
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) as category_names,
    -- Include professional type names but not IDs
    (
        SELECT array_agg(pt.name)
        FROM profile_professional_types ppt
        JOIN sustainability_professional_types pt ON pt.id = ppt.professional_type_id
        WHERE ppt.profile_id = p.id
    ) as professional_types,
    -- Create a text search vector excluding sensitive data
    to_tsvector('english', 
        coalesce(p.first_name, '') || ' ' || 
        coalesce(p.last_name, '') || ' ' || 
        coalesce(p.bio, '') || ' ' || 
        coalesce(p.title, '') || ' ' ||
        coalesce(p.organization, '')
    ) as text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    p.profile_visibility = true
    -- Exclude deleted or inactive profiles
    AND (p.deleted_at IS NULL OR p.deleted_at > now())
    AND (p.is_active IS NULL OR p.is_active = true);

-- Create indexes for efficient searching
CREATE INDEX IF NOT EXISTS idx_public_professionals_name 
    ON profiles USING gin((first_name || ' ' || last_name) gin_trgm_ops);

CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Set up Row Level Security for the view
ALTER VIEW public_professional_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy that only allows reading public data
CREATE POLICY "Allow reading public professional profiles"
    ON public_professional_profiles
    FOR SELECT
    TO authenticated
    USING (true);  -- Since we filtered in the view, we can allow all rows

-- Grant access to the view
GRANT SELECT ON public_professional_profiles TO authenticated;
GRANT SELECT ON public_professional_profiles TO anon;


