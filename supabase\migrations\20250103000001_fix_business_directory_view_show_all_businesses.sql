-- Fix business_directory_view to show all businesses instead of only verified ones or user-owned ones
-- This addresses the issue where the Businesses page only shows businesses created by the logged-in user

BEGIN;

-- Drop and recreate the business_directory_view without the restrictive WHERE clause
DROP VIEW IF EXISTS public.business_directory_view CASCADE;

CREATE OR REPLACE VIEW public.business_directory_view
WITH (security_invoker = on) AS
SELECT 
    b.id,
    b.name,
    b.description,
    b.logo_url,
    b.website,
    CASE WHEN b.is_verified = true THEN b.email ELSE NULL END AS email,
    b.tlr_level,
    b.hq_location_id,
    b.main_category_id,
    b.owner_id,
    b.created_at,
    b.updated_at,
    b.is_verified,
    b.verification_date,
    b.sponsorship_tier,
    b.sponsorship_start_date,
    b.sponsorship_end_date,
    l.name AS hq_location_name,
    c.name AS main_category_name,
    (
        SELECT array_agg(i.name)
        FROM business_relevant_industries bri
        JOIN industries i ON i.id = bri.industry_id
        WHERE bri.business_id = b.id
    ) AS industry_names,
    (
        SELECT array_agg(loc.name)
        FROM business_service_locations bsl
        JOIN locations loc ON loc.id = bsl.location_id
        WHERE bsl.business_id = b.id
    ) AS location_names
FROM businesses b
LEFT JOIN locations l ON l.id = b.hq_location_id
LEFT JOIN netzero_categories c ON c.id = b.main_category_id;

-- Grant necessary permissions
GRANT SELECT ON public.business_directory_view TO authenticated;
GRANT SELECT ON public.business_directory_view TO service_role;

COMMIT;

-- Verify the fix
SELECT 'Business directory view updated to show all businesses' as status;
