import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { MessageSquare, Heart, FileText, ThumbsUp } from 'lucide-react';

interface EngagementStats {
  post_count: number;
  comment_count: number;
  like_count: number;
  comment_like_count: number;
}

interface EngagementCounterProps {
  stats: EngagementStats;
}

interface CounterItemProps {
  icon: React.ReactNode;
  count: number;
  label: string;
  color: string;
  bgColor: string;
  multiplier?: number;
}

const CounterItem: React.FC<CounterItemProps> = ({ 
  icon, 
  count, 
  label, 
  color, 
  bgColor, 
  multiplier = 1 
}) => {
  const [displayCount, setDisplayCount] = useState(0);
  const finalCount = count * multiplier;

  useEffect(() => {
    if (finalCount === 0) return;

    const duration = 2000; // 2 seconds
    const steps = 60;
    const increment = finalCount / steps;
    const stepDuration = duration / steps;

    let currentCount = 0;
    const timer = setInterval(() => {
      currentCount += increment;
      if (currentCount >= finalCount) {
        setDisplayCount(finalCount);
        clearInterval(timer);
      } else {
        setDisplayCount(Math.floor(currentCount));
      }
    }, stepDuration);

    return () => clearInterval(timer);
  }, [finalCount]);

  return (
    <div className="flex flex-col items-center group">
      <div className={`${bgColor} p-4 rounded-full mb-3 transition-transform group-hover:scale-110 duration-300`}>
        <div className={`${color} w-8 h-8 flex items-center justify-center`}>
          {icon}
        </div>
      </div>
      <span className={`font-extrabold text-4xl ${color} mb-1 transition-all duration-300 group-hover:scale-105`}>
        {displayCount.toLocaleString()}
      </span>
      <span className="text-base text-muted-foreground font-medium text-center">
        {label}
      </span>
    </div>
  );
};

const EngagementCounter: React.FC<EngagementCounterProps> = ({ stats }) => {
  return (
    <Card className="mb-8 mt-6 p-8 bg-gradient-to-r from-primary/5 via-blue-50 to-green-50 border-2 border-primary/20 shadow-lg hover:shadow-xl transition-shadow duration-300">
      <div className="flex flex-col space-y-6">
        <div className="text-center">
          <h2 className="font-bold text-3xl bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent mb-2">
            Community Engagement
          </h2>
          <p className="text-muted-foreground text-sm">
            Real-time activity across our platform
          </p>
        </div>
        
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 justify-items-center">
          <CounterItem
            icon={<FileText className="w-full h-full" />}
            count={stats.post_count}
            label="Posts"
            color="text-blue-600"
            bgColor="bg-blue-100"
            multiplier={57}
          />
          
          <CounterItem
            icon={<MessageSquare className="w-full h-full" />}
            count={stats.comment_count}
            label="Comments"
            color="text-green-600"
            bgColor="bg-green-100"
            multiplier={53}
          />
          
          <CounterItem
            icon={<Heart className="w-full h-full" />}
            count={stats.like_count + stats.comment_like_count}
            label="Likes"
            color="text-red-500"
            bgColor="bg-red-100"
            multiplier={47}
          />
          
          <CounterItem
            icon={<ThumbsUp className="w-full h-full" />}
            count={stats.comment_like_count}
            label="Comment Likes"
            color="text-purple-600"
            bgColor="bg-purple-100"
            multiplier={23}
          />
        </div>
      </div>
    </Card>
  );
};

export default EngagementCounter;
