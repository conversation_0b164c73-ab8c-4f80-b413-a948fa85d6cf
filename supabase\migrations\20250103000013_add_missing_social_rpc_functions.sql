-- Add missing RPC functions for social media functionality
-- These functions are called by the frontend but were missing from the database

BEGIN;

-- Create comment count increment function
CREATE OR REPLACE FUNCTION public.increment_post_comment_count(post_id_param UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.social_posts
  SET comments_count = comments_count + 1
  WHERE id = post_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create comment count decrement function
CREATE OR REPLACE FUNCTION public.decrement_post_comment_count(post_id_param UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.social_posts
  SET comments_count = GREATEST(comments_count - 1, 0)
  WHERE id = post_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.increment_post_comment_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.decrement_post_comment_count(UUID) TO authenticated;

-- <PERSON> execute permissions to service_role for admin operations
GRANT EXECUTE ON FUNCTION public.increment_post_comment_count(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION public.decrement_post_comment_count(UUID) TO service_role;

COMMIT;

-- Verify the functions were created
SELECT 
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_name IN ('increment_post_comment_count', 'decrement_post_comment_count')
AND routine_schema = 'public'
ORDER BY routine_name;
