# Cloudflare Images Implementation

This document outlines how image uploads are implemented using Cloudflare Images and Supabase.

## Overview

The implementation uses Cloudflare Images for storage and delivery, with image IDs stored in Supabase. The flow is:

1. Frontend requests a direct upload URL from Cloudflare via a Supabase Edge Function
2. User uploads image directly to Cloudflare (client-side)
3. Cloudflare returns an image ID which is stored in Supabase
4. Images are served using Cloudflare's Image Delivery network

## Components

### 1. Supabase Edge Function for Cloudflare Images

Located at: `supabase/functions/cloudflare-images/index.ts`

This Edge Function acts as a secure proxy between the frontend and Cloudflare Images API:

```typescript
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

// CORS headers to allow requests from any origin
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS, DELETE',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    })
  }

  try {
    // Read Cloudflare credentials from environment variables
    const CLOUDFLARE_ACCOUNT_ID = Deno.env.get("CLOUDFLARE_ACCOUNT_ID") || ""
    const CLOUDFLARE_API_TOKEN = Deno.env.get("CLOUDFLARE_API_TOKEN") || ""

    if (!CLOUDFLARE_ACCOUNT_ID || !CLOUDFLARE_API_TOKEN) {
      throw new Error('Missing Cloudflare credentials in environment variables')
    }

    // Parse request body
    const requestData = await req.json()
    const { method } = requestData

    // Handle methods
    switch (method) {
      case "getUploadUrl": {
        // Get direct upload URL from Cloudflare Images
        const response = await fetch(
          `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/images/v2/direct_upload`,
          {
            method: "POST",
            headers: {
              "Authorization": `Bearer ${CLOUDFLARE_API_TOKEN}`,
              "Content-Type": "application/json"
            }
          }
        )

        const result = await response.json()
        
        if (!result.success) {
          throw new Error('Failed to get upload URL from Cloudflare: ' + 
            (result.errors?.[0]?.message || 'Unknown error'))
        }

        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        })
      }

      case "deleteImage": {
        // Delete image from Cloudflare
        const { imageId } = requestData

        if (!imageId) {
          throw new Error('No image ID provided')
        }

        const response = await fetch(
          `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/images/v1/${imageId}`,
          {
            method: "DELETE",
            headers: {
              "Authorization": `Bearer ${CLOUDFLARE_API_TOKEN}`,
              "Content-Type": "application/json"
            }
          }
        )

        const result = await response.json()
        
        if (!result.success) {
          throw new Error('Failed to delete image from Cloudflare: ' + 
            (result.errors?.[0]?.message || 'Unknown error'))
        }

        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        })
      }

      default:
        throw new Error(`Invalid method: ${method}`)
    }
  } catch (error) {
    console.error('Error in cloudflare-images function:', error)
    
    return new Response(JSON.stringify({ 
      success: false,
      error: error.message || 'An unexpected error occurred' 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400
    })
  }
})
```

### 2. Cloudflare Utility Functions

Located at: `src/lib/cloudflare.ts`

Handles URL generation for Cloudflare Images:

```typescript
/**
 * Utility functions for Cloudflare Images
 */

// Get the Cloudflare account hash from environment variables
const CLOUDFLARE_ACCOUNT_HASH = import.meta.env.VITE_CLOUDFLARE_ACCOUNT_HASH || '';

/**
 * Generate a Cloudflare Image delivery URL
 * @param imageId The ID of the image (or full URL if external)
 * @param variant The image variant (e.g., 'public', 'thumbnail')
 * @returns The full Cloudflare Images URL or the original URL if already complete
 */
export function getCloudflareImageUrl(imageId: string, variant: string = 'public'): string {
  if (!imageId) return '';
  
  // If it's already a full URL, return it
  if (imageId.startsWith('http')) return imageId;
  
  // Otherwise, construct the Cloudflare Images URL
  return `https://imagedelivery.net/${CLOUDFLARE_ACCOUNT_HASH}/${imageId}/${variant}`;
}
```

### 3. Image Upload Component 

Located at: `src/components/ImageUpload.tsx`

A reusable component for image uploading:

```typescript
import React, { useState, useRef } from 'react';
import { Button } from './ui/button';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { User2, Upload, Trash2, Loader2 } from 'lucide-react';
import { toast } from './ui/sonner';
import { supabase } from '@/integrations/supabase/client';
import { getCloudflareImageUrl } from '@/lib/cloudflare';

type ImageUploadProps = {
  userId: string;
  currentImageUrl: string | null;
  onImageUpdate: (imageId: string) => Promise<void>;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
};

export function ImageUpload({ 
  userId, 
  currentImageUrl, 
  onImageUpdate,
  className = '',
  size = 'md',
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Size classes based on the size prop
  const sizeClasses = {
    sm: 'h-16 w-16',
    md: 'h-24 w-24',
    lg: 'h-32 w-32',
    xl: 'h-40 w-40',
  };

  // Trigger file input click
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // Handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
      toast.error('File size must be less than 5MB');
      return;
    }

    try {
      setIsUploading(true);

      // Get the session for the authorization header
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        toast.error('You must be logged in to upload an image');
        return;
      }

      // Get direct upload URL from Cloudflare via Edge Function
      const functionResponse = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/cloudflare-images`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ method: 'getUploadUrl' })
        }
      );

      const uploadUrlData = await functionResponse.json();
      
      if (!uploadUrlData.success || !uploadUrlData.result?.uploadURL) {
        throw new Error('Failed to get upload URL');
      }

      // Upload file directly to Cloudflare
      const formData = new FormData();
      formData.append('file', file);
      
      const uploadResponse = await fetch(uploadUrlData.result.uploadURL, {
        method: 'POST',
        body: formData,
      });

      const uploadResult = await uploadResponse.json();
      
      if (!uploadResult.success) {
        throw new Error('Failed to upload image');
      }

      // Get the image ID from the result
      const imageId = uploadResult.result.id;
      
      // Update the profile with the new image ID
      await onImageUpdate(imageId);
      
      toast.success('Profile image updated successfully');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle image deletion
  const handleDeleteImage = async () => {
    if (!currentImageUrl || !userId) return;
    
    try {
      setIsDeleting(true);

      // Get the session for the authorization header
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        toast.error('You must be logged in to delete your image');
        return;
      }

      // Call Cloudflare Edge Function to delete the image if it's a Cloudflare image ID
      if (currentImageUrl && !currentImageUrl.startsWith('http')) {
        const functionResponse = await fetch(
          `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/cloudflare-images`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
              method: 'deleteImage',
              imageId: currentImageUrl
            })
          }
        );

        const result = await functionResponse.json();
        
        if (!result.success) {
          console.warn('Warning: Failed to delete image from Cloudflare', result);
          // Continue anyway to update the database
        }
      }

      // Update the profile to remove the image
      await onImageUpdate('');
      
      toast.success('Profile image removed');
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Failed to delete image');
    } finally {
      setIsDeleting(false);
    }
  };

  // Convert image ID to full URL if needed
  const imageUrl = currentImageUrl ? getCloudflareImageUrl(currentImageUrl) : '';

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="relative">
        <Avatar className={`${sizeClasses[size]} border-2 border-gray-200`}>
          <AvatarImage src={imageUrl} alt="User avatar" />
          <AvatarFallback className="bg-nz-green-100 text-nz-green-800">
            <User2 className="h-1/2 w-1/2" />
          </AvatarFallback>
        </Avatar>
        
        {/* Upload / Change overlay */}
        <div className="absolute -right-2 -bottom-2 flex space-x-1">
          <Button 
            type="button"
            variant="secondary" 
            size="icon"
            className="h-8 w-8 rounded-full shadow"
            onClick={handleUploadClick}
            disabled={isUploading || isDeleting}
          >
            {isUploading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Upload className="h-4 w-4" />
            )}
          </Button>
          
          {currentImageUrl && (
            <Button 
              type="button"
              variant="destructive" 
              size="icon"
              className="h-8 w-8 rounded-full shadow"
              onClick={handleDeleteImage}
              disabled={isUploading || isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
        aria-label="Upload profile image"
      />
    </div>
  );
}
```

### 4. Usage in UserProfile Component

Updated in: `src/pages/UserProfile.tsx`

```tsx
// Inside UserProfile component
const handleAvatarUpdate = async (imageId: string) => {
  await updateProfile({ profile_image_url: imageId });
};

// In the JSX
{user && (
  <ImageUpload 
    userId={user.id} 
    currentImageUrl={profile?.profile_image_url || null}
    onImageUpdate={handleAvatarUpdate}
    size="lg"
  />
)}
```

## Environment Setup

To use this implementation, you need to set up the following environment variables:

1. **Edge Function Secrets** (in Supabase dashboard):
   - `CLOUDFLARE_ACCOUNT_ID`: Your Cloudflare account ID
   - `CLOUDFLARE_API_TOKEN`: API token with permissions for Cloudflare Images

2. **Frontend Environment Variables** (in `.env` file):
   - `VITE_CLOUDFLARE_ACCOUNT_HASH`: Your Cloudflare account hash for image delivery URLs

## Deployment Process

1. **Deploy the Edge Function**:
   ```bash
   npm run deploy:cloudflare-images
   ```
   or directly with Supabase CLI:
   ```bash
   supabase functions deploy cloudflare-images
   ```

2. **Alternative Deployment Methods**:
   - Via Supabase Dashboard
   - Using GitHub Actions CI/CD
   - VS Code Supabase Extension

## How to Create Your Own Image Upload for Other Features

To implement image uploads for other features (e.g., business logos):

1. Create a specialized component or extend the existing ImageUpload component with additional props:
   ```tsx
   // Example extension for business logos
   <ImageUpload
     userId={user.id}
     currentImageUrl={business.logo_url || null}
     onImageUpdate={(imageId) => updateBusiness({ logo_url: imageId })}
     size="lg"
   />
   ```

2. Ensure the existing Edge Function has appropriate permissions for the Cloudflare Images API.

3. Update your database schema to store the Cloudflare image IDs in relevant tables.

## Security Considerations

- The Edge Function validates authentication to ensure only authorized users can upload images
- API keys and credentials are secured as environment variables
- File validation is performed on the client side before uploading
- Direct upload URLs are single-use and expire quickly