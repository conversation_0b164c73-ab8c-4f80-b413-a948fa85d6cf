-- Verify if the cancel function was updated correctly
-- Run this in Supabase SQL Editor

-- 1. Check if the function exists and get its definition
SELECT 
    'Function definition check' as section,
    routine_name,
    routine_type,
    security_type,
    LEFT(routine_definition, 200) as definition_start
FROM information_schema.routines 
WHERE routine_name = 'cancel_connection_request'
AND routine_schema = 'public';

-- 2. Test the function directly with a fake ID to see the error format
SELECT 
    'Direct function test' as section,
    public.cancel_connection_request('00000000-0000-0000-0000-000000000000'::uuid) as result;

-- 3. Check what connection requests actually exist
SELECT 
    'Existing requests' as section,
    id,
    user_id,
    connection_id,
    status,
    status::text as status_text,
    created_at
FROM public.user_connections
WHERE status::text = 'pending'
ORDER BY created_at DESC
LIMIT 10;

-- 4. Check the status column type
SELECT 
    'Status column info' as section,
    column_name,
    data_type,
    udt_name
FROM information_schema.columns
WHERE table_name = 'user_connections'
AND column_name = 'status'
AND table_schema = 'public';
