-- Migration: Add verification and sponsorship features to businesses table
-- This migration adds verification and sponsorship related columns to the businesses table

ALTER TABLE public.businesses
  ADD COLUMN is_verified BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN verification_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN verification_document_url TEXT,
  ADD COLUMN sponsorship_tier TEXT CHECK (sponsorship_tier IN ('none', 'bronze', 'silver', 'gold')),
  ADD COLUMN sponsorship_start_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN sponsorship_end_date TIMESTAMP WITH TIME ZONE;

-- Create index for efficient querying of verified and sponsored businesses
CREATE INDEX idx_businesses_is_verified ON public.businesses (is_verified);
CREATE INDEX idx_businesses_sponsorship_tier ON public.businesses (sponsorship_tier);

-- Create a view for featured businesses to easily filter and sort sponsored businesses
CREATE OR REPLACE VIEW public.featured_businesses AS
SELECT 
  *,
  CASE 
    WHEN sponsorship_tier = 'gold' THEN 3
    WHEN sponsorship_tier = 'silver' THEN 2 
    WHEN sponsorship_tier = 'bronze' THEN 1
    ELSE 0
  END as sponsorship_level
FROM 
  public.businesses
WHERE 
  sponsorship_tier IS NOT NULL 
  AND sponsorship_tier != 'none'
  AND sponsorship_start_date <= NOW()
  AND (sponsorship_end_date IS NULL OR sponsorship_end_date > NOW())
ORDER BY
  sponsorship_level DESC,
  sponsorship_start_date DESC;

COMMENT ON COLUMN public.businesses.is_verified IS 'Indicates whether the business has been verified';
COMMENT ON COLUMN public.businesses.verification_date IS 'The date when the business was verified';
COMMENT ON COLUMN public.businesses.verification_document_url IS 'URL to the verification document';
COMMENT ON COLUMN public.businesses.sponsorship_tier IS 'The sponsorship tier of the business (none, bronze, silver, gold)';
COMMENT ON COLUMN public.businesses.sponsorship_start_date IS 'The start date of the current sponsorship period';
COMMENT ON COLUMN public.businesses.sponsorship_end_date IS 'The end date of the current sponsorship period';
COMMENT ON VIEW public.featured_businesses IS 'View of currently sponsored businesses ordered by sponsorship level';
