-- Fix the notify_post_like function that's referencing "posts" instead of "social_posts"

BEGIN;

-- Drop the problematic function and trigger
DROP TRIGGER IF EXISTS notify_post_like_trigger ON public.post_likes;
DROP FUNCTION IF EXISTS public.notify_post_like() CASCADE;

-- Recreate the function with correct table reference
CREATE OR REPLACE FUNCTION public.notify_post_like()
RETURNS TRIGGER AS $$
DECLARE
    post_owner_id UUID;
BEGIN
    -- Get the post owner from social_posts table (not "posts")
    SELECT user_id INTO post_owner_id
    FROM public.social_posts
    WHERE id = NEW.post_id;
    
    -- Don't notify if user likes their own post
    IF post_owner_id = NEW.user_id THEN
        RETURN NEW;
    END IF;
    
    -- Only create notification if notifications table exists
    -- (since notifications might have been removed in some migrations)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications' AND table_schema = 'public') THEN
        INSERT INTO public.notifications (user_id, type, content, reference_id, actor_id)
        VALUES (
            post_owner_id,
            'post_like',
            'liked your post',
            NEW.post_id,
            NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER notify_post_like_trigger
    AFTER INSERT ON public.post_likes
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_post_like();

COMMIT;

-- Test the function by checking if it exists
SELECT 
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_name = 'notify_post_like'
AND routine_schema = 'public';
