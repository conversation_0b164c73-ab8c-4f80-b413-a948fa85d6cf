-- Optimize search views for better performance and proper column references
-- This migration creates optimized search views for events, professionals, and public profiles
-- with proper column references and eliminates index creation on views

---------------------------------------------------------
-- PART 1: CREATE INDEXES ON BASE TABLES
---------------------------------------------------------

-- Create indexes on the events table
CREATE INDEX IF NOT EXISTS idx_events_title_trgm ON events USING gin (title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_description_trgm ON events USING gin (description gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_physical_location_trgm ON events USING gin (physical_location gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_category_ids ON events USING gin (netzero_category_ids);
CREATE INDEX IF NOT EXISTS idx_events_industry_ids ON events USING gin (industry_ids);

-- Create indexes on the profiles table
CREATE INDEX IF NOT EXISTS idx_profiles_name_trgm ON profiles USING gin ((first_name || ' ' || last_name) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_profiles_bio_trgm ON profiles USING gin (bio gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_profiles_visibility ON profiles (profile_visibility) WHERE profile_visibility = true;

---------------------------------------------------------
-- PART 2: CREATE USER CONSENT SETTINGS
---------------------------------------------------------

-- Create the user consent settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_consent_settings (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    share_email_with_event_creators BOOLEAN NOT NULL DEFAULT FALSE,
    share_email_with_attendees BOOLEAN NOT NULL DEFAULT FALSE,
    share_contact_details BOOLEAN NOT NULL DEFAULT FALSE,
    consent_updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS to the consent settings table
ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

-- Users can only view and edit their own consent settings
CREATE POLICY view_own_consent_settings 
    ON public.user_consent_settings 
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY update_own_consent_settings 
    ON public.user_consent_settings 
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY insert_own_consent_settings 
    ON public.user_consent_settings
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

---------------------------------------------------------
-- PART 3: CREATE OPTIMIZED SEARCH VIEWS
---------------------------------------------------------

-- Add text_search column to events table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' 
        AND table_name = 'events'
        AND column_name = 'text_search'
    ) THEN
        ALTER TABLE public.events 
        ADD COLUMN text_search tsvector
        GENERATED ALWAYS AS (
            setweight(to_tsvector('english', coalesce(title, '')), 'A') ||
            setweight(to_tsvector('english', coalesce(description, '')), 'B') ||
            setweight(to_tsvector('english', coalesce(physical_location, '')), 'C') ||
            setweight(to_tsvector('english', coalesce(meeting_url, '')), 'D')
        ) STORED;
        
        -- Create index on the stored tsvector column
        CREATE INDEX IF NOT EXISTS idx_events_text_search ON events USING gin(text_search);
    END IF;
END $$;

-- Add text_search column to profiles table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' 
        AND table_name = 'profiles'
        AND column_name = 'text_search'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN text_search tsvector
        GENERATED ALWAYS AS (
            setweight(to_tsvector('english', coalesce(first_name, '')), 'A') ||
            setweight(to_tsvector('english', coalesce(last_name, '')), 'A') ||
            setweight(to_tsvector('english', coalesce(bio, '')), 'B') ||
            setweight(to_tsvector('english', coalesce(title, '')), 'C') ||
            setweight(to_tsvector('english', coalesce(organization, '')), 'C')
        ) STORED;
        
        -- Create index on the stored tsvector column
        CREATE INDEX IF NOT EXISTS idx_profiles_text_search ON profiles USING gin(text_search);
    END IF;
END $$;

-- Events search view
CREATE OR REPLACE VIEW events_with_search AS
SELECT 
    e.id,
    e.title,
    e.description,
    e.start_date,
    e.physical_location,
    e.meeting_url,
    e.event_type,
    e.industry_ids,
    e.netzero_category_ids,
    e.creator_user_id,
    p.first_name as creator_first_name,
    p.last_name as creator_last_name,
    p.organization as creator_organization,
    -- Only show creator email if they have consented
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM user_consent_settings ucs 
            WHERE ucs.user_id = e.creator_user_id 
            AND ucs.share_email_with_event_creators = true
        ) THEN p.email 
        ELSE NULL 
    END as creator_email,
    (
        SELECT array_agg(c.name)
        FROM unnest(e.netzero_category_ids) AS category_id
        JOIN netzero_categories c ON c.id = category_id::UUID
    ) as category_names,
    e.text_search
FROM events e
LEFT JOIN profiles p ON p.id = e.creator_user_id
WHERE 
    -- Only show public events and events from visible profiles
    p.profile_visibility = true;

-- Professionals search view (simplified without soft deletion)
CREATE OR REPLACE VIEW professionals_with_search AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    p.profile_visibility,
    p.subscription_tier,
    p.is_sustainability_professional,
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) as category_names,
    l.name as location_name,
    i.name as industry_name,
    p.text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    -- Note: Removed soft deletion checks since app uses hard deletion
    p.profile_visibility = true;

-- Grant necessary permissions
GRANT SELECT ON events_with_search TO authenticated;
GRANT SELECT ON events_with_search TO anon;
GRANT SELECT ON professionals_with_search TO authenticated;
GRANT SELECT ON professionals_with_search TO anon;

---------------------------------------------------------
-- PART 4: CREATE SECURE PROFILE VIEW
---------------------------------------------------------

-- Create secure view for public profiles (simplified)
CREATE OR REPLACE VIEW public_professional_profiles AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    p.bio,
    p.location_id,
    p.main_industry_id,
    p.is_sustainability_professional,
    p.subscription_tier,
    p.profile_visibility,
    l.name as location_name,
    i.name as industry_name,
    (
        SELECT array_agg(c.name)
        FROM user_categories uc
        JOIN netzero_categories c ON c.id = uc.category_id
        WHERE uc.user_id = p.id
    ) as category_names,
    -- Only show email if user has consented
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM user_consent_settings ucs 
            WHERE ucs.user_id = p.id 
            AND ucs.share_contact_details = true
        ) THEN p.email 
        ELSE NULL 
    END as email,
    p.text_search
FROM profiles p
LEFT JOIN locations l ON l.id = p.location_id
LEFT JOIN industries i ON i.id = p.main_industry_id
WHERE 
    -- Only include profiles that are set to visible
    p.profile_visibility = true;

-- Grant permissions for the secure profile view
GRANT SELECT ON public_professional_profiles TO authenticated;
GRANT SELECT ON public_professional_profiles TO anon;

-- Create a function to search events
CREATE OR REPLACE FUNCTION search_events(
    search_text TEXT DEFAULT NULL,
    categories UUID[] DEFAULT NULL,
    industries UUID[] DEFAULT NULL,
    event_types TEXT[] DEFAULT NULL,
    start_date_after TIMESTAMP DEFAULT NULL,
    start_date_before TIMESTAMP DEFAULT NULL
) 
RETURNS SETOF events_with_search
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM events_with_search e
    WHERE
        -- Apply text search if provided
        (search_text IS NULL OR e.text_search @@ to_tsquery('english', search_text)) AND
        -- Filter by categories if provided
        (categories IS NULL OR categories && e.netzero_category_ids) AND
        -- Filter by industries if provided
        (industries IS NULL OR industries && e.industry_ids) AND
        -- Filter by event type if provided
        (event_types IS NULL OR e.event_type = ANY(event_types)) AND
        -- Filter by date range if provided
        (start_date_after IS NULL OR e.start_date >= start_date_after) AND
        (start_date_before IS NULL OR e.start_date <= start_date_before)
    ORDER BY
        -- If search text provided, order by rank, otherwise by date
        CASE
            WHEN search_text IS NOT NULL THEN ts_rank(e.text_search, to_tsquery('english', search_text))
            ELSE 0
        END DESC,
        e.start_date ASC;
END;
$$;

-- Create a function to search profiles
CREATE OR REPLACE FUNCTION search_professionals(
    search_text TEXT DEFAULT NULL,
    categories UUID[] DEFAULT NULL,
    industry_id UUID DEFAULT NULL,
    location_id UUID DEFAULT NULL,
    is_sustainability_professional BOOLEAN DEFAULT NULL
) 
RETURNS SETOF professionals_with_search
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT p.*
    FROM professionals_with_search p
    WHERE
        -- Apply text search if provided
        (search_text IS NULL OR p.text_search @@ to_tsquery('english', search_text)) AND
        -- Check if professional has any of the specified categories
        (categories IS NULL OR EXISTS (
            SELECT 1 FROM user_categories uc
            WHERE uc.user_id = p.id AND uc.category_id = ANY(categories)
        )) AND
        -- Filter by industry if provided
        (industry_id IS NULL OR p.main_industry_id = industry_id) AND
        -- Filter by location if provided
        (location_id IS NULL OR p.location_id = location_id) AND
        -- Filter by sustainability professional status if provided
        (is_sustainability_professional IS NULL OR p.is_sustainability_professional = is_sustainability_professional)
    ORDER BY
        -- If search text provided, order by rank, otherwise by name
        CASE
            WHEN search_text IS NOT NULL THEN ts_rank(p.text_search, to_tsquery('english', search_text))
            ELSE 0
        END DESC,
        p.first_name ASC, 
        p.last_name ASC;
END;
$$;

-- Grant execute permissions on search functions
GRANT EXECUTE ON FUNCTION search_events TO authenticated;
GRANT EXECUTE ON FUNCTION search_events TO anon;
GRANT EXECUTE ON FUNCTION search_professionals TO authenticated;
GRANT EXECUTE ON FUNCTION search_professionals TO anon;
