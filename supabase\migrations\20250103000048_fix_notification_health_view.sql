-- Fix notification_system_health view to show correct counts
-- The view was using wrong column names causing incorrect counts
-- Date: 2025-01-03

BEGIN;

-- Drop and recreate the notification_system_health view with correct column mapping
DROP VIEW IF EXISTS public.notification_system_health;

CREATE OR REPLACE VIEW public.notification_system_health AS
SELECT 
    'Notification Preferences' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN email_notifications = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN post_comments = false THEN 1 END) as post_comments_disabled_count,
    COUNT(CASE WHEN event_signups = false THEN 1 END) as event_signups_disabled_count
FROM public.notification_preferences
UNION ALL
SELECT 
    'User Consent Settings' as component,
    COUNT(*) as total_records,
    COUNT(CASE WHEN share_email_with_event_creators = true THEN 1 END) as email_enabled_count,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as post_comments_disabled_count,
    COUNT(CASE WHEN share_contact_details = true THEN 1 END) as event_signups_disabled_count
FROM public.user_consent_settings;

-- Grant access to the view
GRANT SELECT ON public.notification_system_health TO authenticated;

-- Create a more detailed consent breakdown view for better monitoring
CREATE OR REPLACE VIEW public.detailed_consent_breakdown AS
SELECT 
    'Email Consent Breakdown' as section,
    COUNT(*) as total_users,
    COUNT(CASE WHEN share_email_with_event_creators = true THEN 1 END) as creator_email_consent_count,
    COUNT(CASE WHEN share_email_with_event_creators = false THEN 1 END) as creator_email_no_consent_count,
    COUNT(CASE WHEN share_email_with_attendees = true THEN 1 END) as attendee_email_consent_count,
    COUNT(CASE WHEN share_email_with_attendees = false THEN 1 END) as attendee_email_no_consent_count,
    COUNT(CASE WHEN share_contact_details = true THEN 1 END) as contact_details_consent_count,
    COUNT(CASE WHEN share_contact_details = false THEN 1 END) as contact_details_no_consent_count
FROM public.user_consent_settings
UNION ALL
SELECT 
    'Notification Preferences Breakdown' as section,
    COUNT(*) as total_users,
    COUNT(CASE WHEN email_notifications = true THEN 1 END) as email_notifications_enabled,
    COUNT(CASE WHEN email_notifications = false THEN 1 END) as email_notifications_disabled,
    COUNT(CASE WHEN post_comments = true THEN 1 END) as post_comments_enabled,
    COUNT(CASE WHEN post_comments = false THEN 1 END) as post_comments_disabled,
    COUNT(CASE WHEN event_signups = true THEN 1 END) as event_signups_enabled,
    COUNT(CASE WHEN event_signups = false THEN 1 END) as event_signups_disabled
FROM public.notification_preferences;

-- Grant access to the detailed view
GRANT SELECT ON public.detailed_consent_breakdown TO authenticated;

-- Create a view to identify users with potentially problematic consent settings
CREATE OR REPLACE VIEW public.consent_audit_flags AS
SELECT 
    p.id as user_id,
    p.first_name,
    p.last_name,
    p.email,
    ucs.share_email_with_event_creators,
    ucs.share_email_with_attendees,
    ucs.share_contact_details,
    ucs.updated_at as consent_last_updated,
    -- Flag potentially problematic settings
    CASE 
        WHEN ucs.share_email_with_attendees = true THEN 'WARNING: Attendee email sharing enabled'
        WHEN ucs.share_contact_details = true THEN 'INFO: Contact details sharing enabled'
        WHEN ucs.share_email_with_event_creators = true THEN 'OK: Event creator email sharing enabled'
        ELSE 'OK: No email sharing enabled'
    END as consent_status,
    -- Check if user has any event signups
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id) as event_signups_count,
    -- Check if user has GDPR consent on any event signups
    (SELECT COUNT(*) FROM public.event_signups es WHERE es.user_id = p.id AND es.gdpr_consent = true) as gdpr_consents_count
FROM public.profiles p
LEFT JOIN public.user_consent_settings ucs ON p.id = ucs.user_id
WHERE ucs.user_id IS NOT NULL
ORDER BY 
    CASE 
        WHEN ucs.share_email_with_attendees = true THEN 1
        WHEN ucs.share_contact_details = true THEN 2
        WHEN ucs.share_email_with_event_creators = true THEN 3
        ELSE 4
    END,
    ucs.updated_at DESC;

-- Grant access to the audit view
GRANT SELECT ON public.consent_audit_flags TO authenticated;

COMMIT;

-- Test the fixed view
SELECT 'Fixed notification system health:' as info;
SELECT * FROM public.notification_system_health;

SELECT 'Detailed consent breakdown:' as info;
SELECT * FROM public.detailed_consent_breakdown;

SELECT 'Consent audit flags (users with email sharing enabled):' as info;
SELECT * FROM public.consent_audit_flags 
WHERE share_email_with_attendees = true 
   OR share_email_with_event_creators = true 
   OR share_contact_details = true;
