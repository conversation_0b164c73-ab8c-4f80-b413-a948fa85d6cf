# Jobs Location Type Fix

## Problem Description

The user reported two issues:
1. When selecting "Remote" as location_type in the frontend, it was being saved as "hybrid" in the database
2. When trying to change location_type directly in the database, they got the error: `new row for relation "jobs" violates check constraint "valid_office_location"`

## Root Cause Analysis

The issue was caused by a mismatch between the database schema and frontend code:

### Database Schema (Current)
- Uses lowercase enum values: `'remote', 'hybrid', 'on-site'`
- May or may not have the `valid_office_location` constraint

### Frontend Code (Previous)
- Expected capitalized enum values: `'Remote', 'Hybrid', 'Office'`
- Used `'Office'` instead of `'on-site'`

### Constraint Issue
The `valid_office_location` constraint expects:
- Remote jobs: `office_location` must be NULL
- Hybrid/Office jobs: `office_location` must be NOT NULL

## Solution Implemented

### 1. Database Migration (`20250127000001_fix_jobs_location_type_enum.sql`)
- Updates existing data from lowercase to capitalized values
- Recreates the `location_type` enum with correct values: `'Remote', 'Hybrid', 'Office'`
- Adds the `valid_office_location` constraint with correct logic
- Also fixes other enum mismatches (salary_bracket, contract_type, hours_type, job_function)

### 2. Frontend Updates

#### JobForm Component (`src/components/JobForm.tsx`)
- Updated enum validation to use correct order: `['Remote', 'Hybrid', 'Office']`
- Enhanced validation to prevent office_location for Remote jobs
- Added validation to require office_location for Hybrid/Office jobs

#### UserJobs Component (`src/components/UserJobs.tsx`)
- Added logic to automatically set `office_location` to NULL for Remote jobs
- Ensures data consistency before saving to database

#### SearchBar Component (`src/components/SearchBar.tsx`)
- Enhanced with clear button functionality
- Better state management

#### New JobFilters Component (`src/components/jobs/JobFilters.tsx`)
- Added comprehensive filtering for jobs
- Supports filtering by location type, contract type, salary, etc.
- Visual filter indicators and clear functionality

#### Jobs Page (`src/pages/Jobs.tsx`)
- Integrated new filter system
- Combined search and filter functionality
- Better user experience with filter counts and clear options

## Database Constraint Logic

The `valid_office_location` constraint ensures:
```sql
CONSTRAINT valid_office_location CHECK (
    (location_type = 'Remote' AND office_location IS NULL) OR
    (location_type IN ('Hybrid', 'Office') AND office_location IS NOT NULL)
)
```

This means:
- **Remote jobs**: Cannot have an office location
- **Hybrid jobs**: Must have an office location specified
- **Office jobs**: Must have an office location specified

## Testing the Fix

### 1. Run the Diagnostic Script
Execute `supabase/diagnostics/check_jobs_schema.sql` in Supabase SQL Editor to check current state.

### 2. Apply the Migration
Run the migration `20250127000001_fix_jobs_location_type_enum.sql` in Supabase.

### 3. Test Frontend
- Create a Remote job → should save with `office_location = NULL`
- Create a Hybrid job → should require office location
- Create an Office job → should require office location
- Test the new filters on the jobs page

## Files Modified

1. `supabase/migrations/20250127000001_fix_jobs_location_type_enum.sql` - Database fix
2. `src/components/JobForm.tsx` - Form validation updates
3. `src/components/UserJobs.tsx` - Save logic updates
4. `src/components/SearchBar.tsx` - Enhanced search functionality
5. `src/components/jobs/JobFilters.tsx` - New filter component
6. `src/pages/Jobs.tsx` - Integrated filters and enhanced search
7. `src/components/DirectoryHeader.tsx` - Support for enhanced search
8. `supabase/diagnostics/check_jobs_schema.sql` - Diagnostic script

## Expected Behavior After Fix

1. **Remote jobs**: 
   - Frontend shows "Remote" option
   - Saves as "Remote" in database
   - `office_location` is automatically set to NULL
   - No office location field required in form

2. **Hybrid jobs**:
   - Frontend shows "Hybrid" option  
   - Saves as "Hybrid" in database
   - `office_location` is required and validated
   - Form shows office location field as required

3. **Office jobs**:
   - Frontend shows "Office" option
   - Saves as "Office" in database  
   - `office_location` is required and validated
   - Form shows office location field as required

4. **Enhanced Jobs Page**:
   - Search with clear functionality
   - Multiple filter options (location type, contract type, salary, etc.)
   - Filter indicators and counts
   - Combined search and filter results
