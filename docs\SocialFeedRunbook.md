# Social Feed Implementation Runbook

This runbook provides step-by-step instructions to ensure all social feed components work together properly and connect correctly to the database.

## Prerequisites

1. Ensure all Supabase tables are set up:
   - `social_posts`
   - `post_likes`
   - `post_comments`
   - `comment_likes`
   - `post_categories`
   - `user_connections`

2. Ensure Supabase client is properly configured in `src/integrations/supabase/client.ts`

## Component Implementation Status

| Component | Status | Database Connection |
|-----------|--------|-------------------|
| `SocialFeed` | ✅ Complete | ✅ Via `useSocialFeed` |
| `SocialFeedPost` | ✅ Complete | ✅ Via parent and hooks |
| `PostCreator` | ✅ Complete | ✅ Via parent |
| `PostComment` | ✅ Complete | ✅ Via parent and hooks |
| `CategoryFilter` | ✅ Complete | ✅ Via `fetchTrendingCategories` |
| `PostCategoriesDisplay` | ✅ Complete | ⚠️ Only displays data passed via props |
| `ConnectionsDrawer` | ✅ Complete | ✅ Via `useUserConnections` |

## Hook Implementation Status

| Hook | Status | Database Connection |
|------|--------|-------------------|
| `useSocialFeed` | ✅ Complete | ✅ Direct Supabase queries |
| `usePostComments` | ✅ Complete | ✅ Direct Supabase queries |
| `usePostLikes` | ✅ Complete | ✅ Direct Supabase queries |
| `useUserConnections` | ✅ Complete | ✅ Direct Supabase queries |

## Integration Steps

1. **Verify Database Schema**
   - Confirm that all required tables exist in your Supabase instance
   - Check that table relationships are properly defined

2. **Load the Social Feed Page**
   - Navigate to `/social` in the application
   - Verify the page loads without errors
   - Check that the `useSocialFeed` hook fetches data correctly

3. **Test Post Creation**
   - Use the `PostCreator` component to create a new post
   - Verify the post includes text content
   - Test adding categories
   - Test adding media (if enabled for user tier)

4. **Test Post Interaction**
   - Like a post using the like button
   - Add a comment to a post
   - Reply to an existing comment

5. **Test Category Filtering**
   - Click on a category in the `CategoryFilter`
   - Verify that posts are filtered correctly
   - Clear the filter and verify all posts return

6. **Test User Connections**
   - View suggested connections
   - Send a connection request
   - Accept/reject incoming connection requests

## Cleanup Plan

Once all functionalities are working correctly, clean up the codebase by:

1. **Remove duplicate components**
   - Delete `src/components/SocialFeedPost.tsx` (if it exists outside the social folder)
   - Ensure imports point to components in the `src/components/social/` directory

2. **Consolidate hooks**
   - Keep all social-related hooks under `src/hooks/social/`
   - Update imports in all files to point to the correct locations

3. **Check for unused code**
   - Remove any unused functions or components
   - Clean up commented-out code

## Troubleshooting

### Common Issues:

1. **Posts not loading**
   - Check network requests for Supabase errors
   - Verify `useSocialFeed` query parameters

2. **Likes not updating**
   - Check `usePostLikes` implementation
   - Verify database permissions for post_likes table

3. **Comments not showing**
   - Check `usePostComments` implementation
   - Verify database permissions for post_comments table

4. **Categories not displaying**
   - Check the `fetchTrendingCategories` function
   - Verify structure of categories data from database

### Data Structure Requirements:

- `PostData` should include author information, content, likes, and comments
- `Comment` should include author information, content, and timestamp
- User connections should track the relationship between users

## Next Steps for Enhancement

1. Add real-time updates using Supabase subscriptions
2. Implement infinite scroll for the feed
3. Add post sharing functionality
4. Enhance media upload capabilities
