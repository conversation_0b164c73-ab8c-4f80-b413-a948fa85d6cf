-- Add Technology Readiness Level (TLR) column to businesses table
ALTER TABLE public.businesses
  ADD COLUMN tlr_level INTEGER NOT NULL DEFAULT 0;

-- Create index for faster queries when filtering by TLR level
CREATE INDEX idx_businesses_tlr_level ON public.businesses(tlr_level);

-- Add function to help query businesses by TLR level
CREATE OR REPLACE FUNCTION get_businesses_by_tlr_level(level_filter INTEGER)
RETURNS SETOF businesses AS $$
BEGIN
  RETURN QUERY
  SELECT * FROM businesses WHERE tlr_level = level_filter;
END;
$$ LANGUAGE plpgsql;
