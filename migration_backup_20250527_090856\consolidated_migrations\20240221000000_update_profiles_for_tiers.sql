-- Update existing subscription_tier column
DO $$ 
BEGIN
  -- Drop existing constraint
  ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_subscription_tier_check;
  
  -- Update any 'seed' values to 'none'
  UPDATE profiles SET subscription_tier = 'none' WHERE subscription_tier = 'seed';
  
  -- Add new constraint
  ALTER TABLE profiles 
    ADD CONSTRAINT profiles_subscription_tier_check 
    CHECK (subscription_tier IN ('none', 'seedling', 'sapling', 'woodland'));
    
  -- Set new default
  ALTER TABLE profiles 
    ALTER COLUMN subscription_tier SET DEFAULT 'none';
END $$;

-- Add sponsorship_tier column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'sponsorship_tier'
  ) THEN
    ALTER TABLE profiles 
    ADD COLUMN sponsorship_tier TEXT NOT NULL 
      DEFAULT 'none' 
      CHECK (sponsorship_tier IN ('none', 'seedling', 'sapling', 'woodland'));
  END IF;
END $$;
