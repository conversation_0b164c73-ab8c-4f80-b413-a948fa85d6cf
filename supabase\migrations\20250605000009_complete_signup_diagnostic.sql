-- Complete diagnostic to find and fix signup issue
-- Run this ONE file to get all the information we need
-- Date: 2025-06-05

-- 1. Check profiles table structure
SELECT 'PROFILES_TABLE' as section, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Test what the function is trying to do step by step
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_metadata JSONB := '{"first_name": "Test", "last_name": "User"}';
    error_msg TEXT;
    step_num INTEGER := 1;
BEGIN
    RAISE NOTICE '=== TESTING SIGNUP FUNCTION STEPS ===';
    RAISE NOTICE 'Test user ID: %', test_user_id;
    
    -- Step 1: Test profiles insert
    RAISE NOTICE 'Step %: Testing profiles insert...', step_num;
    BEGIN
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            email, 
            social_visibility, 
            subscription_tier, 
            subscription_status, 
            created_at, 
            updated_at
        )
        VALUES (
            test_user_id,
            test_metadata->>'first_name',
            test_metadata->>'last_name',
            test_email,
            'public',
            'none',
            'trial',
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Step %: SUCCESS - Profile created', step_num;
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Step %: FAILED - %', step_num, error_msg;
            RAISE NOTICE 'SOLUTION: Fix profiles table or function';
            RETURN; -- Stop here if profiles fail
    END;
    
    step_num := step_num + 1;
    
    -- Step 2: Test user_consent_settings insert
    RAISE NOTICE 'Step %: Testing user_consent_settings insert...', step_num;
    BEGIN
        INSERT INTO public.user_consent_settings (
            user_id,
            profile_visibility,
            newsletter_subscription,
            show_businesses,
            show_events,
            show_connections,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            true, false, true, true, true,
            NOW(), NOW()
        );
        RAISE NOTICE 'Step %: SUCCESS - User consent settings created', step_num;
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Step %: FAILED - %', step_num, error_msg;
            RAISE NOTICE 'SOLUTION: Fix user_consent_settings table or function';
    END;
    
    step_num := step_num + 1;
    
    -- Step 3: Test notification_preferences insert
    RAISE NOTICE 'Step %: Testing notification_preferences insert...', step_num;
    BEGIN
        INSERT INTO public.notification_preferences (
            profile_id,
            email_notifications,
            post_likes,
            post_comments,
            comment_replies,
            comment_likes,
            connection_requests,
            connection_accepted,
            event_signups,
            event_updates,
            system_notifications,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            true, true, true, true, true, true, true, true, true, true,
            NOW(), NOW()
        );
        RAISE NOTICE 'Step %: SUCCESS - Notification preferences created', step_num;
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Step %: FAILED - %', step_num, error_msg;
            RAISE NOTICE 'SOLUTION: Fix notification_preferences table or function';
    END;
    
    -- Clean up test records
    DELETE FROM public.notification_preferences WHERE profile_id = test_user_id;
    DELETE FROM public.user_consent_settings WHERE user_id = test_user_id;
    DELETE FROM public.profiles WHERE id = test_user_id;
    
    RAISE NOTICE '=== TEST COMPLETED ===';
    RAISE NOTICE 'If all steps succeeded, the function should work';
    RAISE NOTICE 'If any step failed, that tells us what to fix';
END $$;
