-- Debug why the signup trigger isn't working
-- Date: 2025-01-06

-- 1. Check if the trigger exists and is enabled
SELECT 
    t.tgname as trigger_name,
    t.tgenabled as enabled,
    c.relname as table_name,
    p.proname as function_name,
    CASE t.tgenabled 
        WHEN 'O' THEN 'ENABLED'
        WHEN 'D' THEN 'DISABLED'
        WHEN 'R' THEN 'REPLICA'
        WHEN 'A' THEN 'ALWAYS'
        ELSE 'UNKNOWN'
    END as status
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE c.relname = 'users' 
AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'auth')
AND t.tgname = 'on_auth_user_created';

-- 2. Check the current handle_new_user function
SELECT 
    p.proname as function_name,
    pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND p.proname = 'handle_new_user';

-- 3. Check if the new user exists in auth.users
SELECT 
    id, 
    email, 
    created_at,
    raw_user_meta_data
FROM auth.users 
WHERE id = '2084c3f6-839c-407e-a1b2-d9b162701bb3';

-- 4. Check if profile was created for this user
SELECT 
    id,
    first_name,
    last_name,
    email,
    created_at
FROM public.profiles 
WHERE id = '2084c3f6-839c-407e-a1b2-d9b162701bb3';

-- 5. Check if notification preferences were created
SELECT 
    profile_id,
    email_notifications,
    created_at
FROM public.notification_preferences 
WHERE profile_id = '2084c3f6-839c-407e-a1b2-d9b162701bb3';

-- 6. Check if user consent settings were created
SELECT 
    user_id,
    profile_visibility,
    newsletter_subscription,
    created_at
FROM public.user_consent_settings 
WHERE user_id = '2084c3f6-839c-407e-a1b2-d9b162701bb3';

-- 7. Test the function manually
DO $$
DECLARE
    test_user_record RECORD;
    result_record RECORD;
BEGIN
    -- Get the user record
    SELECT 
        id, 
        email, 
        created_at, 
        updated_at,
        raw_user_meta_data
    INTO test_user_record
    FROM auth.users 
    WHERE id = '2084c3f6-839c-407e-a1b2-d9b162701bb3';
    
    IF test_user_record.id IS NOT NULL THEN
        RAISE NOTICE 'Found user: % (email: %)', test_user_record.id, test_user_record.email;
        RAISE NOTICE 'User metadata: %', test_user_record.raw_user_meta_data;
        
        -- Try to manually call the function logic
        BEGIN
            -- Try to insert profile
            INSERT INTO public.profiles (
                id, 
                first_name, 
                last_name, 
                email, 
                social_visibility, 
                subscription_tier, 
                subscription_status, 
                created_at, 
                updated_at
            )
            VALUES (
                test_user_record.id,
                test_user_record.raw_user_meta_data->>'first_name',
                test_user_record.raw_user_meta_data->>'last_name',
                test_user_record.email,
                'public',
                'none',
                'trial',
                test_user_record.created_at,
                test_user_record.created_at
            );
            
            RAISE NOTICE 'Profile created successfully';
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Profile creation failed: %', SQLERRM;
        END;
        
        -- Try to insert user consent settings
        BEGIN
            INSERT INTO public.user_consent_settings (
                user_id,
                profile_visibility,
                newsletter_subscription,
                show_businesses,
                show_events,
                show_connections,
                created_at,
                updated_at
            )
            VALUES (
                test_user_record.id,
                true, true, true, true, true,
                test_user_record.created_at,
                test_user_record.created_at
            );
            
            RAISE NOTICE 'User consent settings created successfully';
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'User consent settings creation failed: %', SQLERRM;
        END;
        
        -- Try to insert notification preferences
        BEGIN
            INSERT INTO public.notification_preferences (
                profile_id,
                email_notifications,
                post_likes,
                post_comments,
                comment_replies,
                comment_likes,
                connection_requests,
                connection_accepted,
                event_signups,
                event_updates,
                system_notifications,
                created_at,
                updated_at
            )
            VALUES (
                test_user_record.id,
                true, true, true, true, true, true, true, true, true, true,
                test_user_record.created_at,
                test_user_record.created_at
            );
            
            RAISE NOTICE 'Notification preferences created successfully';
            
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Notification preferences creation failed: %', SQLERRM;
        END;
        
    ELSE
        RAISE NOTICE 'User not found in auth.users';
    END IF;
END $$;
