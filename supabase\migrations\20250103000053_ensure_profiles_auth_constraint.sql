-- Ensure profiles table is properly linked to auth.users
-- This is a supplementary migration to guarantee the most critical constraint exists

BEGIN;

-- Check and add the profiles → auth.users foreign key constraint if missing
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu
            ON tc.constraint_name = ccu.constraint_name
            AND tc.table_schema = ccu.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
            AND tc.table_name = 'profiles'
            AND ccu.table_schema = 'auth'
            AND ccu.table_name = 'users'
    ) THEN
        -- Add the constraint
        ALTER TABLE public.profiles 
        ADD CONSTRAINT profiles_id_fkey 
        FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added profiles → auth.users foreign key constraint';
    ELSE
        RAISE NOTICE 'profiles → auth.users foreign key constraint already exists';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'profiles → auth.users foreign key constraint already exists (duplicate_object)';
    WHEN OTHERS THEN
        RAISE WARNING 'Error adding profiles → auth.users constraint: %', SQLERRM;
END $$;

-- Verify the constraint exists
DO $$
DECLARE
    constraint_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage ccu
        ON tc.constraint_name = ccu.constraint_name
        AND tc.table_schema = ccu.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = 'profiles'
        AND ccu.table_schema = 'auth'
        AND ccu.table_name = 'users';
    
    IF constraint_count > 0 THEN
        RAISE NOTICE 'SUCCESS: profiles → auth.users constraint verified (% constraints found)', constraint_count;
    ELSE
        RAISE WARNING 'FAILED: profiles → auth.users constraint not found';
    END IF;
END $$;

COMMIT;

-- Final verification query
SELECT 
    'Final Verification' as test_name,
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
LEFT JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name = 'profiles'
    AND ccu.table_name = 'users'
    AND ccu.table_schema = 'auth';
