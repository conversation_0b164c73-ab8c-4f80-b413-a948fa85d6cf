-- Cleanup notification system and ensure proper preference checking
-- Remove unused columns and fix notification triggers
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: CLEAN UP NOTIFICATION_PREFERENCES TABLE
-- ================================================================

-- Remove push_notifications column if it exists (not used in platform)
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification_preferences' 
        AND column_name = 'push_notifications'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notification_preferences DROP COLUMN push_notifications;
        RAISE NOTICE 'Removed push_notifications column from notification_preferences';
    END IF;
END $$;

-- Ensure notification_preferences table has the correct structure
-- This handles cases where the table might have the old type/email_enabled structure
DO $$
BEGIN
    -- Check if we have the old structure (type, email_enabled, push_enabled)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notification_preferences' 
        AND column_name = 'type'
        AND table_schema = 'public'
    ) THEN
        -- Drop the old table and recreate with correct structure
        DROP TABLE IF EXISTS public.notification_preferences CASCADE;
        
        CREATE TABLE public.notification_preferences (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE UNIQUE,
            email_notifications BOOLEAN NOT NULL DEFAULT true,
            post_likes BOOLEAN NOT NULL DEFAULT true,
            post_comments BOOLEAN NOT NULL DEFAULT true,
            comment_replies BOOLEAN NOT NULL DEFAULT true,
            comment_likes BOOLEAN NOT NULL DEFAULT true,
            connection_requests BOOLEAN NOT NULL DEFAULT true,
            connection_accepted BOOLEAN NOT NULL DEFAULT true,
            event_signups BOOLEAN NOT NULL DEFAULT true,
            event_updates BOOLEAN NOT NULL DEFAULT true,
            system_notifications BOOLEAN NOT NULL DEFAULT true,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
        
        -- Enable RLS
        ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY "Users can view their own notification preferences"
            ON public.notification_preferences
            FOR SELECT
            TO authenticated
            USING (profile_id = auth.uid());

        CREATE POLICY "Users can update their own notification preferences"
            ON public.notification_preferences
            FOR ALL
            TO authenticated
            USING (profile_id = auth.uid())
            WITH CHECK (profile_id = auth.uid());
            
        -- Create indexes
        CREATE INDEX idx_notification_preferences_profile_id ON public.notification_preferences(profile_id);
        
        RAISE NOTICE 'Recreated notification_preferences table with correct structure';
    END IF;
END $$;

-- ================================================================
-- PART 2: UPDATE NOTIFICATION TRIGGER FUNCTIONS TO RESPECT PREFERENCES
-- ================================================================

-- Update post like notification function to check preferences
CREATE OR REPLACE FUNCTION public.notify_post_like()
RETURNS TRIGGER AS $$
DECLARE
    should_notify BOOLEAN;
    post_author_profile_id UUID;
BEGIN
    -- Get the profile_id of the post author
    SELECT profile_id INTO post_author_profile_id
    FROM public.social_posts
    WHERE id = NEW.post_id;

    -- Don't notify if user likes their own post
    IF post_author_profile_id = NEW.profile_id THEN
        RETURN NEW;
    END IF;

    -- Check if user has enabled notifications for post likes
    SELECT post_likes INTO should_notify
    FROM public.notification_preferences
    WHERE profile_id = post_author_profile_id;

    -- If no preferences found, default to true (notify)
    IF should_notify IS NULL THEN
        should_notify := true;
    END IF;

    IF should_notify THEN
        INSERT INTO public.notifications (
            profile_id,
            type,
            content,
            related_id,
            related_type,
            actor_id
        ) VALUES (
            post_author_profile_id,
            'post_like',
            'liked your post',
            NEW.post_id,
            'post',
            NEW.profile_id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update post comment notification function to check preferences
CREATE OR REPLACE FUNCTION public.notify_post_comment()
RETURNS TRIGGER AS $$
DECLARE
    should_notify BOOLEAN;
    post_author_profile_id UUID;
BEGIN
    -- Get the profile_id of the post author
    SELECT profile_id INTO post_author_profile_id
    FROM public.social_posts
    WHERE id = NEW.post_id;

    -- Don't notify if user comments on their own post
    IF post_author_profile_id = NEW.profile_id THEN
        RETURN NEW;
    END IF;

    -- Check if user has enabled notifications for post comments
    SELECT post_comments INTO should_notify
    FROM public.notification_preferences
    WHERE profile_id = post_author_profile_id;

    -- If no preferences found, default to true (notify)
    IF should_notify IS NULL THEN
        should_notify := true;
    END IF;

    IF should_notify THEN
        INSERT INTO public.notifications (
            profile_id,
            type,
            content,
            related_id,
            related_type,
            actor_id
        ) VALUES (
            post_author_profile_id,
            'post_comment',
            'commented on your post',
            NEW.post_id,
            'post',
            NEW.profile_id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update comment reply notification function to check preferences
CREATE OR REPLACE FUNCTION public.notify_comment_reply()
RETURNS TRIGGER AS $$
DECLARE
    should_notify BOOLEAN;
    parent_comment_author_id UUID;
BEGIN
    -- Only process if this is a reply to another comment
    IF NEW.parent_comment_id IS NOT NULL THEN
        -- Get parent comment author
        SELECT profile_id INTO parent_comment_author_id
        FROM public.post_comments
        WHERE id = NEW.parent_comment_id;

        -- Don't notify if user replies to their own comment
        IF parent_comment_author_id = NEW.profile_id THEN
            RETURN NEW;
        END IF;

        -- Check if user has enabled notifications for comment replies
        SELECT comment_replies INTO should_notify
        FROM public.notification_preferences
        WHERE profile_id = parent_comment_author_id;

        -- If no preferences found, default to true (notify)
        IF should_notify IS NULL THEN
            should_notify := true;
        END IF;

        IF should_notify THEN
            INSERT INTO public.notifications (
                profile_id,
                type,
                content,
                related_id,
                related_type,
                actor_id
            ) VALUES (
                parent_comment_author_id,
                'comment_reply',
                'replied to your comment',
                NEW.parent_comment_id,
                'comment',
                NEW.profile_id
            );
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update comment like notification function to check preferences
CREATE OR REPLACE FUNCTION public.notify_comment_like()
RETURNS TRIGGER AS $$
DECLARE
    should_notify BOOLEAN;
    comment_author_id UUID;
BEGIN
    -- Get comment author
    SELECT profile_id INTO comment_author_id
    FROM public.post_comments
    WHERE id = NEW.comment_id;

    -- Don't notify if user likes their own comment
    IF comment_author_id = NEW.profile_id THEN
        RETURN NEW;
    END IF;

    -- Check if user has enabled notifications for comment likes
    SELECT comment_likes INTO should_notify
    FROM public.notification_preferences
    WHERE profile_id = comment_author_id;

    -- If no preferences found, default to true (notify)
    IF should_notify IS NULL THEN
        should_notify := true;
    END IF;

    IF should_notify THEN
        INSERT INTO public.notifications (
            profile_id,
            type,
            content,
            related_id,
            related_type,
            actor_id
        ) VALUES (
            comment_author_id,
            'comment_like',
            'liked your comment',
            NEW.comment_id,
            'comment',
            NEW.profile_id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;

-- Verify the cleanup was successful
SELECT 'Notification system cleanup completed - preferences will now be respected' as status;
