-- Fix create_enrollment_safely function to match frontend expectations
-- This addresses the parameter mismatch error where frontend sends p_profile_id but function expects p_user_id

BEGIN;

-- Drop the existing function if it exists (try different possible signatures)
DROP FUNCTION IF EXISTS public.create_enrollment_safely(UUID, UUID, TEXT, BOOLEAN);
DROP FUNCTION IF EXISTS public.create_enrollment_safely(UUID, BOOLEAN, UUID, TEXT);
DROP FUNCTION IF EXISTS public.create_enrollment_safely(UUID, BOOLEAN, TEXT, UUID);
DROP FUNCTION IF EXISTS public.create_enrollment_safely(UUID, UUID, BOOLEAN, TEXT);
DROP FUNCTION IF EXISTS public.create_enrollment_safely;

-- Create the function with the correct parameter signature that matches the frontend call
CREATE OR REPLACE FUNCTION public.create_enrollment_safely(
    p_course_id UUID,
    p_profile_id UUID,
    p_request_message TEXT,
    p_gdpr_consent BOOLEAN
) RETURNS VOID AS $$
BEGIN
    -- Check if enrollment exists in a transaction
    PERFORM pg_advisory_xact_lock(hashtext('create_enrollment_' || p_course_id::text || '_' || p_profile_id::text));
    
    -- Check if enrollment already exists using profile_id
    IF EXISTS (
        SELECT 1 
        FROM training_course_enrollments 
        WHERE course_id = p_course_id 
        AND profile_id = p_profile_id
    ) THEN
        RAISE EXCEPTION 'Duplicate enrollment' USING ERRCODE = '23505';
    END IF;

    -- If no enrollment exists, create one
    -- Only use profile_id since user_id column may not exist in the actual database
    INSERT INTO training_course_enrollments (
        course_id,
        profile_id,
        status,
        request_message,
        gdpr_consent
    ) VALUES (
        p_course_id,
        p_profile_id,
        'pending',
        p_request_message,
        p_gdpr_consent
    );

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_enrollment_safely(UUID, UUID, TEXT, BOOLEAN) TO authenticated;

COMMIT;

-- Verify the fix
SELECT 'Enrollment function updated to match frontend parameter expectations' as status;
