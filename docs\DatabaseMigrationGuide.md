# NetZero Platform - Database Migration Guide

## Single Migration Approach

For times when you need to apply a single migration without resetting your entire database, we've created targeted scripts that allow for incremental updates.

### Apply Latest Engagement Score Fix

To apply only the latest engagement score fix without resetting your database:

```powershell
# Apply to local development environment
.\scripts\apply_latest_engagement_fix.ps1

# Or use the comprehensive migration tool
.\scripts\apply_migration.ps1 -MigrationFile "supabase\migrations\20250527020000_fix_engagement_score_and_comment_likes.sql"
```

### Apply to Production

To apply the fix to production:

```powershell
# Set your production database URL
$env:SUPABASE_PROD_DB_URL = "your-production-db-url-here"

# Apply the migration to production
.\scripts\apply_migration.ps1 -MigrationFile "supabase\migrations\20250527020000_fix_engagement_score_and_comment_likes.sql" -Production
```

## General Migration Tool

We now have a comprehensive migration tool that can apply any SQL file:

```powershell
# Apply a specific migration file
.\scripts\apply_migration.ps1 -MigrationFile "path\to\your\migration.sql"

# Apply to production
.\scripts\apply_migration.ps1 -MigrationFile "path\to\your\migration.sql" -Production
```

## Full Database Reset (When Needed)

If you do need to completely reset your database:

```powershell
# Reset the local database and apply all migrations
.\scripts\update_engagement_triggers.ps1

# Or use the Supabase CLI directly
supabase db reset
```

## Benefits of Targeted Migrations

- **Less Disruptive**: Keep your development data intact
- **Faster**: Applying a single migration is much quicker than resetting the entire database
- **Safer for Production**: Can be applied to production without downtime
- **Audit Trail**: All migration executions are logged for reference

## When to Use Each Approach

- **Targeted Migration**: When making small changes to functions, triggers, or adding features that don't require schema changes
- **Full Database Reset**: When making major schema changes or when your local database is in an inconsistent state

## Notes on the Engagement Score Fix

The engagement score fix applies the following changes:
- Updates `update_post_engagement_on_comment_like()` function
- Updates `sync_post_likes_on_change()` function
- Updates `handle_comment_count()` function
- Ensures `comment_likes_count` column exists in `social_posts` table
- Updates the engagement score calculation formula
- Recalculates all existing engagement scores
