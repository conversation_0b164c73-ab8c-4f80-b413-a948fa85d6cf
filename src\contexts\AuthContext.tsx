import { createContext, useContext, useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useToast } from "@/hooks/use-toast";
import { Database } from '@/types/database.types'; // Assuming this file exists and contains Supabase types

type ExtendedUser = User & {
  profile_id?: string;
};

type AuthContextType = {
  user: ExtendedUser | null;
  session: Session | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, firstName?: string, lastName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  ensureProfileId: () => Promise<void>;
};

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<ExtendedUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Helper function to fetch profile_id for a user
  const fetchProfileId = async (userId: string) => {
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('Error fetching profile_id:', profileError);
        return null;
      }

      return profile?.id || null;
    } catch (err) {
      console.error('Error in fetchProfileId:', err);
      return null;
    }
  };  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(      async (event, currentSession) => {
        console.log('Auth state changed:', event);

        // Handle token refresh errors and other auth events
        switch (event) {
          case 'SIGNED_OUT':
            console.log('User signed out');
            setSession(null);
            setUser(null);
            setIsLoading(false);
            return;
          case 'TOKEN_REFRESHED':
            console.log('Token successfully refreshed');
            break;
          case 'USER_UPDATED':
            console.log('User data updated');
            break;
          default:
            // For any session issues, try to recover
            if (!currentSession && session) {
              console.error('Session lost, attempting to recover');

              // Clear any invalid auth data
              localStorage.removeItem('netzero_auth');
              sessionStorage.removeItem('netzero_auth');

              // Try to refresh the session once
              try {
                const { data } = await supabase.auth.refreshSession();
                if (data.session) {
                  console.log('Session successfully refreshed after loss');
                }
              } catch (err) {
                console.error('Failed to refresh session after loss', err);
              }
            }
        }

        // For most events, just set the user without fetching profile_id
        // The profile_id will be set by checkSession() on initial load
        if (currentSession?.user) {
          // Preserve existing profile_id if we have it
          setUser(prev => prev?.profile_id ? { ...currentSession.user, profile_id: prev.profile_id } : currentSession.user);
        } else {
          setUser(null);
        }

        setSession(currentSession);
        setIsLoading(false);
      }
    );

    // THEN check for existing session
    const checkSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Session error:', error);
          // Clear invalid session data
          try {
            localStorage.removeItem('netzero_auth');
            sessionStorage.removeItem('netzero_auth');
            console.log('Auth storage cleared due to session error');
          } catch (err) {
            console.error('Error clearing auth storage:', err);
          }
          setSession(null);
          setUser(null);
          
          // Try to refresh the session
          try {
            const { data, error } = await supabase.auth.refreshSession();
            if (error) {
              console.error('Error refreshing session:', error);
            }
          } catch (err) {
            console.error('Session refresh error:', err);
          }
        } else {
          if (!data.session) {
            console.log('No active session found');
          } else {
            console.log('Active session found');
            const { user } = data.session;
            const profileId = await fetchProfileId(user.id);

            setUser({ ...user, profile_id: profileId });
            setSession(data.session);
          }
        }
      } catch (err) {
        console.error('Error checking session:', err);
        setSession(null);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkSession();

    return () => subscription.unsubscribe();
  }, []);
  const signIn = async (email: string, password: string) => {
    try {      // Clear any existing potentially invalid tokens first
      try {
        localStorage.removeItem('netzero_auth');
        sessionStorage.removeItem('netzero_auth');
        console.log('Auth storage cleared');
      } catch (err) {
        console.error('Error clearing auth storage:', err);
      }
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }
      
      // Verify we got a valid session
      if (!data.session) {
        throw new Error('No session returned after sign in');
      }
      
      console.log('Sign in successful, session established');
      
      // Show success message
      toast({
        title: "Sign in successful",
        description: "Welcome back!",
      });
    } catch (error: any) {
      console.error('Sign in error:', error);
      toast({
        title: "Sign in failed",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    }
  };

  const signUp = async (email: string, password: string, firstName?: string, lastName?: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
          },
        },
      });

      if (error) {
        throw error;
      }

      // If user was created successfully, complete the signup process
      if (data.user) {
        try {
          const { data: completionResult, error: completionError } = await supabase.rpc('complete_user_signup');

          if (completionError) {
            console.error('Signup completion failed:', completionError);
            // Don't throw here - user is created, just log the error
          } else {
            console.log('Signup completed successfully:', completionResult);
          }
        } catch (completionError) {
          console.error('Error completing signup:', completionError);
          // Don't throw here - user is created, just log the error
        }
      }

      toast({
        title: "Sign up successful",
        description: "Welcome! Your account has been created.",
      });
    } catch (error: any) {
      toast({
        title: "Sign up failed",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    }
  };
  const signOut = async () => {
    try {      // First clear any local storage to ensure complete signout
      try {
        localStorage.removeItem('netzero_auth');
        sessionStorage.removeItem('netzero_auth');
        console.log('Auth storage cleared before signout');
      } catch (err) {
        console.error('Error clearing auth storage:', err);
      }
      
      // Then call the API to signout
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }
      
      // Ensure state is cleared
      setUser(null);
      setSession(null);
      
      toast({
        title: "Signed out",
        description: "You have been signed out successfully.",
      });
    } catch (error: any) {
      console.error('Sign out error:', error);
        // Still clear storage and state even if API call fails
      try {
        localStorage.removeItem('netzero_auth');
        sessionStorage.removeItem('netzero_auth');
        console.log('Auth storage cleared after signout error');
      } catch (err) {
        console.error('Error clearing auth storage after signout error:', err);
      }
      setUser(null);
      setSession(null);
      
      toast({
        title: "Sign out issue",
        description: "You've been signed out, but there was an error: " + error.message,
        variant: "destructive",
      });
    }
  };

  const ensureProfileId = async () => {
    if (user && !user.profile_id) {
      const profileId = await fetchProfileId(user.id);
      setUser({ ...user, profile_id: profileId });
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        isLoading,
        signIn,
        signUp,
        signOut,
        ensureProfileId,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};


