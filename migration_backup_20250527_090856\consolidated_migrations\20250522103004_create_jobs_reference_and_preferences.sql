-- Create reference tables for jobs

-- Drop existing tables if they exist
DROP TABLE IF EXISTS public.industries CASCADE;
DROP TABLE IF EXISTS public.net_zero_categories CASCADE;

-- Create industries table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create net zero categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.net_zero_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES public.net_zero_categories(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Insert initial data for industries if empty
INSERT INTO public.industries (name, description)
SELECT * FROM (VALUES
    ('Energy', 'Renewable energy, power generation, and utilities'),
    ('Manufacturing', 'Production and manufacturing processes'),
    ('Construction', 'Building and infrastructure development'),
    ('Transportation', 'Transportation and logistics services'),
    ('Agriculture', 'Agriculture, forestry, and food production'),
    ('Technology', 'Information technology and software'),
    ('Finance', 'Banking, investment, and financial services'),
    ('Consulting', 'Professional services and consulting'),
    ('Education', 'Educational institutions and services'),
    ('Government', 'Public sector and government organizations')
) AS v(name, description)
WHERE NOT EXISTS (SELECT 1 FROM public.industries);

-- Insert initial data for net zero categories if empty
INSERT INTO public.net_zero_categories (name, description)
SELECT * FROM (VALUES
    ('Carbon Reduction', 'Direct reduction of carbon emissions'),
    ('Renewable Energy', 'Clean and renewable energy solutions'),
    ('Energy Efficiency', 'Improving energy usage efficiency'),
    ('Sustainable Transport', 'Low-carbon transportation solutions'),
    ('Waste Management', 'Sustainable waste reduction and management'),
    ('Green Building', 'Sustainable construction and building practices'),
    ('Circular Economy', 'Resource efficiency and waste reduction'),
    ('Natural Solutions', 'Nature-based climate solutions'),
    ('Climate Tech', 'Technology solutions for climate change'),
    ('Policy & Advocacy', 'Climate policy and advocacy work')
) AS v(name, description)
WHERE NOT EXISTS (SELECT 1 FROM public.net_zero_categories);

-- Enable RLS on the tables
ALTER TABLE public.industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.net_zero_categories ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for reading reference data
CREATE POLICY "Anyone can read industries" ON public.industries
    FOR SELECT USING (true);

CREATE POLICY "Anyone can read net zero categories" ON public.net_zero_categories
    FOR SELECT USING (true);

-- Add indexes for frequently queried columns
CREATE INDEX idx_industries_name ON public.industries(name);
CREATE INDEX idx_net_zero_categories_name ON public.net_zero_categories(name);
CREATE INDEX idx_net_zero_categories_parent ON public.net_zero_categories(parent_id);

-- Create junction table for user job preferences
CREATE TABLE IF NOT EXISTS public.user_job_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    industry_id UUID REFERENCES public.industries(id) ON DELETE CASCADE,
    net_zero_category_id UUID REFERENCES public.net_zero_categories(id) ON DELETE CASCADE,
    location_id UUID REFERENCES public.locations(id) ON DELETE CASCADE,
    job_function job_function,
    salary_bracket salary_bracket,
    location_type job_location_type,
    contract_type job_contract_type,
    hours_type job_hours_type,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS on user_job_preferences
ALTER TABLE public.user_job_preferences ENABLE ROW LEVEL SECURITY;

-- Users can see their own preferences
CREATE POLICY "Users can view their own job preferences" ON public.user_job_preferences
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own preferences
CREATE POLICY "Users can insert their own job preferences" ON public.user_job_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own preferences
CREATE POLICY "Users can update their own job preferences" ON public.user_job_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own preferences
CREATE POLICY "Users can delete their own job preferences" ON public.user_job_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- Add indexes for user_job_preferences
CREATE INDEX idx_user_job_preferences_user ON public.user_job_preferences(user_id);
CREATE INDEX idx_user_job_preferences_industry ON public.user_job_preferences(industry_id);
CREATE INDEX idx_user_job_preferences_category ON public.user_job_preferences(net_zero_category_id);
CREATE INDEX idx_user_job_preferences_location ON public.user_job_preferences(location_id);
