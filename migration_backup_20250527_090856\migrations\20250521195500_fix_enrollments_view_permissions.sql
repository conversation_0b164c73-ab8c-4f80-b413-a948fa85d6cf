-- Grant permissions on the view
GRANT SELECT ON training_course_enrollments_view TO authenticated;

-- Fix the view to properly alias the select fields
DROP VIEW IF EXISTS training_course_enrollments_view;
CREATE OR REPLACE VIEW training_course_enrollments_view AS
SELECT 
    e.id,
    e.course_id,
    e.user_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.avatar_url,
    p.title as user_title,
    p.organization as user_organization,
    p.email as user_email,
    t.title as course_title,
    t.organization_name as course_provider
FROM 
    training_course_enrollments e
JOIN 
    profiles p ON e.profile_id = p.id
JOIN 
    training_courses t ON e.course_id = t.id
WHERE 
    auth.uid() = e.user_id OR 
    auth.uid() IN (
        SELECT creator_id 
        FROM training_courses 
        WHERE id = e.course_id
    );
