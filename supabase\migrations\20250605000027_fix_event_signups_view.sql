-- Fix event_signups_with_users view to properly show attendee names
-- The current view is missing the full_name field and using INNER JOIN
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- FIX THE EVENT_SIGNUPS_WITH_USERS VIEW
-- ================================================================

-- Drop the existing view
DROP VIEW IF EXISTS public.event_signups_with_users CASCADE;

-- Recreate the view with proper name handling and LEFT JOIN
CREATE OR REPLACE VIEW public.event_signups_with_users AS
SELECT 
    es.id,
    es.event_id,
    es.user_id,
    es.gdpr_consent,
    es.created_at,
    es.updated_at,
    es.hide_attendance,
    -- Individual name fields
    p.first_name,
    p.last_name,
    p.avatar_url,
    p.title,
    p.organization,
    -- Computed full_name field that the frontend expects
    CASE 
        WHEN p.first_name IS NOT NULL OR p.last_name IS NOT NULL THEN
            COALESCE(
                NULLIF(TRIM(CONCAT(COALESCE(p.first_name, ''), ' ', COALESCE(p.last_name, ''))), ''),
                'Anonymous User'
            )
        ELSE 'Anonymous User'
    END AS full_name,
    -- Event information
    e.title as event_title,
    e.start_date as event_start_date
FROM 
    public.event_signups es
    INNER JOIN public.events e ON es.event_id = e.id
    LEFT JOIN public.profiles p ON es.user_id = p.id;  -- Use LEFT JOIN so users without profiles still show

-- Grant proper permissions to the view
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.event_signups_with_users TO anon;

-- Add comment to explain the view
COMMENT ON VIEW public.event_signups_with_users IS 
'Event signups with user profile information. Uses LEFT JOIN to include users without profiles and computes full_name field.';

-- ================================================================
-- TEST THE VIEW
-- ================================================================

-- Test that the view works and returns proper data
SELECT 
    'EVENT_SIGNUPS_VIEW_TEST' as test_type,
    COUNT(*) as signup_count,
    COUNT(CASE WHEN full_name != 'Anonymous User' THEN 1 END) as named_signups,
    COUNT(CASE WHEN full_name = 'Anonymous User' THEN 1 END) as anonymous_signups
FROM public.event_signups_with_users;

-- Show a sample of the data to verify names are working
SELECT 
    'SAMPLE_SIGNUPS' as test_type,
    user_id,
    first_name,
    last_name,
    full_name,
    hide_attendance
FROM public.event_signups_with_users
LIMIT 5;

COMMIT;
