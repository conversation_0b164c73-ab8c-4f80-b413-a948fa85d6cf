-- Drop the existing view first
DROP VIEW IF EXISTS funding_opportunities_view;

-- Recreate the view with category names
CREATE VIEW funding_opportunities_view AS
SELECT 
  f.id,
  f.created_at,
  f.updated_at,
  f.title,
  f.organization_name,
  f.description,
  f.eligibility,
  f.type,
  f.amount,
  f.deadline,
  f.website_url,
  f.status,
  f.creator_id,
  COALESCE(
    (
      SELECT array_agg(l.name)
      FROM funding_opportunity_regions fr
      JOIN locations l ON l.id::text = fr.location_id::text
      WHERE fr.funding_opportunity_id = f.id
    ), 
    ARRAY[]::text[]
  ) as regions,
  COALESCE(
    (
      SELECT array_agg(nc.name)
      FROM funding_opportunity_categories fc
      JOIN netzero_categories nc ON nc.id::text = fc.category_id::text
      WHERE fc.funding_opportunity_id = f.id
    ),
    ARRAY[]::text[]
  ) as categories
FROM funding_opportunities f
WHERE status = 'Published' OR auth.uid() = creator_id;

-- Grant necessary permissions
GRANT SELECT ON funding_opportunities_view TO authenticated;
GRANT SELECT ON funding_opportunities_view TO anon;
