-- Fix events_with_creator view permissions and definition
-- This migration fixes the permission denied error when accessing events_with_creator view
-- Date: 2025-06-05

BEGIN;

-- ================================================================
-- PART 1: FIX EVENTS_WITH_CREATOR VIEW DEFINITION AND PERMISSIONS
-- ================================================================

-- Drop the existing view that has permission issues
DROP VIEW IF EXISTS public.events_with_creator CASCADE;

-- Recreate the view with proper security settings and without auth.users join
-- Note: We avoid joining user_consent_settings due to RLS restrictions
CREATE OR REPLACE VIEW public.events_with_creator
WITH (security_invoker = on) AS
SELECT
    e.id,
    e.creator_user_id,
    e.title,
    e.start_date,
    e.start_time,
    e.end_date,
    e.end_time,
    e.description,
    e.image_url,
    e.event_type,
    e.physical_location,
    e.meeting_url,
    e.event_category,
    e.tags,
    e.created_at,
    e.updated_at,
    e.industry_ids,
    e.netzero_category_ids,
    e.owner_id,
    e.status,
    e.capacity,
    e.is_private,
    e.category,
    e.industry,
    -- Creator information from profiles table only (no auth.users join)
    -- Show creator info for all profiles (privacy is handled at application level)
    concat(p.first_name, ' ', p.last_name) AS creator_name,
    p.avatar_url AS creator_avatar_url,
    p.email AS creator_email
FROM events e
LEFT JOIN profiles p ON e.creator_user_id = p.id;

-- Grant proper permissions to the view
GRANT SELECT ON public.events_with_creator TO authenticated;
GRANT SELECT ON public.events_with_creator TO anon;

-- Add comment to the view
COMMENT ON VIEW public.events_with_creator IS 
'Events view with creator information. Uses security_invoker for proper permission handling and respects profile visibility settings.';

-- ================================================================
-- PART 2: ENSURE UNDERLYING TABLES HAVE PROPER PERMISSIONS
-- ================================================================

-- Make sure the underlying tables have the necessary permissions
-- Events table permissions
GRANT SELECT ON public.events TO authenticated;
GRANT SELECT ON public.events TO anon;

-- Profiles table permissions (for creator information)
GRANT SELECT ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;

-- ================================================================
-- PART 3: VERIFICATION AND LOGGING
-- ================================================================

-- Test the view permissions
DO $$
DECLARE
    view_exists BOOLEAN;
    permission_count INTEGER;
    sample_event RECORD;
BEGIN
    -- Check if view exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.views 
        WHERE table_schema = 'public' 
        AND table_name = 'events_with_creator'
    ) INTO view_exists;

    -- Count permission grants
    SELECT COUNT(*) INTO permission_count
    FROM information_schema.table_privileges 
    WHERE table_schema = 'public' 
    AND table_name = 'events_with_creator'
    AND privilege_type = 'SELECT';

    RAISE NOTICE 'EVENTS_WITH_CREATOR VIEW VERIFICATION:';
    RAISE NOTICE 'View exists: %', view_exists;
    RAISE NOTICE 'Permission grants count: %', permission_count;
    
    -- Try to select from the view (this will test if it works)
    BEGIN
        SELECT id, title, creator_name INTO sample_event
        FROM public.events_with_creator 
        LIMIT 1;
        
        IF sample_event.id IS NOT NULL THEN
            RAISE NOTICE 'Sample event found: % - %', sample_event.title, sample_event.creator_name;
        ELSE
            RAISE NOTICE 'No events found in view (this is OK if no events exist)';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Error testing view: %', SQLERRM;
    END;
END $$;

COMMIT;
