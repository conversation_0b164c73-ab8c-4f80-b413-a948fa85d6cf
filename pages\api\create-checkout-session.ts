import { NextApiRequest, NextApiResponse } from 'next';
import Strip<PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { tier, type = 'user' } = req.body;
    
    // Get user from session
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    // Get subscription plan details
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', tier)
      .eq('type', type)
      .single();

    if (planError || !plan) {
      return res.status(400).json({ error: 'Invalid subscription plan' });
    }

    if (!plan.stripe_price_id) {
      return res.status(400).json({ error: 'Stripe price ID not configured for this plan' });
    }

    // Get user profile for customer info
    const { data: profile } = await supabase
      .from('profiles')
      .select('first_name, last_name, customer_id')
      .eq('id', user.id)
      .single();

    // Create or get Stripe customer
    let customerId = profile?.customer_id;
    
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email!,
        name: profile ? `${profile.first_name} ${profile.last_name}` : undefined,
        metadata: {
          user_id: user.id,
        },
      });
      customerId = customer.id;

      // Update profile with customer ID
      await supabase
        .from('profiles')
        .update({ customer_id: customerId })
        .eq('id', user.id);
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: plan.stripe_price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${req.headers.origin}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.origin}/billing/cancel`,
      metadata: {
        user_id: user.id,
        tier: tier,
        type: type,
        plan_id: plan.id,
      },
    });

    res.status(200).json({ sessionId: session.id, url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
