-- Test script to manually test the signup function
-- Run this in Supabase SQL Editor to test if the function works

-- First, let's test the function manually by simulating a new user insert
-- Note: This is a test - don't use real user data

DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_first_name TEXT := 'Test';
    test_last_name TEXT := 'User';
BEGIN
    -- Simulate what happens when a new user signs up
    RAISE NOTICE 'Testing signup function with user ID: %', test_user_id;
    
    -- Try to call the handle_new_user function manually
    -- We'll simulate the NEW record that would be passed to the trigger
    BEGIN
        -- Test if we can insert into profiles directly first
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            email, 
            social_visibility, 
            subscription_tier, 
            subscription_status, 
            created_at, 
            updated_at
        )
        VALUES (
            test_user_id,
            test_first_name,
            test_last_name,
            test_email,
            'public',
            'none',
            'trial',
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Profile insert successful';
        
        -- Test user_consent_settings insert
        INSERT INTO public.user_consent_settings (
            user_id,
            share_email_with_event_creators,
            share_email_with_attendees,
            share_contact_details,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            false,
            false,
            false,
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'User consent settings insert successful';
        
        -- Clean up test data
        DELETE FROM public.user_consent_settings WHERE user_id = test_user_id;
        DELETE FROM public.profiles WHERE id = test_user_id;
        
        RAISE NOTICE 'Test completed successfully - signup function should work';
        
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error during test: %', SQLERRM;
        -- Try to clean up in case of partial success
        BEGIN
            DELETE FROM public.user_consent_settings WHERE user_id = test_user_id;
            DELETE FROM public.profiles WHERE id = test_user_id;
        EXCEPTION WHEN OTHERS THEN
            -- Ignore cleanup errors
        END;
    END;
END $$;
