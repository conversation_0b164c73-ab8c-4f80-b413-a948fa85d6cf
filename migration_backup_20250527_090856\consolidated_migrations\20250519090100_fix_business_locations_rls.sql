-- Fix RLS policies for business locations
-- This ensures consistent read access while maintaining security

-- Drop any potentially conflicting policies
DROP POLICY IF EXISTS "Enable read access for all users" ON public.business_service_locations;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.business_service_locations;
DROP POLICY IF EXISTS "Enable read access for service role" ON public.business_service_locations;

-- Create comprehensive policies
-- Allow anyone to read business_service_locations
CREATE POLICY "Enable read access for all users" ON public.business_service_locations
    FOR SELECT
    USING (true);

-- Allow authenticated business owners to manage their own locations
CREATE POLICY "Business owners can manage their service locations" ON public.business_service_locations
    FOR ALL
    TO authenticated
    USING (
        business_id IN (
            SELECT id FROM businesses WHERE owner_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id FROM businesses WHERE owner_id = auth.uid()
        )
    );

-- Allow service role full access
CREATE POLICY "Service role has full access" ON public.business_service_locations
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Grant necessary permissions
GRANT SELECT ON public.business_service_locations TO anon;
GRANT SELECT ON public.business_service_locations TO authenticated;
GRANT ALL ON public.business_service_locations TO service_role;
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;
