-- Debug why notification_preferences aren't being created during signup

-- 1. Check if there are any existing notification_preferences records
SELECT 
    'Existing notification_preferences records:' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT profile_id) as unique_profiles
FROM public.notification_preferences;

-- 2. Check recent profiles and their notification preferences
SELECT 
    'Recent profiles and their notification preferences:' as info,
    p.id as profile_id,
    p.first_name,
    p.last_name,
    p.created_at as profile_created,
    CASE 
        WHEN np.profile_id IS NOT NULL THEN 'HAS_PREFERENCES'
        ELSE 'NO_PREFERENCES'
    END as notification_status
FROM public.profiles p
LEFT JOIN public.notification_preferences np ON p.id = np.profile_id
ORDER BY p.created_at DESC
LIMIT 10;

-- 3. Check if there are any constraints on notification_preferences that might be failing
SELECT 
    'Constraints on notification_preferences:' as info,
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'public.notification_preferences'::regclass;

-- 4. Test if we can manually insert a notification_preferences record
-- (This will help identify if there's a constraint issue)
DO $$
DECLARE
    test_profile_id UUID;
    test_result TEXT;
BEGIN
    -- Get a profile that doesn't have notification preferences
    SELECT p.id INTO test_profile_id
    FROM public.profiles p
    LEFT JOIN public.notification_preferences np ON p.id = np.profile_id
    WHERE np.profile_id IS NULL
    LIMIT 1;
    
    IF test_profile_id IS NOT NULL THEN
        BEGIN
            INSERT INTO public.notification_preferences (
                profile_id,
                email_notifications,
                push_notifications,
                post_likes,
                post_comments,
                comment_replies,
                comment_likes,
                connection_requests,
                connection_accepted,
                event_signups,
                event_updates,
                system_notifications,
                created_at,
                updated_at
            )
            VALUES (
                test_profile_id,
                true, true, true, true, true, true, true, true, true, true, true,
                NOW(), NOW()
            );
            
            test_result := 'SUCCESS: Manual insert worked for profile ' || test_profile_id;
            
            -- Clean up the test record
            DELETE FROM public.notification_preferences WHERE profile_id = test_profile_id;
            
        EXCEPTION WHEN OTHERS THEN
            test_result := 'ERROR: Manual insert failed - ' || SQLERRM;
        END;
    ELSE
        test_result := 'INFO: All profiles already have notification preferences';
    END IF;
    
    RAISE NOTICE '%', test_result;
END $$;

-- 5. Check the current handle_new_user function to see if it's actually running
SELECT 
    'Current handle_new_user function:' as info,
    pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND p.proname = 'handle_new_user';
