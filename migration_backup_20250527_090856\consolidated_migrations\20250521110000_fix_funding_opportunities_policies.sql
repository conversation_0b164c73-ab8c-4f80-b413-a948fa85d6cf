-- Drop existing policies to start fresh
DROP POLICY IF EXISTS "Anyone can view published funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can insert their own funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Authenticated users can insert funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can update their own funding opportunities" ON "public"."funding_opportunities";
DROP POLICY IF EXISTS "Users can delete their own funding opportunities" ON "public"."funding_opportunities";

-- Create a new comprehensive viewing policy that allows:
-- 1. Anyone to see published opportunities
-- 2. Creators to see their own opportunities regardless of status
CREATE POLICY "View funding opportunities policy"
ON "public"."funding_opportunities"
FOR SELECT USING (
    status = 'Published' OR 
    auth.uid() = creator_id
);

-- Create a single, clear insert policy
CREATE POLICY "Users can create funding opportunities"
ON "public"."funding_opportunities"
FOR INSERT
WITH CHECK (
    auth.uid() IS NOT NULL AND
    auth.uid() = creator_id
);

-- Allow users to update their own opportunities
CREATE POLICY "Users can update their own funding opportunities"
ON "public"."funding_opportunities"
FOR UPDATE USING (
    auth.uid() = creator_id
)
WITH CHECK (
    auth.uid() = creator_id
);

-- Allow users to delete their own opportunities
CREATE POLICY "Users can delete their own funding opportunities"
ON "public"."funding_opportunities"
FOR DELETE USING (
    auth.uid() = creator_id
);

-- Grant necessary permissions to roles
GRANT ALL ON "public"."funding_opportunities" TO authenticated;
GRANT SELECT ON "public"."funding_opportunities" TO anon;

-- Enable RLS for the linking tables if not already enabled
ALTER TABLE "public"."funding_opportunity_regions" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."funding_opportunity_categories" ENABLE ROW LEVEL SECURITY;

-- Recreate cleaner policies for regions
DROP POLICY IF EXISTS "Anyone can view funding opportunity regions" ON "public"."funding_opportunity_regions";
DROP POLICY IF EXISTS "Creator can insert funding opportunity regions" ON "public"."funding_opportunity_regions";
DROP POLICY IF EXISTS "Creator can update funding opportunity regions" ON "public"."funding_opportunity_regions";
DROP POLICY IF EXISTS "Creator can delete funding opportunity regions" ON "public"."funding_opportunity_regions";

-- Create simpler region policies
CREATE POLICY "View funding opportunity regions"
ON "public"."funding_opportunity_regions"
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM funding_opportunities fo
        WHERE fo.id = funding_opportunity_id
        AND (fo.status = 'Published' OR fo.creator_id = auth.uid())
    )
);

CREATE POLICY "Manage funding opportunity regions"
ON "public"."funding_opportunity_regions"
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM funding_opportunities fo
        WHERE fo.id = funding_opportunity_id
        AND fo.creator_id = auth.uid()
    )
);

-- Grant necessary permissions for regions
GRANT ALL ON "public"."funding_opportunity_regions" TO authenticated;
GRANT SELECT ON "public"."funding_opportunity_regions" TO anon;

-- Recreate cleaner policies for categories
DROP POLICY IF EXISTS "Anyone can view funding opportunity categories" ON "public"."funding_opportunity_categories";
DROP POLICY IF EXISTS "Creator can insert funding opportunity categories" ON "public"."funding_opportunity_categories";
DROP POLICY IF EXISTS "Creator can update funding opportunity categories" ON "public"."funding_opportunity_categories";
DROP POLICY IF EXISTS "Creator can delete funding opportunity categories" ON "public"."funding_opportunity_categories";

-- Create simpler category policies
CREATE POLICY "View funding opportunity categories"
ON "public"."funding_opportunity_categories"
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM funding_opportunities fo
        WHERE fo.id = funding_opportunity_id
        AND (fo.status = 'Published' OR fo.creator_id = auth.uid())
    )
);

CREATE POLICY "Manage funding opportunity categories"
ON "public"."funding_opportunity_categories"
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM funding_opportunities fo
        WHERE fo.id = funding_opportunity_id
        AND fo.creator_id = auth.uid()
    )
);

-- Grant necessary permissions for categories
GRANT ALL ON "public"."funding_opportunity_categories" TO authenticated;
GRANT SELECT ON "public"."funding_opportunity_categories" TO anon;
