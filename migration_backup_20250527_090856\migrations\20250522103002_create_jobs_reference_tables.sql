-- Create reference tables for jobs

-- Drop existing tables if they exist
DROP TABLE IF EXISTS public.regions CASCADE;
DROP TABLE IF EXISTS public.industries CASCADE;
DROP TABLE IF EXISTS public.net_zero_categories CASCADE;

-- Create regions table
CREATE TABLE IF NOT EXISTS public.regions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    country TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create industries table
CREATE TABLE IF NOT EXISTS public.industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create net zero categories table
CREATE TABLE IF NOT EXISTS public.net_zero_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES public.net_zero_categories(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Insert initial data for regions
INSERT INTO public.regions (name, country) VALUES
    ('London', 'United Kingdom'),
    ('South East', 'United Kingdom'),
    ('South West', 'United Kingdom'),
    ('East of England', 'United Kingdom'),
    ('West Midlands', 'United Kingdom'),
    ('East Midlands', 'United Kingdom'),
    ('Yorkshire and the Humber', 'United Kingdom'),
    ('North West', 'United Kingdom'),
    ('North East', 'United Kingdom'),
    ('Scotland', 'United Kingdom'),
    ('Wales', 'United Kingdom'),
    ('Northern Ireland', 'United Kingdom');

-- Insert initial data for industries
INSERT INTO public.industries (name, description) VALUES
    ('Energy', 'Renewable energy, power generation, and utilities'),
    ('Manufacturing', 'Production and manufacturing processes'),
    ('Construction', 'Building and infrastructure development'),
    ('Transportation', 'Transportation and logistics services'),
    ('Agriculture', 'Agriculture, forestry, and food production'),
    ('Technology', 'Information technology and software'),
    ('Finance', 'Banking, investment, and financial services'),
    ('Consulting', 'Professional services and consulting'),
    ('Education', 'Educational institutions and services'),
    ('Government', 'Public sector and government organizations');

-- Insert initial data for net zero categories
INSERT INTO public.net_zero_categories (name, description) VALUES
    ('Carbon Reduction', 'Direct reduction of carbon emissions'),
    ('Renewable Energy', 'Clean and renewable energy solutions'),
    ('Energy Efficiency', 'Improving energy usage efficiency'),
    ('Sustainable Transport', 'Low-carbon transportation solutions'),
    ('Waste Management', 'Sustainable waste reduction and management'),
    ('Green Building', 'Sustainable construction and building practices'),
    ('Circular Economy', 'Resource efficiency and waste reduction'),
    ('Natural Solutions', 'Nature-based climate solutions'),
    ('Climate Tech', 'Technology solutions for climate change'),
    ('Policy & Advocacy', 'Climate policy and advocacy work');

-- Enable RLS on the tables
ALTER TABLE public.regions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.net_zero_categories ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for reading reference data
CREATE POLICY "Anyone can read regions" ON public.regions
    FOR SELECT USING (true);

CREATE POLICY "Anyone can read industries" ON public.industries
    FOR SELECT USING (true);

CREATE POLICY "Anyone can read net zero categories" ON public.net_zero_categories
    FOR SELECT USING (true);

-- Add indexes for frequently queried columns
CREATE INDEX idx_regions_name ON public.regions(name);
CREATE INDEX idx_industries_name ON public.industries(name);
CREATE INDEX idx_net_zero_categories_name ON public.net_zero_categories(name);
CREATE INDEX idx_net_zero_categories_parent ON public.net_zero_categories(parent_id);
