-- Implement Monetization System for NetZero Platform
-- Date: 2025-01-06

BEGIN;

-- =====================================================
-- 1. CREATE SUBSCRIPTION PLANS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    type TEXT NOT NULL CHECK (type IN ('user', 'business')),
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2),
    stripe_price_id TEXT,
    stripe_product_id TEXT,
    features JSONB NOT NULL DEFAULT '{}',
    limits JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Insert user subscription plans
INSERT INTO public.subscription_plans (name, type, price_monthly, price_yearly, features, limits) VALUES
('seed', 'user', 0.00, 0.00, 
 '{"basic_profile": true, "view_events": true, "join_events": true, "connections": true, "view_businesses": true}',
 '{"events_view_per_month": 5, "events_join_per_month": 2, "connections_per_month": 5, "business_results": 10, "posts_per_month": 0, "messaging": false}'
),
('sapling', 'user', 9.99, 99.99,
 '{"everything_in_seed": true, "unlimited_events": true, "posting": true, "messaging": true, "business_directory": true, "priority_notifications": true, "basic_analytics": true}',
 '{"posts_per_month": 3, "connections_per_month": 50, "messaging_conversations_per_month": 10}'
),
('woodland', 'user', 29.99, 299.99,
 '{"everything_in_sapling": true, "unlimited_posts": true, "unlimited_connections": true, "unlimited_messaging": true, "create_events": true, "advanced_analytics": true, "verified_badge": true, "priority_support": true, "export_data": true}',
 '{"events_create_per_month": 5}'
);

-- Insert business sponsorship plans
INSERT INTO public.subscription_plans (name, type, price_monthly, price_yearly, features, limits) VALUES
('bronze', 'business', 49.99, 499.99,
 '{"basic_listing": true, "posting": true, "events": true, "analytics": true, "contact_forms": true}',
 '{"posts_per_month": 5, "events_per_month": 2}'
),
('silver', 'business', 149.99, 1499.99,
 '{"everything_in_bronze": true, "featured_listing": true, "unlimited_posts": true, "unlimited_events": true, "advanced_analytics": true, "lead_tracking": true, "custom_design": true, "sponsored_content": true}',
 '{"sponsored_content_per_month": 2}'
),
('gold', 'business', 299.99, 2999.99,
 '{"everything_in_silver": true, "homepage_banner": true, "premium_search": true, "unlimited_sponsored_content": true, "custom_branding": true, "account_manager": true, "api_access": true, "white_label_events": true}',
 '{}'
);

-- =====================================================
-- 2. CREATE USAGE TRACKING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.user_usage_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    month_year TEXT NOT NULL, -- Format: 'YYYY-MM'
    posts_count INTEGER DEFAULT 0,
    events_viewed_count INTEGER DEFAULT 0,
    events_joined_count INTEGER DEFAULT 0,
    events_created_count INTEGER DEFAULT 0,
    connections_requested_count INTEGER DEFAULT 0,
    messaging_conversations_count INTEGER DEFAULT 0,
    business_searches_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(user_id, month_year)
);

-- Enable RLS
ALTER TABLE public.user_usage_tracking ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Users can view their own usage" ON public.user_usage_tracking
    FOR ALL TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- =====================================================
-- 3. UPDATE PROFILES TABLE CONSTRAINTS
-- =====================================================

-- Update subscription_tier to use proper enum values
ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS profiles_subscription_tier_check;

ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_subscription_tier_check 
CHECK (subscription_tier IN ('seed', 'sapling', 'woodland'));

-- Update subscription_status to use proper enum values
ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS profiles_subscription_status_check;

ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_subscription_status_check 
CHECK (subscription_status IN ('active', 'cancelled', 'past_due', 'trial', 'incomplete'));

-- Update sponsorship_tier to use proper enum values
ALTER TABLE public.profiles 
DROP CONSTRAINT IF EXISTS profiles_sponsorship_tier_check;

ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_sponsorship_tier_check 
CHECK (sponsorship_tier IN ('none', 'bronze', 'silver', 'gold'));

-- =====================================================
-- 4. UPDATE BUSINESSES TABLE CONSTRAINTS
-- =====================================================

-- Update sponsorship_tier to use proper enum values
ALTER TABLE public.businesses 
DROP CONSTRAINT IF EXISTS businesses_sponsorship_tier_check;

ALTER TABLE public.businesses 
ADD CONSTRAINT businesses_sponsorship_tier_check 
CHECK (sponsorship_tier IN ('none', 'bronze', 'silver', 'gold'));

-- =====================================================
-- 5. CREATE TIER CHECKING FUNCTIONS
-- =====================================================

-- Function to check if user can perform action based on their tier
CREATE OR REPLACE FUNCTION public.can_user_perform_action(
    action_type TEXT,
    user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
DECLARE
    user_tier TEXT;
    current_month TEXT;
    usage_record RECORD;
    plan_limits JSONB;
BEGIN
    -- Get user's subscription tier
    SELECT subscription_tier INTO user_tier
    FROM public.profiles
    WHERE id = user_id;
    
    IF user_tier IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Get plan limits
    SELECT limits INTO plan_limits
    FROM public.subscription_plans
    WHERE name = user_tier AND type = 'user';
    
    -- Get current month usage
    current_month := TO_CHAR(NOW(), 'YYYY-MM');
    
    SELECT * INTO usage_record
    FROM public.user_usage_tracking
    WHERE user_usage_tracking.user_id = can_user_perform_action.user_id 
    AND month_year = current_month;
    
    -- If no usage record, create one
    IF usage_record IS NULL THEN
        INSERT INTO public.user_usage_tracking (user_id, month_year)
        VALUES (can_user_perform_action.user_id, current_month);
        
        SELECT * INTO usage_record
        FROM public.user_usage_tracking
        WHERE user_usage_tracking.user_id = can_user_perform_action.user_id 
        AND month_year = current_month;
    END IF;
    
    -- Check specific action limits
    CASE action_type
        WHEN 'post' THEN
            IF user_tier = 'seed' THEN
                RETURN FALSE;
            ELSIF user_tier = 'sapling' THEN
                RETURN usage_record.posts_count < (plan_limits->>'posts_per_month')::INTEGER;
            ELSE -- woodland
                RETURN TRUE;
            END IF;
            
        WHEN 'create_event' THEN
            IF user_tier IN ('seed', 'sapling') THEN
                RETURN FALSE;
            ELSE -- woodland
                RETURN usage_record.events_created_count < (plan_limits->>'events_create_per_month')::INTEGER;
            END IF;
            
        WHEN 'join_event' THEN
            IF user_tier = 'seed' THEN
                RETURN usage_record.events_joined_count < (plan_limits->>'events_join_per_month')::INTEGER;
            ELSE
                RETURN TRUE;
            END IF;
            
        WHEN 'view_event' THEN
            IF user_tier = 'seed' THEN
                RETURN usage_record.events_viewed_count < (plan_limits->>'events_view_per_month')::INTEGER;
            ELSE
                RETURN TRUE;
            END IF;
            
        WHEN 'connect' THEN
            IF user_tier = 'seed' THEN
                RETURN usage_record.connections_requested_count < (plan_limits->>'connections_per_month')::INTEGER;
            ELSIF user_tier = 'sapling' THEN
                RETURN usage_record.connections_requested_count < (plan_limits->>'connections_per_month')::INTEGER;
            ELSE -- woodland
                RETURN TRUE;
            END IF;
            
        WHEN 'message' THEN
            IF user_tier = 'seed' THEN
                RETURN FALSE;
            ELSIF user_tier = 'sapling' THEN
                RETURN usage_record.messaging_conversations_count < (plan_limits->>'messaging_conversations_per_month')::INTEGER;
            ELSE -- woodland
                RETURN TRUE;
            END IF;
            
        ELSE
            RETURN TRUE;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment usage counter
CREATE OR REPLACE FUNCTION public.increment_usage_counter(
    action_type TEXT,
    user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
DECLARE
    current_month TEXT;
BEGIN
    current_month := TO_CHAR(NOW(), 'YYYY-MM');
    
    -- Insert or update usage record
    INSERT INTO public.user_usage_tracking (user_id, month_year)
    VALUES (user_id, current_month)
    ON CONFLICT (user_id, month_year) DO NOTHING;
    
    -- Increment the appropriate counter
    CASE action_type
        WHEN 'post' THEN
            UPDATE public.user_usage_tracking
            SET posts_count = posts_count + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND month_year = current_month;
            
        WHEN 'create_event' THEN
            UPDATE public.user_usage_tracking
            SET events_created_count = events_created_count + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND month_year = current_month;
            
        WHEN 'join_event' THEN
            UPDATE public.user_usage_tracking
            SET events_joined_count = events_joined_count + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND month_year = current_month;
            
        WHEN 'view_event' THEN
            UPDATE public.user_usage_tracking
            SET events_viewed_count = events_viewed_count + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND month_year = current_month;
            
        WHEN 'connect' THEN
            UPDATE public.user_usage_tracking
            SET connections_requested_count = connections_requested_count + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND month_year = current_month;
            
        WHEN 'message' THEN
            UPDATE public.user_usage_tracking
            SET messaging_conversations_count = messaging_conversations_count + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND month_year = current_month;
            
        WHEN 'business_search' THEN
            UPDATE public.user_usage_tracking
            SET business_searches_count = business_searches_count + 1, updated_at = NOW()
            WHERE user_usage_tracking.user_id = increment_usage_counter.user_id 
            AND month_year = current_month;
    END CASE;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.can_user_perform_action(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_user_perform_action(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.increment_usage_counter(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.increment_usage_counter(TEXT) TO authenticated;

-- =====================================================
-- 6. UPDATE HANDLE_NEW_USER TO SET DEFAULT TIER
-- =====================================================

-- Update the signup function to set default tier to 'seed'
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_profile_id UUID;
BEGIN
  RAISE NOTICE 'handle_new_user triggered for user: %', NEW.id;
  
  -- Insert a new profile record for the new user
  BEGIN
    INSERT INTO public.profiles (
      id, 
      first_name, 
      last_name, 
      email, 
      social_visibility, 
      subscription_tier, 
      subscription_status, 
      created_at, 
      updated_at
    )
    VALUES (
      NEW.id,
      NEW.raw_user_meta_data->>'first_name',
      NEW.raw_user_meta_data->>'last_name',
      NEW.email,
      'public',
      'seed', -- Default to free tier
      'active',
      NEW.created_at,
      NEW.created_at
    )
    RETURNING id INTO new_profile_id;
    
    RAISE NOTICE 'Profile created for user: %', NEW.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'Profile creation failed for user %: %', NEW.id, SQLERRM;
      RETURN NEW;
  END;

  -- Create default user_consent_settings
  BEGIN
    INSERT INTO public.user_consent_settings (
      user_id,
      profile_visibility,
      newsletter_subscription,
      show_businesses,
      show_events,
      show_connections,
      created_at,
      updated_at
    )
    VALUES (
      NEW.id,
      true, true, true, true, true,
      NEW.created_at,
      NEW.created_at
    )
    ON CONFLICT (user_id) DO NOTHING;
    
    RAISE NOTICE 'User consent settings created for user: %', NEW.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'User consent settings creation failed for user %: %', NEW.id, SQLERRM;
  END;

  -- Create notification preferences
  BEGIN
    INSERT INTO public.notification_preferences (
      profile_id,
      email_notifications,
      post_likes,
      post_comments,
      comment_replies,
      comment_likes,
      connection_requests,
      connection_accepted,
      event_signups,
      event_updates,
      system_notifications,
      created_at,
      updated_at
    )
    VALUES (
      new_profile_id,
      true, true, true, true, true, true, true, true, true, true,
      NEW.created_at, NEW.created_at
    )
    ON CONFLICT (profile_id) DO NOTHING;
    
    RAISE NOTICE 'Notification preferences created for user: %', NEW.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'Notification preferences creation failed for user %: %', NEW.id, SQLERRM;
  END;

  RAISE NOTICE 'handle_new_user completed successfully for user: %', NEW.id;
  RETURN NEW;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Critical error in handle_new_user for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;
