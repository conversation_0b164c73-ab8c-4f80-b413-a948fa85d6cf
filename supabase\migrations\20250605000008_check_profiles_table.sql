-- Check profiles table structure to see if it has all required columns
-- Date: 2025-06-05

-- Check profiles table structure
SELECT 
    'profiles_structure' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if profiles table has all the columns the function is trying to insert into
SELECT 
    'profiles_column_check' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'social_visibility' AND table_schema = 'public') 
        THEN 'social_visibility EXISTS' 
        ELSE 'social_visibility MISSING' 
    END as social_visibility_check,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'subscription_tier' AND table_schema = 'public') 
        THEN 'subscription_tier EXISTS' 
        ELSE 'subscription_tier MISSING' 
    END as subscription_tier_check,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'subscription_status' AND table_schema = 'public') 
        THEN 'subscription_status EXISTS' 
        ELSE 'subscription_status MISSING' 
    END as subscription_status_check;

-- Test a minimal profile insert to see if it works
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    error_msg TEXT;
BEGIN
    -- Try minimal profile insert first
    BEGIN
        INSERT INTO public.profiles (id, created_at, updated_at)
        VALUES (test_user_id, NOW(), NOW());
        RAISE NOTICE 'Minimal profile insert: SUCCESS';
        
        -- Clean up
        DELETE FROM public.profiles WHERE id = test_user_id;
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Minimal profile insert: FAILED - %', error_msg;
    END;
    
    -- Try full profile insert like the function does
    BEGIN
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            email, 
            social_visibility, 
            subscription_tier, 
            subscription_status, 
            created_at, 
            updated_at
        )
        VALUES (
            test_user_id,
            'Test',
            'User',
            '<EMAIL>',
            'public',
            'none',
            'trial',
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Full profile insert: SUCCESS';
        
        -- Clean up
        DELETE FROM public.profiles WHERE id = test_user_id;
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Full profile insert: FAILED - %', error_msg;
    END;
END $$;
