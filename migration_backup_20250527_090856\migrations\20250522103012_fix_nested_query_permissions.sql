-- Drop existing policies
DROP POLICY IF EXISTS "Service role can do anything with business relevant industries" ON public.business_relevant_industries;

-- Create comprehensive service role policy
CREATE POLICY "Service role can do anything with business relevant industries"
    ON public.business_relevant_industries
    FOR ALL 
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Grant ALL to service role
GRANT ALL ON public.business_relevant_industries TO service_role;
GRANT USAGE ON SCHEMA public TO service_role;

-- Ensure sequences are accessible
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- Add policies for nested queries
CREATE POLICY "Enable read access for service role on businesses"
    ON public.businesses
    FOR SELECT 
    TO service_role
    USING (true);

CREATE POLICY "Enable read access for service role on business_relevant_industries"
    ON public.business_relevant_industries
    FOR SELECT 
    TO service_role
    USING (true);

CREATE POLICY "Enable read access for service role on industries"
    ON public.industries
    FOR SELECT 
    TO service_role
    USING (true);

-- Grant explicit permissions for all roles involved in the nested query
GRANT SELECT ON public.businesses TO service_role;
GRANT SELECT ON public.business_relevant_industries TO service_role;
GRANT SELECT ON public.industries TO service_role;

-- Add RLS policies for authenticated users for nested queries
CREATE POLICY "Users can view all businesses"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can view all business industries"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can view all industries"
    ON public.industries
    FOR SELECT
    TO authenticated
    USING (true);

-- Grant necessary permissions to authenticated users
GRANT SELECT ON public.businesses TO authenticated;
GRANT SELECT ON public.business_relevant_industries TO authenticated;
GRANT SELECT ON public.industries TO authenticated;

-- Add helpful indexes for nested query performance
CREATE INDEX IF NOT EXISTS idx_business_industries_composite 
    ON public.business_relevant_industries(business_id, industry_id);

-- Verify Foreign Key Relationships
DO $$
BEGIN
    -- Check if the foreign key from business_relevant_industries to businesses exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc 
        JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
        WHERE tc.table_name = 'business_relevant_industries' 
        AND tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'businesses'
    ) THEN
        RAISE NOTICE 'Foreign key from business_relevant_industries to businesses is missing';
    END IF;

    -- Check if the foreign key from business_relevant_industries to industries exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints tc 
        JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
        WHERE tc.table_name = 'business_relevant_industries' 
        AND tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'industries'
    ) THEN
        RAISE NOTICE 'Foreign key from business_relevant_industries to industries is missing';
    END IF;
END
$$;
