-- Drop all existing policies first to avoid conflicts
DROP POLICY IF EXISTS "Anyone can view business relevant industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Business owners can manage their business industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable write access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for all" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable insert for business owners" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable update/delete for business owners" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Users can view all business industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Users can manage their business industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Service role can do anything with business industries" ON public.business_relevant_industries;

-- Restore original working policies
CREATE POLICY "Enable read access for service role"
    ON public.business_relevant_industries
    FOR SELECT 
    TO service_role
    USING (true);

CREATE POLICY "Enable write access for service role"
    ON public.business_relevant_industries
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Anyone can view business relevant industries"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Business owners can manage their business industries"
    ON public.business_relevant_industries
    FOR ALL
    TO authenticated
    USING (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO service_role;
GRANT ALL ON public.business_relevant_industries TO service_role;
GRANT ALL ON public.businesses TO service_role;
GRANT ALL ON public.industries TO service_role;

GRANT SELECT ON public.business_relevant_industries TO authenticated;
GRANT SELECT ON public.businesses TO authenticated;
GRANT SELECT ON public.industries TO authenticated;

-- For nested queries to work, service role needs these explicit grants
GRANT SELECT ON public.business_relevant_industries TO service_role;
GRANT SELECT ON public.industries TO service_role;

-- Ensure sequences are accessible
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;
