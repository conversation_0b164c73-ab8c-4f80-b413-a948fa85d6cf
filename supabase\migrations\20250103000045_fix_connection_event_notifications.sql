-- Fix connection and event notification functions to respect preferences
-- Date: 2025-01-03

BEGIN;

-- ================================================================
-- PART 1: UPDATE CONNECTION NOTIFICATION FUNCTIONS
-- ================================================================

-- Update connection request notification function to check preferences
CREATE OR REPLACE FUNCTION public.notify_connection_request()
RETURNS TRIGGER AS $$
DECLARE
    should_notify <PERSON>O<PERSON><PERSON><PERSON>;
BEGIN
    -- Don't notify if user sends request to themselves
    IF NEW.user_id = NEW.connection_id THEN
        RETURN NEW;
    END IF;

    -- Only notify on new connection requests
    IF NEW.status = 'pending' AND (OLD IS NULL OR OLD.status != 'pending') THEN
        -- Check if user has enabled notifications for connection requests
        SELECT connection_requests INTO should_notify
        FROM public.notification_preferences
        WHERE profile_id = NEW.connection_id;

        -- If no preferences found, default to true (notify)
        IF should_notify IS NULL THEN
            should_notify := true;
        END IF;

        IF should_notify THEN
            INSERT INTO public.notifications (
                profile_id,
                type,
                content,
                related_id,
                related_type,
                actor_id
            ) VALUES (
                NEW.connection_id,
                'connection_request',
                'sent you a connection request',
                NEW.id,
                'connection',
                NEW.user_id
            );
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update connection accepted notification function to check preferences
CREATE OR REPLACE FUNCTION public.notify_connection_accepted()
RETURNS TRIGGER AS $$
DECLARE
    should_notify BOOLEAN;
BEGIN
    -- Only notify when connection is accepted
    IF NEW.status = 'accepted' AND OLD.status = 'pending' THEN
        -- Check if user has enabled notifications for connection accepted
        SELECT connection_accepted INTO should_notify
        FROM public.notification_preferences
        WHERE profile_id = NEW.user_id;

        -- If no preferences found, default to true (notify)
        IF should_notify IS NULL THEN
            should_notify := true;
        END IF;

        IF should_notify THEN
            INSERT INTO public.notifications (
                profile_id,
                type,
                content,
                related_id,
                related_type,
                actor_id
            ) VALUES (
                NEW.user_id,
                'connection_accepted',
                'accepted your connection request',
                NEW.id,
                'connection',
                NEW.connection_id
            );
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================================================
-- PART 2: UPDATE EVENT NOTIFICATION FUNCTIONS
-- ================================================================

-- Update event signup notification function to check preferences
CREATE OR REPLACE FUNCTION public.notify_event_signup()
RETURNS TRIGGER AS $$
DECLARE
    should_notify BOOLEAN;
    event_creator_profile_id UUID;
BEGIN
    -- Get the creator_profile_id of the event
    SELECT creator_user_id INTO event_creator_profile_id
    FROM public.events
    WHERE id = NEW.event_id;

    -- Don't notify if user signs up for their own event
    IF event_creator_profile_id = NEW.profile_id THEN
        RETURN NEW;
    END IF;

    -- Check if user has enabled notifications for event signups
    SELECT event_signups INTO should_notify
    FROM public.notification_preferences
    WHERE profile_id = event_creator_profile_id;

    -- If no preferences found, default to true (notify)
    IF should_notify IS NULL THEN
        should_notify := true;
    END IF;

    IF should_notify THEN
        INSERT INTO public.notifications (
            profile_id,
            type,
            content,
            related_id,
            related_type,
            actor_id
        ) VALUES (
            event_creator_profile_id,
            'event_signup',
            'signed up for your event',
            NEW.event_id,
            'event',
            NEW.profile_id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================================================
-- PART 3: ENSURE TRIGGERS ARE PROPERLY ATTACHED
-- ================================================================

-- Drop existing triggers if they exist and recreate them
DROP TRIGGER IF EXISTS notify_post_like_trigger ON public.post_likes;
DROP TRIGGER IF EXISTS notify_post_comment_trigger ON public.post_comments;
DROP TRIGGER IF EXISTS notify_comment_like_trigger ON public.comment_likes;
DROP TRIGGER IF EXISTS notify_connection_request_trigger ON public.user_connections;
DROP TRIGGER IF EXISTS notify_connection_accepted_trigger ON public.user_connections;
DROP TRIGGER IF EXISTS notify_event_signup_trigger ON public.event_signups;

-- Create triggers for social media notifications
CREATE TRIGGER notify_post_like_trigger
    AFTER INSERT ON public.post_likes
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_post_like();

CREATE TRIGGER notify_post_comment_trigger
    AFTER INSERT ON public.post_comments
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_post_comment();

CREATE TRIGGER notify_comment_like_trigger
    AFTER INSERT ON public.comment_likes
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_comment_like();

-- Create triggers for connection notifications
CREATE TRIGGER notify_connection_request_trigger
    AFTER INSERT OR UPDATE ON public.user_connections
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_connection_request();

CREATE TRIGGER notify_connection_accepted_trigger
    AFTER UPDATE ON public.user_connections
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_connection_accepted();

-- Create trigger for event signup notifications
CREATE TRIGGER notify_event_signup_trigger
    AFTER INSERT ON public.event_signups
    FOR EACH ROW
    EXECUTE FUNCTION public.notify_event_signup();

-- ================================================================
-- PART 4: UPDATE NOTIFICATION PREFERENCES COMPONENT TYPE
-- ================================================================

-- Create a function to help with notification preference updates
CREATE OR REPLACE FUNCTION public.update_notification_preference(
    preference_type TEXT,
    enabled BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    updated_rows INTEGER;
BEGIN
    -- Validate preference type
    IF preference_type NOT IN (
        'email_notifications', 'post_likes', 'post_comments', 'comment_replies',
        'comment_likes', 'connection_requests', 'connection_accepted',
        'event_signups', 'event_updates', 'system_notifications'
    ) THEN
        RAISE EXCEPTION 'Invalid preference type: %', preference_type;
    END IF;

    -- Update the preference using dynamic SQL
    EXECUTE format(
        'UPDATE public.notification_preferences SET %I = $1, updated_at = NOW() WHERE profile_id = $2',
        preference_type
    ) USING enabled, auth.uid();

    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    -- If no rows were updated, create default preferences
    IF updated_rows = 0 THEN
        INSERT INTO public.notification_preferences (profile_id)
        VALUES (auth.uid())
        ON CONFLICT (profile_id) DO NOTHING;
        
        -- Try the update again
        EXECUTE format(
            'UPDATE public.notification_preferences SET %I = $1, updated_at = NOW() WHERE profile_id = $2',
            preference_type
        ) USING enabled, auth.uid();
    END IF;

    RETURN true;
END;
$$;

COMMIT;

-- Verify the connection and event notification fixes
SELECT 'Connection and event notification preferences are now properly enforced' as status;
