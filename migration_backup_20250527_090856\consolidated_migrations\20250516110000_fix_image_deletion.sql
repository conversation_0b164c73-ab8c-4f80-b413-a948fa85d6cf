-- First, drop the existing trigger that uses pg_net
DROP TRIGGER IF EXISTS trigger_delete_cloudflare_image ON social_posts;
DROP TRIGGER IF EXISTS trigger_delete_cloudflare_image ON profiles;
DROP TRIGGER IF EXISTS trigger_delete_cloudflare_image ON businesses;

-- Drop the function that uses pg_net
DROP FUNCTION IF EXISTS delete_cloudflare_image();

-- Create a new function that just marks images for deletion
CREATE OR REPLACE FUNCTION mark_image_for_deletion()
RETURNS TRIGGER AS $$
BEGIN
    -- Store the image ID in a cleanup queue table
    IF TG_OP = 'DELETE' AND OLD.media_type = 'image' AND OLD.media_url IS NOT NULL THEN
        INSERT INTO cleanup_queue (resource_type, resource_id, resource_url)
        VALUES ('image', OLD.id, OLD.media_url);
    END IF;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the cleanup queue table if it doesn't exist
CREATE TABLE IF NOT EXISTS cleanup_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_type TEXT NOT NULL,
    resource_id UUID NOT NULL,
    resource_url TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    error TEXT
);

-- Create an index on processed_at to quickly find unprocessed items
CREATE INDEX IF NOT EXISTS idx_cleanup_queue_processed 
ON cleanup_queue (processed_at) 
WHERE processed_at IS NULL;

-- Add new triggers that use the mark_image_for_deletion function
CREATE TRIGGER trigger_mark_image_for_deletion
    BEFORE DELETE ON social_posts
    FOR EACH ROW
    WHEN (OLD.media_type = 'image' AND OLD.media_url IS NOT NULL)
    EXECUTE FUNCTION mark_image_for_deletion();

CREATE TRIGGER trigger_mark_profile_image_for_deletion
    BEFORE DELETE ON profiles
    FOR EACH ROW
    WHEN (OLD.avatar_url IS NOT NULL)
    EXECUTE FUNCTION mark_image_for_deletion();

CREATE TRIGGER trigger_mark_business_image_for_deletion
    BEFORE DELETE ON businesses
    FOR EACH ROW
    WHEN (OLD.logo_url IS NOT NULL)
    EXECUTE FUNCTION mark_image_for_deletion();

-- Grant permissions
GRANT SELECT, INSERT ON cleanup_queue TO authenticated;
GRANT EXECUTE ON FUNCTION mark_image_for_deletion() TO authenticated;
