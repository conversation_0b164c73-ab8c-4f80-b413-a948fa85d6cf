# User Subscription and Business Verification Implementation Plan

This document outlines the plan to implement user subscription tiers and business verification/sponsorship features for the NetZero Platform.

## Overview

### User Subscription Tiers
We will implement a three-tier subscription model:
1. **Free** - Basic access with limited features
2. **Premium** - Enhanced access with more features
3. **Enterprise** - Full access with advanced features and support

### Business Verification & Sponsorship
We will implement two additional business features:
1. **Verification** - To validate business credentials and display a verification badge
2. **Sponsorship Tiers** - To allow businesses to promote themselves on the platform (Bronze, Silver, Gold)

## Database Changes

### Profiles Table Modifications
1. Update the profiles table to track subscription status:
```sql
ALTER TABLE public.profiles
  ADD COLUMN subscription_tier TEXT NOT NULL DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium', 'enterprise')),
  ADD COLUMN subscription_start_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN subscription_end_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'canceled', 'expired', 'trial')),
  ADD COLUMN payment_method_id TEXT,
  ADD COLUMN customer_id TEXT;
```

### Businesses Table Modifications
1. Add verification and sponsorship columns:
```sql
ALTER TABLE public.businesses
  ADD COLUMN is_verified BOOLEAN NOT NULL DEFAULT false,
  ADD COLUMN verification_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN verification_document_url TEXT,
  ADD COLUMN sponsorship_tier TEXT CHECK (sponsorship_tier IN ('none', 'bronze', 'silver', 'gold')),
  ADD COLUMN sponsorship_start_date TIMESTAMP WITH TIME ZONE,
  ADD COLUMN sponsorship_end_date TIMESTAMP WITH TIME ZONE;
```

2. Create a view to easily filter verified and sponsored businesses:
```sql
CREATE OR REPLACE VIEW public.featured_businesses AS
SELECT 
  *,
  CASE 
    WHEN sponsorship_tier = 'gold' THEN 3
    WHEN sponsorship_tier = 'silver' THEN 2 
    WHEN sponsorship_tier = 'bronze' THEN 1
    ELSE 0
  END as sponsorship_level
FROM 
  public.businesses
WHERE 
  sponsorship_tier IS NOT NULL 
  AND sponsorship_tier != 'none'
  AND sponsorship_start_date <= NOW()
  AND (sponsorship_end_date IS NULL OR sponsorship_end_date > NOW())
ORDER BY
  sponsorship_level DESC,
  sponsorship_start_date DESC;
```

## Feature Implementation

### 1. User Subscription Features

#### Free Tier Limits
- Limited posting (5 posts)
- Basic user profile 
- Access to one directory section
- View public content
- Limited networking

#### Premium Tier Features
- Up to 100 posts
- Full media upload capabilities
- Access to all directories
- Advanced analytics
- Featured listings
- Priority networking
- 24/7 support

#### Enterprise Tier Features
- Unlimited posts
- All Premium features
- API access
- Dedicated account manager
- Custom branding
- Team management
- Advanced integrations
- Enterprise-level support

### 2. Business Verification Process

#### Verification Application Process
1. Business owners can apply for verification from their business profile
2. Required to upload documentation proving business authenticity
3. Admin review of submitted documents
4. Once approved, business receives verified badge

#### Verification Implementation
- Add verification request form component
- Create admin interface for reviewing requests
- Implement document upload for verification documents
- Add verification badge display on business profiles

### 3. Business Sponsorship Tiers

#### Bronze Tier
- Basic featured listing in one category
- Small badge on business profile
- Appears in sponsored businesses section

#### Silver Tier
- Featured listing in multiple categories
- Medium badge on business profile
- Higher placement in search results
- Logo displayed in relevant event pages

#### Gold Tier
- Featured listing in all categories
- Premium badge on business profile
- Top placement in search results
- Logo featured on the homepage
- Featured in the newsletter

## Technical Implementation Tasks

### Database
- [ ] Create migration script for profiles table changes
- [ ] Create migration script for businesses table changes
- [ ] Create views for featured businesses
- [ ] Add indexes for efficient querying of subscription and sponsor status

### Backend
- [ ] Update profile types in TypeScript
- [ ] Update business types in TypeScript
- [ ] Create APIs for subscription management
- [ ] Create APIs for verification request handling
- [ ] Create APIs for sponsorship management
- [ ] Implement subscription-based access control logic

### Frontend
- [ ] Create subscription management page
- [ ] Add subscription tier indicator to user profiles
- [ ] Create verification request form
- [ ] Add verification badge to business profiles
- [ ] Create sponsorship management interface
- [ ] Add sponsor badge displays to business profiles
- [ ] Update business directory to highlight sponsored businesses
- [ ] Modify search/filtering to include verified/sponsored filters

### Payment Integration
- [ ] Integrate payment processor (e.g., Stripe)
- [ ] Set up subscription plans in payment system
- [ ] Implement subscription lifecycle management
- [ ] Create payment history and invoice views

## UI Changes

### User Profile
- Add subscription tier badge
- Add subscription management section
- Show subscription-gated features

### Business Profile
- Add verification badge for verified businesses
- Add sponsorship badge showing tier
- Add verification application button for unverified businesses
- Add sponsorship management section for business owners

### Directory & Search
- Add filter options for verified businesses
- Add highlighted sections for sponsored businesses
- Sort options that can prioritize verified/sponsored businesses

## Testing Plan

### Subscription Testing
- [ ] Test subscription purchase flow
- [ ] Test subscription cancellation
- [ ] Test subscription tier changes
- [ ] Verify feature access based on subscription tier

### Verification Testing
- [ ] Test verification application process
- [ ] Test verification approval workflow
- [ ] Test verification badge display
- [ ] Test verification filtering in search

### Sponsorship Testing
- [ ] Test sponsorship purchase flow
- [ ] Test sponsorship visibility in directories
- [ ] Test sponsorship badge displays
- [ ] Test sponsorship filtering in search

## Deployment Plan

1. Deploy database changes
2. Deploy backend API changes
3. Deploy frontend changes for subscription management
4. Deploy verification and sponsorship UI components
5. Test in staging environment
6. Deploy to production

## Rollout Strategy

1. **Phase 1**: Launch subscription tiers with promotional period
2. **Phase 2**: Introduce business verification system
3. **Phase 3**: Launch business sponsorship tiers

## Monitoring and Analytics

- Track conversion rates between subscription tiers
- Monitor verification application success rates
- Track sponsorship tier conversion rates
- Analyze user engagement with sponsored content

## Future Enhancements

- Special event pricing for sponsored businesses
- Referral program for subscriptions
- Enhanced analytics dashboard for business sponsors
- Customizable business profile features for sponsors
