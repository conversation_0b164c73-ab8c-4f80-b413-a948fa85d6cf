# Social Feed Component Cleanup Plan

Based on our assessment of the codebase, here's a cleanup plan to consolidate redundant components and ensure everything works together properly.

## Identified Redundancies

### Components

1. **Post.tsx vs SocialFeedPost.tsx**:
   - Both components serve a similar purpose (displaying a social post)
   - Recommendation: Keep `SocialFeedPost.tsx` and remove `Post.tsx`

### Hooks

1. **Multiple versions of the same hooks**:
   - `usePostComments.ts` and `usePostComments.tsx`
   - `usePostLikes.ts` and `usePostLikes.tsx`
   - `useUserConnections.ts` and `useUserConnections.tsx`
   - Recommendation: Keep the `.tsx` versions which are more complete

## Cleanup Steps

### 1. Consolidate Hooks

For each pair of duplicate hooks:
1. Compare the implementations to ensure the `.tsx` version contains all functionality
2. Check for any imports pointing to the `.ts` versions
3. Update imports to point to the `.tsx` versions
4. Remove the `.ts` files

```powershell
# After verifying functionality, remove the duplicate .ts files
Remove-Item -Path c:\Users\<USER>\Projects\netzeroplatformv1\src\hooks\social\usePostComments.ts
Remove-Item -Path c:\Users\<USER>\Projects\netzeroplatformv1\src\hooks\social\usePostLikes.ts
Remove-Item -Path c:\Users\<USER>\Projects\netzeroplatformv1\src\hooks\social\useUserConnections.ts
```

### 2. Remove Redundant Components

1. Check for any imports pointing to `Post.tsx`
2. Update those imports to use `SocialFeedPost.tsx` instead
3. Remove `Post.tsx`

```powershell
# After verifying no imports point to Post.tsx
Remove-Item -Path c:\Users\<USER>\Projects\netzeroplatformv1\src\components\social\Post.tsx
```

### 3. Ensure Database Connections

For each component/hook that interacts with the database:
1. Verify the component properly imports and uses the Supabase client
2. Test the component to ensure it correctly reads/writes data
3. Fix any database connection issues

## Component Hierarchy

After cleanup, our component hierarchy should be:

```
SocialFeed (Page)
├─ PostCreator
│  └─ PostCategorySelector
├─ CategoryFilter
├─ SocialFeedPost
│  ├─ PostComment
│  └─ PostCategoriesDisplay
└─ Sidebars
   ├─ TrendingSidebar
   └─ ConnectionsSidebar
```

## Hook Dependencies

```
useSocialFeed
├─ usePostLikes.tsx
└─ usePostComments.tsx
    └─ useRealtimeSubscription.ts
```

## Implementation Test Plan

After completing the cleanup:

1. Test post creation flow
2. Test post viewing and feed loading
3. Test commenting on posts
4. Test liking posts and comments
5. Test category filtering
6. Test connection management

Any issues discovered during testing should be fixed before considering the cleanup complete.

## Check TypeScript Types

Ensure all components properly use the types defined in `src/types/social.ts`, especially:
- PostData
- Comment
- Category
- CurrentUser
