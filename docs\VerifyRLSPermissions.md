# Verifying RLS Permissions in NetZeroHub

This document describes how to verify that the Row Level Security (RLS) permissions are set up correctly in your Supabase database for the NetZeroHub platform.

## Background

We encountered issues with authenticated users not being able to access the `profiles` and `user_locations` tables due to RLS permission problems. The issues were addressed in the following scripts:

1. `FixProfileRLSPermissions.sql` - Initial fix for profile permissions
2. `FixRemainingRLSPermissions.sql` - Comprehensive fix for both profiles and user_locations tables

## Verification Steps

### 1. Run Verification SQL in Supabase Dashboard

Log in to the Supabase dashboard at https://supabase.com/dashboard/project/psowjyllxqzllhbiyjtn and go to the SQL Editor. Run the following SQL query to verify permissions:

```sql
SELECT 
    tablename, 
    has_table_privilege('anon', 'public.' || tablename, 'SELECT') AS anon_select,
    has_table_privilege('authenticated', 'public.' || tablename, 'SELECT') AS auth_select
FROM 
    pg_tables 
WHERE 
    schemaname = 'public' AND 
    tablename IN ('profiles', 'user_locations', 'locations');

-- Also check the policies
SELECT 
    tablename, 
    policyname,
    roles,
    cmd
FROM 
    pg_policies
WHERE 
    schemaname = 'public' AND 
    tablename IN ('profiles', 'user_locations');
```

Expected results:
- Both `profiles` and `user_locations` should show `true` for `auth_select`
- There should be policies for both tables that allow authenticated users to perform SELECT operations
- There should be policies that allow users to modify their own data

### 2. Test in the Application

1. **Sign up a new user** or log in with an existing account
2. **Access the profile page** - This should load your profile data without errors
3. **Try to update your profile** - You should be able to modify and save your profile information
4. **Try to add or view locations** - Location-related features should work properly

### 3. Check Browser Console

Open the browser developer console while using the application to see if there are any permission-related errors:

- No "permission denied" errors should appear
- No "RLS policy" errors should appear

## Troubleshooting

If you're still experiencing permission issues:

1. Check that the RLS policies are applied correctly by running the verification SQL above
2. Ensure that your application is using the correct authentication token in requests
3. Verify that the table structures match what's expected in the application code
4. Check that the user ID in the JWT token matches the `id` in profiles or `user_id` in user_locations

## Contact

For persistent issues, please report them to the development team.
