-- Simple step-by-step debug script
-- Run each section separately in Supabase SQL Editor

-- STEP 1: Check if tables exist
SELECT
    tablename,
    schemaname
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('user_categories', 'netzero_categories')
ORDER BY tablename;

-- STEP 2: Count rows in netzero_categories
SELECT COUNT(*) as category_count FROM public.netzero_categories;

-- STEP 3: Show first 5 categories
SELECT id, name, parent_id FROM public.netzero_categories LIMIT 5;

-- STEP 4: Check current user ID
SELECT auth.uid() as current_user_id;

-- STEP 5: Count user categories for current user
SELECT COUNT(*) as my_categories FROM public.user_categories WHERE user_id = auth.uid();
