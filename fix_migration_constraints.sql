-- =====================================================
-- FIX MIGRATION CONSTRAINTS ISSUE
-- =====================================================
-- Fix the enum constraint issue for blocked status

-- =====================================================
-- 1. CHECK CURRENT CONSTRAINT
-- =====================================================

-- Check what constraint exists on social_connections.status
SELECT 
    '=== CURRENT STATUS CONSTRAINT ===' as check_phase,
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'social_connections'::regclass 
AND contype = 'c'
AND pg_get_constraintdef(oid) LIKE '%status%';

-- =====================================================
-- 2. DROP AND RECREATE CONSTRAINT IF NEEDED
-- =====================================================

-- Drop the existing status constraint
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find the constraint name
    SELECT conname INTO constraint_name
    FROM pg_constraint 
    WHERE conrelid = 'social_connections'::regclass 
    AND contype = 'c'
    AND pg_get_constraintdef(oid) LIKE '%status%'
    LIMIT 1;
    
    -- Drop it if it exists
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE social_connections DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END IF;
END $$;

-- Add the correct constraint that includes 'blocked'
ALTER TABLE social_connections 
ADD CONSTRAINT social_connections_status_check 
CHECK (status IN ('pending', 'connected', 'declined', 'blocked'));

-- =====================================================
-- 3. VERIFY CONSTRAINT IS CORRECT
-- =====================================================

-- Check the new constraint
SELECT 
    '=== NEW STATUS CONSTRAINT ===' as check_phase,
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'social_connections'::regclass 
AND contype = 'c'
AND pg_get_constraintdef(oid) LIKE '%status%';

-- =====================================================
-- 4. TEST THE CONSTRAINT
-- =====================================================

-- Test that all statuses work
DO $$
BEGIN
    -- Test each status value
    PERFORM 1 WHERE 'pending' IN ('pending', 'connected', 'declined', 'blocked');
    PERFORM 1 WHERE 'connected' IN ('pending', 'connected', 'declined', 'blocked');
    PERFORM 1 WHERE 'declined' IN ('pending', 'connected', 'declined', 'blocked');
    PERFORM 1 WHERE 'blocked' IN ('pending', 'connected', 'declined', 'blocked');
    
    RAISE NOTICE 'All status values are valid';
END $$;

-- =====================================================
-- 5. SAFE MIGRATION WITH PROPER STATUS MAPPING
-- =====================================================

-- Now run the migration with proper status mapping
INSERT INTO social_connections (
    requester_id, 
    recipient_id, 
    status, 
    created_at, 
    updated_at, 
    connected_at
)
SELECT 
    uc.user_id as requester_id,
    uc.connection_id as recipient_id,
    CASE 
        WHEN uc.status = 'accepted' THEN 'connected'
        WHEN uc.status = 'pending' THEN 'pending'
        WHEN uc.status = 'rejected' THEN 'declined'
        WHEN uc.status = 'blocked' THEN 'blocked'
        ELSE 'pending'
    END as status,
    COALESCE(uc.created_at, now()) as created_at,
    COALESCE(uc.updated_at, now()) as updated_at,
    CASE 
        WHEN uc.status = 'accepted' THEN COALESCE(uc.updated_at, uc.created_at, now())
        ELSE NULL 
    END as connected_at
FROM user_connections uc
WHERE NOT EXISTS (
    SELECT 1 FROM social_connections sc 
    WHERE (
        (sc.requester_id = uc.user_id AND sc.recipient_id = uc.connection_id)
        OR 
        (sc.requester_id = uc.connection_id AND sc.recipient_id = uc.user_id)
    )
)
AND EXISTS (SELECT 1 FROM profiles WHERE id = uc.user_id)
AND EXISTS (SELECT 1 FROM profiles WHERE id = uc.connection_id)
AND uc.user_id != uc.connection_id;

-- =====================================================
-- 6. VERIFICATION
-- =====================================================

-- Check migration results
SELECT 
    '=== MIGRATION VERIFICATION ===' as phase,
    'Old system total' as metric,
    COUNT(*) as count
FROM user_connections
UNION ALL
SELECT 
    '=== MIGRATION VERIFICATION ===',
    'New system total',
    COUNT(*)
FROM social_connections
UNION ALL
SELECT 
    '=== MIGRATION VERIFICATION ===',
    'Connected relationships',
    COUNT(*)
FROM social_connections 
WHERE status = 'connected'
UNION ALL
SELECT 
    '=== MIGRATION VERIFICATION ===',
    'Blocked relationships',
    COUNT(*)
FROM social_connections 
WHERE status = 'blocked';

-- Show sample data
SELECT 
    '=== SAMPLE MIGRATED DATA ===' as sample,
    sc.status,
    COUNT(*) as count
FROM social_connections sc
GROUP BY sc.status
ORDER BY sc.status;

SELECT 
    '✅ CONSTRAINT FIXED AND MIGRATION COMPLETE!' as status,
    'All status values now supported' as message,
    'Ready to continue with full migration' as next_step;
