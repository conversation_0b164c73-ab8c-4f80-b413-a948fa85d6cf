-- Debug the signup function to see what's failing
-- Date: 2025-06-05

-- First, let's check what the current function looks like
SELECT routine_definition 
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- Check if the profiles table has all the columns we're trying to insert into
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if user_consent_settings table exists and has the expected columns
SELECT 
    'user_consent_settings_check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'user_consent_settings' 
            AND table_schema = 'public'
        ) THEN 'EXISTS'
        ELSE 'MISSING'
    END as table_status;

-- If user_consent_settings exists, check its structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'user_consent_settings' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Test the function manually with a fake user record
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
    test_email TEXT := '<EMAIL>';
    test_metadata JSONB := '{"first_name": "Test", "last_name": "User"}';
    error_msg TEXT;
BEGIN
    -- Try to manually execute what the function should do
    RAISE NOTICE 'Testing with user_id: %', test_user_id;
    
    -- Test 1: Insert into profiles
    BEGIN
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            email, 
            social_visibility, 
            subscription_tier, 
            subscription_status, 
            created_at, 
            updated_at
        )
        VALUES (
            test_user_id,
            test_metadata->>'first_name',
            test_metadata->>'last_name',
            test_email,
            'public',
            'none',
            'trial',
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Profile insert: SUCCESS';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Profile insert: FAILED - %', error_msg;
    END;
    
    -- Test 2: Insert into user_consent_settings
    BEGIN
        INSERT INTO public.user_consent_settings (
            user_id,
            profile_visibility,
            email_notifications,
            newsletter_subscription,
            show_businesses,
            show_events,
            show_connections,
            share_email_with_event_creators,
            share_email_with_attendees,
            share_contact_details,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            true, true, false, true, true, true, false, false, false,
            NOW(), NOW()
        );
        RAISE NOTICE 'User consent settings insert: SUCCESS';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'User consent settings insert: FAILED - %', error_msg;
    END;
    
    -- Test 3: Insert into notification_preferences
    BEGIN
        INSERT INTO public.notification_preferences (
            profile_id,
            email_notifications,
            post_likes,
            post_comments,
            comment_replies,
            comment_likes,
            connection_requests,
            connection_accepted,
            event_signups,
            event_updates,
            system_notifications,
            created_at,
            updated_at
        )
        VALUES (
            test_user_id,
            true, true, true, true, true, true, true, true, true, true,
            NOW(), NOW()
        );
        RAISE NOTICE 'Notification preferences insert: SUCCESS';
    EXCEPTION
        WHEN OTHERS THEN
            error_msg := SQLERRM;
            RAISE NOTICE 'Notification preferences insert: FAILED - %', error_msg;
    END;
    
    -- Clean up test records
    DELETE FROM public.notification_preferences WHERE profile_id = test_user_id;
    DELETE FROM public.user_consent_settings WHERE user_id = test_user_id;
    DELETE FROM public.profiles WHERE id = test_user_id;
    
    RAISE NOTICE 'Test completed and cleaned up';
END $$;
