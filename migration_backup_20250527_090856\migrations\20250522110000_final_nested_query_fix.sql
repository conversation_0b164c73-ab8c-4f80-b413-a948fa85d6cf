-- This is a comprehensive final fix for the nested query issues
-- It ensures proper foreign keys, RLS policies, and permissions

-- Step 1: Make sure tables exist and have proper structure

-- First, let's fix any potential issues with the business_relevant_industries table
DO $$
BEGIN
    -- Drop and recreate the table if it exists with problems
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'business_relevant_industries'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'business_relevant_industries' 
        AND column_name = 'business_id'
    ) THEN
        -- Table exists but doesn't have proper columns
        DROP TABLE public.business_relevant_industries;
    END IF;
END $$;

-- Recreate the business_relevant_industries table if needed
CREATE TABLE IF NOT EXISTS public.business_relevant_industries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID NOT NULL,
    industry_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(business_id, industry_id)
);

-- Step 2: Add foreign keys

-- Add foreign key constraints if they don't exist
DO $$
BEGIN
    -- Add foreign key to businesses if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.key_column_usage kcu ON kcu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND kcu.table_schema = 'public'
        AND kcu.table_name = 'business_relevant_industries'
        AND kcu.column_name = 'business_id'
        AND ccu.table_schema = 'public'
        AND ccu.table_name = 'businesses'
        AND ccu.column_name = 'id'
    ) THEN
        ALTER TABLE public.business_relevant_industries 
        ADD CONSTRAINT fk_business_relevant_industries_business_id 
        FOREIGN KEY (business_id) REFERENCES public.businesses(id) ON DELETE CASCADE;
    END IF;
    
    -- Add foreign key to industries if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints tc
        JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.key_column_usage kcu ON kcu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND kcu.table_schema = 'public'
        AND kcu.table_name = 'business_relevant_industries'
        AND kcu.column_name = 'industry_id'
        AND ccu.table_schema = 'public'
        AND ccu.table_name = 'industries'
        AND ccu.column_name = 'id'
    ) THEN
        ALTER TABLE public.business_relevant_industries 
        ADD CONSTRAINT fk_business_relevant_industries_industry_id 
        FOREIGN KEY (industry_id) REFERENCES public.industries(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_relevant_industries_business_id 
ON public.business_relevant_industries(business_id);

CREATE INDEX IF NOT EXISTS idx_business_relevant_industries_industry_id 
ON public.business_relevant_industries(industry_id);

-- Step 3: Fix RLS policies

-- First enable RLS on all tables
ALTER TABLE public.business_relevant_industries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.industries ENABLE ROW LEVEL SECURITY;

-- Clear all existing policies to avoid conflicts
DROP POLICY IF EXISTS "Anyone can view business relevant industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Business owners can manage their business industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable read access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Enable write access for service role" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Service role can do anything with business relevant industries" ON public.business_relevant_industries;
DROP POLICY IF EXISTS "Anyone can view industries" ON public.industries;
DROP POLICY IF EXISTS "Enable read access for service role on industries" ON public.industries;

-- Create PUBLIC access policies for industries (critical for nested queries)
CREATE POLICY "Anyone can view industries"
    ON public.industries
    FOR SELECT
    TO PUBLIC
    USING (true);

-- Create comprehensive policies for business_relevant_industries
CREATE POLICY "Anyone can view business relevant industries"
    ON public.business_relevant_industries
    FOR SELECT
    TO PUBLIC
    USING (true);

CREATE POLICY "Business owners can manage their business industries"
    ON public.business_relevant_industries
    FOR ALL
    TO authenticated
    USING (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

-- Create service role policies (critical for nested queries)
CREATE POLICY "Enable read access for service role"
    ON public.business_relevant_industries
    FOR SELECT
    TO service_role
    USING (true);

CREATE POLICY "Enable write access for service role"
    ON public.business_relevant_industries
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Step 4: Grant necessary permissions

-- Grant permissions to authenticated users
GRANT SELECT ON public.business_relevant_industries TO authenticated;
GRANT SELECT ON public.industries TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.business_relevant_industries TO authenticated;

-- Grant full permissions to service_role (critical for nested queries)
GRANT ALL ON public.business_relevant_industries TO service_role;
GRANT ALL ON public.industries TO service_role;
GRANT ALL ON public.businesses TO service_role;
GRANT USAGE ON SCHEMA public TO service_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- Step 5: Test the nested query
DO $$
BEGIN
    RAISE NOTICE 'Nested query should now be working properly:';
    RAISE NOTICE 'Try: /rest/v1/businesses?select=*,business_relevant_industries(industries(id,name))';
END $$;
