-- Check if there's a custom enum type for status
-- Run this in Supabase SQL Editor

-- 1. Check for custom enum types
SELECT 
    'Custom enum types' as section,
    t.typname as enum_name,
    e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname LIKE '%status%' OR t.typname LIKE '%connection%'
ORDER BY t.typname, e.enumsortorder;

-- 2. Check the exact column definition
SELECT 
    'Column definition' as section,
    column_name,
    data_type,
    udt_name,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'user_connections'
AND column_name = 'status'
AND table_schema = 'public';

-- 3. Check what status values actually exist in the data
SELECT 
    'Actual status values' as section,
    status,
    COUNT(*) as count
FROM public.user_connections
GROUP BY status
ORDER BY count DESC;
