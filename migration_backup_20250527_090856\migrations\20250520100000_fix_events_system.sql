-- Events System Fix Script
-- This script ensures that the events system database objects are properly created
-- Run this if events are not displaying on the Events page

-- First check if the events table exists and create it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_catalog.pg_tables
    WHERE schemaname = 'public' AND tablename = 'events'
  ) THEN
    RAISE NOTICE 'Creating events table';
    
    -- Create events table
    CREATE TABLE "public"."events" (
      "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
      "creator_user_id" uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      "title" text NOT NULL,
      "start_date" date NOT NULL,
      "start_time" time WITHOUT TIME ZONE NOT NULL,
      "end_date" date,
      "end_time" time WITHOUT TIME ZONE,
      "description" text,
      "image_url" text,
      "event_type" text NOT NULL CHECK (event_type IN ('in-person', 'remote', 'hybrid')),
      "physical_location" text,
      "meeting_url" text,
      "event_category" text NOT NULL,
      "tags" text[] DEFAULT '{}',
      "netzero_category_ids" uuid[] DEFAULT '{}',
      "industry_ids" uuid[] DEFAULT '{}',
      "created_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      "updated_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      PRIMARY KEY (id)
    );

    -- Enable Row Level Security
    ALTER TABLE "public"."events" ENABLE ROW LEVEL SECURITY;

    -- Create policies
    -- Everyone can view events
    CREATE POLICY "Everyone can view events" ON "public"."events"
    FOR SELECT USING (true);

    -- Users can create their own events
    CREATE POLICY "Users can create their own events" ON "public"."events"
    FOR INSERT WITH CHECK (auth.uid() = creator_user_id);

    -- Users can update their own events
    CREATE POLICY "Users can update their own events" ON "public"."events"
    FOR UPDATE USING (auth.uid() = creator_user_id);

    -- Users can delete their own events
    CREATE POLICY "Users can delete their own events" ON "public"."events"
    FOR DELETE USING (auth.uid() = creator_user_id);

    -- Create an index for faster event lookups by creator
    CREATE INDEX "events_creator_user_id_idx" ON "public"."events" (creator_user_id);

    -- Create an index for date-based querying
    CREATE INDEX "events_start_date_idx" ON "public"."events" (start_date);
    
    RAISE NOTICE 'Events table created successfully';
  ELSE
    RAISE NOTICE 'Events table already exists';
  END IF;
END $$;

-- Now check if the event_signups table exists and create it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_catalog.pg_tables
    WHERE schemaname = 'public' AND tablename = 'event_signups'
  ) THEN
    RAISE NOTICE 'Creating event_signups table';
    
    -- Create event signups table
    CREATE TABLE "public"."event_signups" (
      "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
      "event_id" uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
      "user_id" uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      "gdpr_consent" boolean NOT NULL DEFAULT false,
      "hide_attendance" boolean NOT NULL DEFAULT false,
      "created_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      "updated_at" timestamp WITH TIME ZONE NOT NULL DEFAULT NOW(),
      PRIMARY KEY (id),
      -- Prevent duplicate signups
      UNIQUE (event_id, user_id)
    );

    -- Enable Row Level Security
    ALTER TABLE "public"."event_signups" ENABLE ROW LEVEL SECURITY;

    -- Create policies
    -- Everyone can view event signups
    CREATE POLICY "Everyone can view event signups" ON "public"."event_signups"
    FOR SELECT USING (true);

    -- Users can sign up for events
    CREATE POLICY "Users can sign up for events" ON "public"."event_signups"
    FOR INSERT WITH CHECK (auth.uid() = user_id);

    -- Users can update their own signups
    CREATE POLICY "Users can update their own signups" ON "public"."event_signups"
    FOR UPDATE USING (auth.uid() = user_id);

    -- Users can delete their own signups
    CREATE POLICY "Users can delete their own signups" ON "public"."event_signups"
    FOR DELETE USING (auth.uid() = user_id);
    
    -- Add explicit comment for better joins and relations
    COMMENT ON COLUMN public.event_signups.user_id IS 'References auth.users(id)';
    
    RAISE NOTICE 'Event signups table created successfully';
  ELSE
    RAISE NOTICE 'Event signups table already exists';
  END IF;
END $$;

-- Enable realtime for event_signups table
DO $$
BEGIN
  -- Add table to supabase_realtime publication if not already added
  IF NOT EXISTS (
    SELECT 1
    FROM pg_publication_tables
    WHERE pubname = 'supabase_realtime'
    AND schemaname = 'public'
    AND tablename = 'event_signups'
  ) THEN
    ALTER publication supabase_realtime ADD TABLE event_signups;
    RAISE NOTICE 'Added event_signups to realtime publication';
  ELSE
    RAISE NOTICE 'event_signups already in realtime publication';
  END IF;
END $$;

-- Check if the view exists and create it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_catalog.pg_views
    WHERE schemaname = 'public' AND viewname = 'events_with_creator'
  ) THEN
    RAISE NOTICE 'Creating events_with_creator view';
    
    -- Create the view
    CREATE VIEW public.events_with_creator AS
    SELECT 
      e.*,
      au.email as creator_email,
      CONCAT(p.first_name, ' ', p.last_name) as creator_name,
      p.avatar_url as creator_avatar_url
    FROM 
      public.events e
    JOIN 
      auth.users au ON e.creator_user_id = au.id
    LEFT JOIN 
      public.profiles p ON e.creator_user_id = p.id;

    -- Grant appropriate permissions on the view
    ALTER VIEW public.events_with_creator OWNER TO postgres;
    GRANT SELECT ON public.events_with_creator TO authenticated;
    GRANT SELECT ON public.events_with_creator TO service_role;
    
    RAISE NOTICE 'Events with creator view created successfully';
  ELSE
    RAISE NOTICE 'Events with creator view already exists';
  END IF;
END $$;

-- Check if the event_signups_with_users view exists and create it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_catalog.pg_views
    WHERE schemaname = 'public' AND viewname = 'event_signups_with_users'
  ) THEN
    RAISE NOTICE 'Creating event_signups_with_users view';
    
    -- Create the view
    CREATE VIEW public.event_signups_with_users AS
    SELECT 
      es.*,
      au.email,
      CONCAT(p.first_name, ' ', p.last_name) as full_name,
      p.avatar_url
    FROM 
      public.event_signups es
    JOIN 
      auth.users au ON es.user_id = au.id
    LEFT JOIN 
      public.profiles p ON es.user_id = p.id;

    -- Grant appropriate permissions on the view
    ALTER VIEW public.event_signups_with_users OWNER TO postgres;
    GRANT SELECT ON public.event_signups_with_users TO authenticated;
    GRANT SELECT ON public.event_signups_with_users TO service_role;
    
    RAISE NOTICE 'Event signups with users view created successfully';
  ELSE
    RAISE NOTICE 'Event signups with users view already exists';
  END IF;
END $$;
