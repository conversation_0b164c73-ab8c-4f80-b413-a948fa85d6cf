-- Add new columns to training_courses table
ALTER TABLE training_courses
  -- Add duration
  ADD COLUMN duration TEXT,
  -- Add level (beginner, intermediate, advanced)
  ADD COLUMN level TEXT CHECK (level IN ('Beginner', 'Intermediate', 'Advanced')),
  -- Add certification details
  ADD COLUMN certification TEXT,
  -- Add arrays for syllabus, instructors, and learning outcomes
  ADD COLUMN syllabus TEXT[],
  ADD COLUMN instructors TEXT[],
  ADD COLUMN learning_outcomes TEXT[],
  -- Add enrollment dates and provider info
  ADD COLUMN enrollment_dates TEXT,
  ADD COLUMN about_provider TEXT,
  -- Add tags
  ADD COLUMN tags TEXT[];

-- Add comment to explain the new fields
COMMENT ON TABLE training_courses IS 'Stores training course information including detailed program content and structure';

-- Add comments for the new columns
COMMENT ON COLUMN training_courses.duration IS 'Duration of the course (e.g., "12 weeks")';
COMMENT ON COLUMN training_courses.level IS 'Difficulty level of the course (Beginner, Intermediate, Advanced)';
COMMENT ON COLUMN training_courses.certification IS 'Details about certification provided';
COMMENT ON COLUMN training_courses.syllabus IS 'Array of course modules/topics';
COMMENT ON COLUMN training_courses.instructors IS 'Array of instructor names';
COMMENT ON COLUMN training_courses.learning_outcomes IS 'Array of expected learning outcomes';
COMMENT ON COLUMN training_courses.enrollment_dates IS 'Information about course start dates';
COMMENT ON COLUMN training_courses.about_provider IS 'Detailed information about the course provider';
COMMENT ON COLUMN training_courses.tags IS 'Array of tags categorizing the course';
