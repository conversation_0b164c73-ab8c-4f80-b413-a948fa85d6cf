-- Add notification preferences creation to user signup process
-- This ensures that notification_preferences records are created automatically when users sign up
-- Date: 2025-01-03

<PERSON><PERSON><PERSON>;

-- Update the handle_new_user function to also create notification preferences
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  profile_id UUID;
  default_notification_types TEXT[] := ARRAY[
    'post_like',
    'post_comment', 
    'comment_reply',
    'comment_like',
    'connection_request',
    'connection_accepted',
    'event_signup',
    'event_creation'
  ];
  notification_type TEXT;
BEGIN
  -- Insert a new profile record for the new user with only existing columns
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    email, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  )
  RETURNING id INTO profile_id;

  -- Create default user_consent_settings for the new user
  -- This replaces the privacy columns that were removed from profiles
  INSERT INTO public.user_consent_settings (
    user_id,
    profile_visibility,
    email_notifications,
    newsletter_subscription,
    show_businesses,
    show_events,
    show_connections,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    true,  -- profile_visibility - default to public
    true,  -- email_notifications - default to enabled
    false, -- newsletter_subscription - default to disabled
    true,  -- show_businesses - default to visible
    true,  -- show_events - default to visible
    true,  -- show_connections - default to visible
    false, -- share_email_with_event_creators - default to private
    false, -- share_email_with_attendees - default to private
    false, -- share_contact_details - default to private
    NEW.created_at,
    NEW.created_at
  )
  ON CONFLICT (user_id) DO NOTHING;

  -- Create default notification preferences for the new user
  -- Check if notification_preferences table exists and has the expected structure
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'notification_preferences'
  ) THEN
    -- Check if the table uses the flexible structure (type, email_enabled, push_enabled)
    IF EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'notification_preferences' 
      AND column_name = 'type'
    ) THEN
      -- Use the flexible structure
      FOREACH notification_type IN ARRAY default_notification_types
      LOOP
        INSERT INTO public.notification_preferences (
          profile_id,
          type,
          email_enabled,
          push_enabled,
          created_at,
          updated_at
        )
        VALUES (
          profile_id,
          notification_type,
          true, -- Default to enabled for email
          true, -- Default to enabled for push
          NOW(),
          NOW()
        )
        ON CONFLICT (profile_id, type) DO NOTHING;
      END LOOP;
    ELSE
      -- Use the old structure with individual boolean columns (if it exists)
      IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'notification_preferences' 
        AND column_name = 'post_likes'
      ) THEN
        INSERT INTO public.notification_preferences (
          profile_id,
          email_notifications,
          push_notifications,
          post_likes,
          post_comments,
          comment_replies,
          comment_likes,
          connection_requests,
          connection_accepted,
          event_signups,
          event_updates,
          system_notifications,
          created_at,
          updated_at
        )
        VALUES (
          profile_id,
          true, -- email_notifications
          true, -- push_notifications
          true, -- post_likes
          true, -- post_comments
          true, -- comment_replies
          true, -- comment_likes
          true, -- connection_requests
          true, -- connection_accepted
          true, -- event_signups
          true, -- event_updates
          true, -- system_notifications
          NOW(),
          NOW()
        )
        ON CONFLICT (profile_id) DO NOTHING;
      END IF;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment to reflect the new functionality
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates a profile record, user consent settings, and notification preferences when a new user signs up';

COMMIT;

-- Verify the function was updated
SELECT 'User signup function updated with notification preferences' as status;
