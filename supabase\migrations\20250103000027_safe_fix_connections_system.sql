-- Safe fix for connections system - handles existing policies gracefully
-- Date: 2025-01-03

BEGIN;

-- First, ensure the user_connections table exists with proper structure
CREATE TABLE IF NOT EXISTS public.user_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    connection_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    status TEXT NOT NULL CHECK (status IN ('pending', 'accepted', 'rejected', 'blocked')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (user_id, connection_id)
);

-- Enable RLS
ALTER TABLE public.user_connections ENABLE ROW LEVEL SECURITY;

-- Drop ALL possible existing RLS policies (comprehensive list)
DO $$
BEGIN
    -- Drop all possible policy name variations
    DROP POLICY IF EXISTS "Users can manage their own connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can view their own connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can insert their own connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can update their own connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can delete their own connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can view their connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can insert their connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can update their connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can delete their connections" ON public.user_connections;
    DROP POLICY IF EXISTS "Users can manage their connections" ON public.user_connections;
    DROP POLICY IF EXISTS "user_connections_select_policy" ON public.user_connections;
    DROP POLICY IF EXISTS "user_connections_insert_policy" ON public.user_connections;
    DROP POLICY IF EXISTS "user_connections_update_policy" ON public.user_connections;
    DROP POLICY IF EXISTS "user_connections_delete_policy" ON public.user_connections;
EXCEPTION
    WHEN OTHERS THEN
        -- Ignore any errors from dropping non-existent policies
        NULL;
END $$;

-- Create comprehensive RLS policies with unique names
CREATE POLICY "connections_select_policy_2025" 
ON public.user_connections
FOR SELECT 
TO authenticated
USING (
    (auth.uid() = user_id) OR (auth.uid() = connection_id)
);

CREATE POLICY "connections_insert_policy_2025" 
ON public.user_connections
FOR INSERT 
TO authenticated
WITH CHECK (
    (auth.uid() = user_id) OR (auth.uid() = connection_id)
);

CREATE POLICY "connections_update_policy_2025" 
ON public.user_connections
FOR UPDATE 
TO authenticated
USING (
    (auth.uid() = user_id) OR (auth.uid() = connection_id)
)
WITH CHECK (
    (auth.uid() = user_id) OR (auth.uid() = connection_id)
);

CREATE POLICY "connections_delete_policy_2025" 
ON public.user_connections
FOR DELETE 
TO authenticated
USING (
    (auth.uid() = user_id) OR (auth.uid() = connection_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_connections_user_id ON public.user_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_connection_id ON public.user_connections(connection_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_status ON public.user_connections(status);
CREATE INDEX IF NOT EXISTS idx_user_connections_user_status ON public.user_connections(user_id, status);
CREATE INDEX IF NOT EXISTS idx_user_connections_connection_status ON public.user_connections(connection_id, status);

-- Drop existing connection-related functions to recreate them properly
DROP FUNCTION IF EXISTS public.request_connection(UUID, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.respond_to_connection_request(UUID, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.cancel_connection_request(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.remove_connection(UUID) CASCADE;

-- Create request_connection function
CREATE OR REPLACE FUNCTION public.request_connection(
    user_id_param UUID,
    connection_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_connection_id UUID;
    current_user_id UUID;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Verify the requesting user is authenticated
    IF current_user_id != user_id_param THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Unauthorized: You can only request connections for yourself'
        );
    END IF;
    
    -- Check if connection already exists
    IF EXISTS (
        SELECT 1 FROM public.user_connections 
        WHERE (user_id = user_id_param AND connection_id = connection_id_param)
           OR (user_id = connection_id_param AND connection_id = user_id_param)
    ) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection already exists or request already sent'
        );
    END IF;
    
    -- Create the connection request
    INSERT INTO public.user_connections (user_id, connection_id, status)
    VALUES (user_id_param, connection_id_param, 'pending')
    RETURNING id INTO new_connection_id;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'id', new_connection_id,
            'user_id', user_id_param,
            'connection_id', connection_id_param,
            'status', 'pending'
        )
    );
END;
$$;

-- Create respond_to_connection_request function
CREATE OR REPLACE FUNCTION public.respond_to_connection_request(
    request_id_param UUID,
    status_param TEXT
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    connection_record RECORD;
    current_user_id UUID;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Validate status
    IF status_param NOT IN ('accepted', 'rejected') THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid status. Must be accepted or rejected'
        );
    END IF;
    
    -- Get the connection request and verify user is the recipient
    SELECT * INTO connection_record
    FROM public.user_connections
    WHERE id = request_id_param 
      AND connection_id = current_user_id 
      AND status = 'pending';
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found or you are not authorized to respond'
        );
    END IF;
    
    -- Update the connection status
    UPDATE public.user_connections
    SET status = status_param, updated_at = now()
    WHERE id = request_id_param;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object(
            'id', request_id_param,
            'status', status_param
        )
    );
END;
$$;

-- Create cancel_connection_request function
CREATE OR REPLACE FUNCTION public.cancel_connection_request(
    request_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Delete the connection request (only if user is the sender and status is pending)
    DELETE FROM public.user_connections
    WHERE id = request_id_param 
      AND user_id = current_user_id 
      AND status = 'pending';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection request not found or cannot be cancelled'
        );
    END IF;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object('id', request_id_param)
    );
END;
$$;

-- Create remove_connection function
CREATE OR REPLACE FUNCTION public.remove_connection(
    connection_id_param UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Authentication required'
        );
    END IF;
    
    -- Delete the connection (user must be involved in the connection)
    DELETE FROM public.user_connections
    WHERE id = connection_id_param 
      AND (user_id = current_user_id OR connection_id = current_user_id);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Connection not found or you are not authorized to remove it'
        );
    END IF;
    
    RETURN jsonb_build_object(
        'success', true,
        'data', jsonb_build_object('id', connection_id_param)
    );
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.request_connection TO authenticated;
GRANT EXECUTE ON FUNCTION public.respond_to_connection_request TO authenticated;
GRANT EXECUTE ON FUNCTION public.cancel_connection_request TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_connection TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION public.request_connection IS 'Send a connection request to another user';
COMMENT ON FUNCTION public.respond_to_connection_request IS 'Accept or reject a pending connection request';
COMMENT ON FUNCTION public.cancel_connection_request IS 'Cancel a pending connection request sent by the current user';
COMMENT ON FUNCTION public.remove_connection IS 'Remove an existing connection';

COMMIT;

-- Create a diagnostic function to help debug connection issues
CREATE OR REPLACE FUNCTION public.debug_user_connections()
RETURNS TABLE (
    connection_id UUID,
    user_id UUID,
    connection_user_id UUID,
    status TEXT,
    created_at TIMESTAMPTZ,
    duplicate_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        uc.id as connection_id,
        uc.user_id,
        uc.connection_id as connection_user_id,
        uc.status,
        uc.created_at,
        COUNT(*) OVER (PARTITION BY uc.id) as duplicate_count
    FROM public.user_connections uc
    WHERE uc.user_id = auth.uid() OR uc.connection_id = auth.uid()
    ORDER BY uc.created_at DESC;
END;
$$;

GRANT EXECUTE ON FUNCTION public.debug_user_connections TO authenticated;

-- Test that functions were created successfully
SELECT
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines
WHERE routine_name IN (
    'request_connection',
    'respond_to_connection_request',
    'cancel_connection_request',
    'remove_connection',
    'debug_user_connections'
)
AND routine_schema = 'public'
ORDER BY routine_name;
