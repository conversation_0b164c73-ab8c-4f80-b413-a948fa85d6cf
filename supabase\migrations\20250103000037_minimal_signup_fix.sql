-- Minimal fix for user signup function - only fix the immediate issue
-- This is a conservative approach to get signups working again
-- Date: 2025-01-03

BEGIN;

-- First, let's see what the current function looks like and fix only what's broken
-- Update the handle_new_user function to work with current table structure
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert a new profile record for the new user with only columns that definitely exist
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    email, 
    social_visibility, 
    subscription_tier, 
    subscription_status, 
    created_at, 
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.email,
    'public', -- Default social_visibility
    'none', -- Default subscription_tier
    'trial', -- Default subscription_status
    NEW.created_at,
    NEW.created_at
  );

  -- Only create user_consent_settings if the table exists and has the expected columns
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'user_consent_settings'
  ) THEN
    -- Check if the extended columns exist (added by consolidation migration)
    IF EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'user_consent_settings' 
      AND column_name = 'profile_visibility'
    ) THEN
      -- Insert with all columns
      INSERT INTO public.user_consent_settings (
        user_id,
        profile_visibility,
        email_notifications,
        newsletter_subscription,
        show_businesses,
        show_events,
        show_connections,
        share_email_with_event_creators,
        share_email_with_attendees,
        share_contact_details,
        created_at,
        updated_at
      )
      VALUES (
        NEW.id,
        true,  -- profile_visibility
        true,  -- email_notifications
        false, -- newsletter_subscription
        true,  -- show_businesses
        true,  -- show_events
        true,  -- show_connections
        false, -- share_email_with_event_creators
        false, -- share_email_with_attendees
        false, -- share_contact_details
        NEW.created_at,
        NEW.created_at
      )
      ON CONFLICT (user_id) DO NOTHING;
    ELSE
      -- Insert with only basic columns
      INSERT INTO public.user_consent_settings (
        user_id,
        share_email_with_event_creators,
        share_email_with_attendees,
        share_contact_details,
        created_at,
        updated_at
      )
      VALUES (
        NEW.id,
        false, -- share_email_with_event_creators
        false, -- share_email_with_attendees
        false, -- share_contact_details
        NEW.created_at,
        NEW.created_at
      )
      ON CONFLICT (user_id) DO NOTHING;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the comment
COMMENT ON FUNCTION public.handle_new_user() IS 'Creates a profile record and user consent settings when a new user signs up - minimal safe version';

COMMIT;

-- Verify the function was updated
SELECT 'Minimal signup function fix applied' as status;
