-- Check and fix businesses table RLS policies and permissions for nested queries
BEGIN;

-- First, check existing policies
SELECT
    schemaname,
    tablename,
    policyname,
    roles,
    cmd
FROM
    pg_policies
WHERE
    tablename = 'businesses';

-- Drop existing policies if any (we'll recreate them)
DROP POLICY IF EXISTS "Enable read access for all" ON public.businesses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.businesses;
DROP POLICY IF EXISTS "Enable update for business owners" ON public.businesses;
DROP POLICY IF EXISTS "Enable delete for business owners" ON public.businesses;

-- Create comprehensive RLS policies for businesses table
-- 1. Allow all authenticated users to read businesses
CREATE POLICY "Enable read access for all"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

-- 2. Allow authenticated users to create their own businesses
CREATE POLICY "Enable insert for authenticated users"
    ON public.businesses
    FOR INSERT
    TO authenticated
    WITH CHECK (
        auth.uid() = owner_id
    );

-- 3. Allow business owners to update their businesses
CREATE POLICY "Enable update for business owners"
    ON public.businesses
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = owner_id)
    WITH CHECK (auth.uid() = owner_id);

-- 4. Allow business owners to delete their businesses
CREATE POLICY "Enable delete for business owners"
    ON public.businesses
    FOR DELETE
    TO authenticated
    USING (auth.uid() = owner_id);

-- Grant necessary permissions
GRANT ALL ON public.businesses TO authenticated;
GRANT ALL ON public.businesses TO service_role;

-- Grant permissions for nested tables
GRANT SELECT ON public.industries TO authenticated;
GRANT SELECT ON public.locations TO authenticated;
GRANT SELECT ON public.netzero_categories TO authenticated;

-- Add necessary indexes for performance (if they don't exist)
CREATE INDEX IF NOT EXISTS idx_businesses_owner_id ON public.businesses(owner_id);
CREATE INDEX IF NOT EXISTS idx_businesses_main_category_id ON public.businesses(main_category_id);
CREATE INDEX IF NOT EXISTS idx_businesses_hq_location_id ON public.businesses(hq_location_id);

-- Verify the final policies
SELECT
    schemaname,
    tablename,
    policyname,
    roles,
    cmd
FROM
    pg_policies
WHERE
    tablename IN ('businesses', 'industries', 'locations', 'netzero_categories');

COMMIT;
