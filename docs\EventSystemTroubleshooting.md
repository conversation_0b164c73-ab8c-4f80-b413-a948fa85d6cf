# Events System Troubleshooting Guide

## Issue: Events Not Displaying on the Events Page

The Events page may sometimes fail to display events due to database structure issues, particularly when migrations have not been properly applied or when there are relationship issues between the `events` and `event_signups` tables.

## Diagnostic Tools

We've added a diagnostic page to help identify and fix these issues:

1. When the Events page shows an error, you'll see a link to the diagnostic page
2. You can directly access the diagnostic page at `/events/system-diagnostic`
3. The diagnostic page will check:
   - If the events table exists
   - If the event_signups table exists
   - If the events_with_creator view exists
   - If the relationships between tables are properly set up
   - If there are any missing migrations

## How to Fix

### Option 1: Using the Diagnostics Page

1. Navigate to `/events/system-diagnostic`
2. Run the diagnostics to identify any issues
3. Click the "Apply Fixes" button to automatically repair the database structure
4. Re-run the diagnostics to verify that the issues have been resolved

### Option 2: Manual Fix

If the automatic fix doesn't work, you can manually apply the migrations:

1. Connect to your Supabase project
2. Go to the SQL Editor
3. Run the following migrations in order:
   - `20250515085000_create_events_table.sql`
   - `20250515090000_create_event_signups_table.sql`
   - `20250515110000_create_events_with_creator_view.sql`
   - `20250516090000_fix_event_signups_relation.sql`
   - `20250520090000_create_event_diagnostic_functions.sql`
   - `20250520110000_create_events_system_fix_rpc.sql`

Alternatively, you can run the comprehensive fix script:
- `20250520100000_fix_events_system.sql`

## Understanding the Root Cause

The issue typically occurs because:

1. The migrations were not applied in the correct order
2. Some migrations were skipped during deployment
3. The relationships between tables were not properly established
4. The views were not created or have incorrect column references

## Prevention

To prevent this issue in the future:

1. Always check that all migrations are applied when deploying
2. Use the `supabase db push` command to ensure migrations are applied correctly
3. Add migration verification to your CI/CD pipeline
4. Periodically run the diagnostics to check for structural issues

## Technical Details

The events system relies on the following database objects:

1. `events` table - Stores event details
2. `event_signups` table - Tracks user registrations for events
3. `events_with_creator` view - Joins events with creator information
4. `event_signups_with_users` view - Joins signups with user information

The query that fails when retrieving events looks like:

```sql
SELECT 
  *,
  signups:event_signups(count),
  user_has_signed_up:event_signups!left(user_id)
FROM public.events
```

This fails when the relationship between `events` and `event_signups` is not properly established.

## Fallback Mechanism

If the main query fails, the system will attempt to use a simplified query that doesn't rely on relationships:

```sql
SELECT * FROM public.events
```

This ensures that basic event information is still displayed even when the relationships have issues.
