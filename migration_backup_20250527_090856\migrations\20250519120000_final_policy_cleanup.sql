-- Final cleanup of business-related policies
BEGIN;

-- Clean up businesses table policies
DROP POLICY IF EXISTS "Anyone can view businesses" ON public.businesses;
DROP POLICY IF EXISTS "Users can view all businesses" ON public.businesses;
DROP POLICY IF EXISTS "Users can create businesses" ON public.businesses;
DROP POLICY IF EXISTS "Users can update their own businesses" ON public.businesses;
DROP POLICY IF EXISTS "Users can delete their own businesses" ON public.businesses;
DROP POLICY IF EXISTS "Service role can do anything with businesses" ON public.businesses;
DROP POLICY IF EXISTS "Users can manage their own businesses" ON public.businesses;
DROP POLICY IF EXISTS "Enable nested queries for authenticated users on businesses" ON public.businesses;
DROP POLICY IF EXISTS "Owners can manage their businesses" ON public.businesses;
DROP POLICY IF EXISTS "Enable read access for service role on businesses" ON public.businesses;

-- Create simplified policies for businesses table
CREATE POLICY "Enable read access for all users"
    ON public.businesses
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable all access for service role"
    ON public.businesses
    TO service_role
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable owner management"
    ON public.businesses
    FOR ALL
    TO authenticated
    USING (auth.uid() = owner_id)
    WITH CHECK (auth.uid() = owner_id);

-- Add REFERENCES permissions to allow joining between tables
GRANT REFERENCES ON public.profiles TO authenticated;
GRANT REFERENCES ON public.businesses TO authenticated;

-- Verify final policy state
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM
    pg_policies
WHERE
    tablename IN (
        'businesses',
        'business_relevant_industries',
        'business_service_locations',
        'industries',
        'locations',
        'netzero_categories',
        'profiles'
    )
ORDER BY
    tablename,
    policyname;

COMMIT;
