-- =====================================================
-- MIGRATE EXISTING CONNECTIONS TO NEW SYSTEM
-- =====================================================
-- Safely migrate data from user_connections to social_connections

-- =====================================================
-- 1. PRE-MIGRATION CHECKS
-- =====================================================

-- Check what data exists in old system
SELECT 
    '=== OLD SYSTEM DATA CHECK ===' as check_phase,
    COUNT(*) as total_connections,
    COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_connections,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_connections,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_connections,
    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_connections
FROM user_connections;

-- Check what data exists in new system
SELECT 
    '=== NEW SYSTEM DATA CHECK ===' as check_phase,
    COUNT(*) as total_connections,
    COUNT(CASE WHEN status = 'connected' THEN 1 END) as connected,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
    COUNT(CASE WHEN status = 'declined' THEN 1 END) as declined,
    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked
FROM social_connections;

-- =====================================================
-- 2. MIGRATION MAPPING
-- =====================================================

-- Show the status mapping that will be used
SELECT 
    '=== STATUS MAPPING ===' as mapping_phase,
    'Old Status' as old_status,
    'New Status' as new_status
UNION ALL
SELECT '', 'accepted', 'connected'
UNION ALL
SELECT '', 'pending', 'pending'
UNION ALL
SELECT '', 'rejected', 'declined'
UNION ALL
SELECT '', 'blocked', 'blocked'
UNION ALL
SELECT '', 'other/null', 'pending (default)';

-- =====================================================
-- 3. SAFE MIGRATION WITH CONFLICT HANDLING
-- =====================================================

-- Migrate connections that don't already exist in new system
INSERT INTO social_connections (
    requester_id, 
    recipient_id, 
    status, 
    created_at, 
    updated_at, 
    connected_at
)
SELECT 
    uc.user_id as requester_id,
    uc.connection_id as recipient_id,
    CASE 
        WHEN uc.status = 'accepted' THEN 'connected'
        WHEN uc.status = 'pending' THEN 'pending'
        WHEN uc.status = 'rejected' THEN 'declined'
        WHEN uc.status = 'blocked' THEN 'blocked'
        ELSE 'pending'  -- Default for any other status
    END as status,
    COALESCE(uc.created_at, now()) as created_at,
    COALESCE(uc.updated_at, now()) as updated_at,
    CASE 
        WHEN uc.status = 'accepted' THEN COALESCE(uc.updated_at, uc.created_at, now())
        ELSE NULL 
    END as connected_at
FROM user_connections uc
WHERE NOT EXISTS (
    -- Avoid duplicates: check if connection already exists in either direction
    SELECT 1 FROM social_connections sc 
    WHERE (
        (sc.requester_id = uc.user_id AND sc.recipient_id = uc.connection_id)
        OR 
        (sc.requester_id = uc.connection_id AND sc.recipient_id = uc.user_id)
    )
)
-- Only migrate valid user IDs that exist in profiles
AND EXISTS (SELECT 1 FROM profiles WHERE id = uc.user_id)
AND EXISTS (SELECT 1 FROM profiles WHERE id = uc.connection_id)
-- Don't migrate self-connections
AND uc.user_id != uc.connection_id;

-- =====================================================
-- 4. POST-MIGRATION VERIFICATION
-- =====================================================

-- Check migration results
SELECT 
    '=== MIGRATION RESULTS ===' as verification_phase,
    'Total migrated connections' as metric,
    COUNT(*) as count
FROM social_connections
WHERE created_at >= (SELECT MIN(created_at) FROM user_connections)
UNION ALL
SELECT 
    '=== MIGRATION RESULTS ===',
    'Connected relationships',
    COUNT(*)
FROM social_connections 
WHERE status = 'connected'
UNION ALL
SELECT 
    '=== MIGRATION RESULTS ===',
    'Pending relationships',
    COUNT(*)
FROM social_connections 
WHERE status = 'pending';

-- Show sample migrated data
SELECT 
    '=== SAMPLE MIGRATED DATA ===' as sample_phase,
    sc.id,
    p1.first_name || ' ' || p1.last_name as requester_name,
    p2.first_name || ' ' || p2.last_name as recipient_name,
    sc.status,
    sc.created_at,
    sc.connected_at
FROM social_connections sc
JOIN profiles p1 ON sc.requester_id = p1.id
JOIN profiles p2 ON sc.recipient_id = p2.id
ORDER BY sc.created_at DESC
LIMIT 10;

-- =====================================================
-- 5. COMPARISON REPORT
-- =====================================================

-- Compare old vs new system counts
WITH old_counts AS (
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending
    FROM user_connections
),
new_counts AS (
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'connected' THEN 1 END) as connected,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending
    FROM social_connections
)
SELECT 
    '=== MIGRATION COMPARISON ===' as comparison_phase,
    'Old System Total' as metric,
    old_counts.total as count
FROM old_counts
UNION ALL
SELECT 
    '=== MIGRATION COMPARISON ===',
    'New System Total',
    new_counts.total
FROM new_counts
UNION ALL
SELECT 
    '=== MIGRATION COMPARISON ===',
    'Old Accepted',
    old_counts.accepted
FROM old_counts
UNION ALL
SELECT 
    '=== MIGRATION COMPARISON ===',
    'New Connected',
    new_counts.connected
FROM new_counts;

-- =====================================================
-- 6. MIGRATION SUCCESS CHECK
-- =====================================================

-- Final success verification
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM social_connections) >= (SELECT COUNT(*) FROM user_connections) 
        THEN '✅ MIGRATION SUCCESSFUL'
        ELSE '⚠️ MIGRATION INCOMPLETE - Check for conflicts or invalid data'
    END as migration_status,
    (SELECT COUNT(*) FROM user_connections) as old_system_count,
    (SELECT COUNT(*) FROM social_connections) as new_system_count,
    (SELECT COUNT(*) FROM social_connections) - (SELECT COUNT(*) FROM user_connections) as difference;

-- =====================================================
-- 7. NEXT STEPS RECOMMENDATIONS
-- =====================================================

SELECT 
    '=== NEXT STEPS ===' as next_steps,
    'Test the new system thoroughly' as step_1,
    'Verify all functionality works' as step_2,
    'Keep old tables as backup for now' as step_3,
    'Drop old tables only after full verification' as step_4;

-- Show any potential issues
SELECT 
    '=== POTENTIAL ISSUES ===' as issues_check,
    'Connections with invalid user IDs' as issue_type,
    COUNT(*) as count
FROM user_connections uc
WHERE NOT EXISTS (SELECT 1 FROM profiles WHERE id = uc.user_id)
   OR NOT EXISTS (SELECT 1 FROM profiles WHERE id = uc.connection_id)
UNION ALL
SELECT 
    '=== POTENTIAL ISSUES ===',
    'Self-connections (user connecting to themselves)',
    COUNT(*)
FROM user_connections 
WHERE user_id = connection_id
UNION ALL
SELECT 
    '=== POTENTIAL ISSUES ===',
    'Duplicate connections in old system',
    COUNT(*) - COUNT(DISTINCT user_id, connection_id)
FROM user_connections;
