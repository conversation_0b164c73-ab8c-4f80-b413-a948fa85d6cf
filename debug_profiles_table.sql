-- Debug: Check what columns exist in the profiles table
-- Run this in Supabase SQL Editor to see the actual table structure

-- 1. Check table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 2. Check if profile_visibility column exists
SELECT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'profiles' 
    AND column_name = 'profile_visibility'
) as profile_visibility_exists;

-- 3. Simple test query to see what data is available
SELECT 
    id,
    first_name,
    last_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'profiles' 
            AND column_name = 'profile_visibility'
        ) THEN 'profile_visibility column exists'
        ELSE 'profile_visibility column MISSING'
    END as visibility_check
FROM profiles 
LIMIT 3;

-- 4. If profile_visibility doesn't exist, check for alternative columns
SELECT column_name
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
AND column_name LIKE '%visibility%'
OR column_name LIKE '%public%'
OR column_name LIKE '%private%'
OR column_name LIKE '%social%';
