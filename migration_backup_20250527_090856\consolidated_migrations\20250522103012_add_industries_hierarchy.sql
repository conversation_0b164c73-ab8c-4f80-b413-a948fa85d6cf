-- Add parent_id column and remove description if it exists
DO $$ 
BEGIN
    -- Drop description column if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'industries' 
        AND column_name = 'description'
    ) THEN
        ALTER TABLE public.industries DROP COLUMN description;
    END IF;

    -- Add parent_id if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'industries' 
        AND column_name = 'parent_id'
    ) THEN
        -- Add the parent_id column
        ALTER TABLE public.industries 
        ADD COLUMN parent_id UUID REFERENCES public.industries(id);
        
        -- Create index for the new parent_id column
        CREATE INDEX idx_industries_parent_id ON public.industries(parent_id);
    END IF;
END $$;

-- Clear existing data to avoid conflicts
TRUNCATE TABLE public.industries CASCADE;

-- Enable RLS and set up policies
ALTER TABLE public.industries ENABLE ROW LEVEL SECURITY;

-- Create policy to allow anyone to read industries
DROP POLICY IF EXISTS "Anyone can read industries" ON public.industries;
CREATE POLICY "Anyone can read industries"
ON public.industries
FOR SELECT
TO PUBLIC
USING (true);

-- Grant SELECT permissions to authenticated and anonymous users
GRANT SELECT ON public.industries TO authenticated;
GRANT SELECT ON public.industries TO anon;

-- Insert parent industries and key sub-industries
-- Construction and Built Environment
WITH ins AS (
    INSERT INTO public.industries (name, parent_id, created_at, updated_at) 
    VALUES ('Construction and Built Environment', NULL, NOW(), NOW()) RETURNING id
)
INSERT INTO public.industries (name, parent_id, created_at, updated_at)
SELECT 'Infrastructure', id, NOW(), NOW() FROM ins
UNION ALL
SELECT 'Sustainable Buildings', id, NOW(), NOW() FROM ins;

-- Financial & Professional Services
WITH ins AS (
    INSERT INTO public.industries (name, parent_id, created_at, updated_at) 
    VALUES ('Financial & Professional Services', NULL, NOW(), NOW()) RETURNING id
)
INSERT INTO public.industries (name, parent_id, created_at, updated_at)
SELECT 'Financial Services', id, NOW(), NOW() FROM ins
UNION ALL
SELECT 'Insurance', id, NOW(), NOW() FROM ins
UNION ALL
SELECT 'Consultancy', id, NOW(), NOW() FROM ins
UNION ALL
SELECT 'Legal', id, NOW(), NOW() FROM ins
UNION ALL
SELECT 'Accounting', id, NOW(), NOW() FROM ins;

-- Real Estate
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Real Estate', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Property Management', id FROM ins
UNION ALL
SELECT 'Development', id FROM ins;

-- Retail, Wholesale & Consumer
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Retail, Wholesale & Consumer', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Retail', id FROM ins
UNION ALL
SELECT 'Wholesale', id FROM ins
UNION ALL
SELECT 'Fashion', id FROM ins
UNION ALL
SELECT 'Food & Drink', id FROM ins;

-- Manufacturing & Industry
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Manufacturing & Industry', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Textiles', id FROM ins
UNION ALL
SELECT 'Engineering', id FROM ins
UNION ALL
SELECT 'Food Production', id FROM ins;

-- Information & Communication
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Information & Communication', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Media', id FROM ins
UNION ALL
SELECT 'Publishing', id FROM ins
UNION ALL
SELECT 'IT', id FROM ins
UNION ALL
SELECT 'Technology / Digital', id FROM ins
UNION ALL
SELECT 'Creative Arts and Design', id FROM ins;

-- Accommodation, Hospitality & Leisure
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Accommodation, Hospitality & Leisure', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Hotels', id FROM ins
UNION ALL
SELECT 'Restaurants', id FROM ins
UNION ALL
SELECT 'Events', id FROM ins
UNION ALL
SELECT 'Leisure', id FROM ins
UNION ALL
SELECT 'Tourism', id FROM ins;

-- Sport as a sub-industry of Leisure
INSERT INTO public.industries (name, parent_id)
SELECT 'Sport', id FROM public.industries WHERE name = 'Leisure';

-- Health, Social Care & Charity
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Health, Social Care & Charity', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Healthcare', id FROM ins
UNION ALL
SELECT 'Social Work', id FROM ins
UNION ALL
SELECT 'Charity', id FROM ins;

-- Education
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Education', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Schools', id FROM ins
UNION ALL
SELECT 'Higher Education', id FROM ins;

-- Agriculture, Forestry & Fishing
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Agriculture, Forestry & Fishing', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Horticulture', id FROM ins
UNION ALL
SELECT 'Fisheries', id FROM ins;

-- Mining & Extractives
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Mining & Extractives', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Oil & Gas', id FROM ins
UNION ALL
SELECT 'Minerals', id FROM ins;

-- Utilities & Waste
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Utilities & Waste', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Energy', id FROM ins
UNION ALL
SELECT 'Water', id FROM ins
UNION ALL
SELECT 'Waste and Resources', id FROM ins;

-- Transport & Logistics
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Transport & Logistics', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Freight', id FROM ins
UNION ALL
SELECT 'Passenger Transport', id FROM ins;

-- Public Sector
WITH ins AS (
    INSERT INTO public.industries (name, parent_id) VALUES ('Public Sector', NULL) RETURNING id
)
INSERT INTO public.industries (name, parent_id)
SELECT 'Central Government', id FROM ins
UNION ALL
SELECT 'Local Government', id FROM ins
UNION ALL
SELECT 'Public Services', id FROM ins;
