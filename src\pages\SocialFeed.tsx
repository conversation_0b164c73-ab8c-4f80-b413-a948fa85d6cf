/**
 * Social Feed Page
 * 
 * This page displays the social feed where users can:
 * - View posts from other users
 * - Create new posts with text and media
 * - Like and comment on posts
 * - Filter posts by categories
 * - Connect with other users
 * * The page uses several key components:
 * - PostCreator: For creating new posts
 * - PremiumAdsCarousel: For displaying premium sponsor advertisements
 * - SocialFeedPost: For displaying individual posts
 * - FeedFilter: For filtering posts by connections and timing
 * - Various sidebars: For trending topics and connections
 * 
 * Main hooks:
 * - useSocialFeed: Manages posts, likes, comments, and creation
 * - useUserConnections: Manages user connections
 */
import { useRef, useState, useEffect } from "react";
import { Layout } from "@/components/Layout";
import { SidebarProvider, Sidebar, SidebarInset } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSocialFeed } from "@/hooks/useSocialFeed";
import SocialFeedPost from "@/components/social/SocialFeedPost";
import { PostCreator } from "@/components/social/PostCreator";
import { TrendingSidebar } from "@/components/social/TrendingSidebar";
import { ConnectionsDrawer } from "@/components/social/ConnectionsDrawer";
import { FeedFilter, type FeedFilterType } from "@/components/social/FeedFilter";
import PremiumAdsCarousel from "@/components/social/PremiumAdsCarousel";
import { useSearchParams } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { UserPlus } from "lucide-react";
import { PostData } from "@/types/social";
import { fetchSuggestedConnections } from "@/lib/social";
import { RepliesProvider } from "@/contexts/RepliesContext";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useAdvertisements } from "@/hooks/useAdvertisements";
import { AdvertisingSidebar } from "@/components/social/AdvertisingSidebar";
import EngagementCounter from "@/components/social/EngagementCounter";
import SocialSignUpPrompt from "@/components/social/SocialSignUpPrompt";
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

const SocialFeed = () => {
  const isMobile = useIsMobile();
  const [searchParams, setSearchParams] = useSearchParams();
  const feedFilterParam = searchParams.get('filter') as FeedFilterType | null;
  const highlightPostId = searchParams.get('highlight_post');
  const highlightCommentId = searchParams.get('highlight_comment');
  const feedFilter: FeedFilterType = feedFilterParam ?
    (['newest', 'trending', 'connections', 'all'].includes(feedFilterParam) ?
      feedFilterParam as FeedFilterType :
      'newest'
    ) : 'newest';
  const { standardAds, premiumAds, isLoading: isLoadingAds } = useAdvertisements();
  const { session } = useAuth();
  const { toast } = useToast();
  
  const {
    posts,
    currentUser,
    userPostCount,
    handleToggleLike,
    handleAddComment,
    handleCreatePost,
    handleLikeComment,
    handleEditComment,
    handleDeleteComment,
    handleEditPost,
    handleDeletePost,
    syncAllCounts,
    isLoading,
    refreshPosts  } = useSocialFeed(null, feedFilter);

  const [trendingTopics, setTrendingTopics] = useState([]);
  const [engagementStats, setEngagementStats] = useState({ post_count: 0, comment_count: 0, like_count: 0, comment_like_count: 0 });

  useEffect(() => {
    async function fetchStats() {
      const { data, error } = await supabase
        .from('social_engagement_stats')
        .select('*')
        .single();
      if (!error && data) {
        setEngagementStats(data);
      }
    }
    fetchStats();
  }, []);

  // Handle highlighting and scrolling to specific posts/comments
  useEffect(() => {
    if (!isLoading && (highlightPostId || highlightCommentId)) {
      const timer = setTimeout(() => {
        if (highlightPostId) {
          const postElement = document.getElementById(`post-${highlightPostId}`);
          if (postElement) {
            postElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            postElement.classList.add('ring-2', 'ring-primary', 'ring-opacity-50');
            // Remove highlight after 3 seconds
            setTimeout(() => {
              postElement.classList.remove('ring-2', 'ring-primary', 'ring-opacity-50');
            }, 3000);
          }
        }

        if (highlightCommentId) {
          const commentElement = document.getElementById(`comment-${highlightCommentId}`);
          if (commentElement) {
            commentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            commentElement.classList.add('ring-2', 'ring-primary', 'ring-opacity-50');
            // Remove highlight after 3 seconds
            setTimeout(() => {
              commentElement.classList.remove('ring-2', 'ring-primary', 'ring-opacity-50');
            }, 3000);
          }
        }

        // Clear the URL parameters after highlighting
        if (highlightPostId || highlightCommentId) {
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.delete('highlight_post');
          newSearchParams.delete('highlight_comment');
          setSearchParams(newSearchParams, { replace: true });
        }
      }, 500); // Small delay to ensure posts are rendered

      return () => clearTimeout(timer);
    }
  }, [isLoading, highlightPostId, highlightCommentId, searchParams, setSearchParams]);

  // Handle feed filter change
  const handleFeedFilterChange = (value: FeedFilterType) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('filter', value);
      return newParams;
    });
  };

  return (
    <Layout>
      <RepliesProvider>
        <SidebarProvider>
          <div className="flex min-h-screen w-full gap-8">
            {!isMobile && (
              <Sidebar
                collapsible="icon"
                side="left"
                className="w-64 lg:w-80 flex-shrink-0"
              >
                <div className="space-y-8">
                  <TrendingSidebar topics={trendingTopics} />
                  {/* Show standard sponsor ads on the left */}
                  <AdvertisingSidebar
                    standardAds={standardAds}
                    premiumAds={[]}
                  />
                </div>
              </Sidebar>
            )}
            <SidebarInset className="px-4 py-6 flex-1">
              <div className="max-w-3xl mx-auto">                <div className="flex justify-between items-center mb-8">
                  <div>
                    <h1 className="text-3xl font-bold">
                      {session?.user ? (
                        feedFilter === "connections"
                          ? "My Network"
                          : "Net Zero Community"
                      ) : (
                        "Net Zero Community"
                      )}
                    </h1>
                    {session?.user ? (
                      feedFilter === "connections" && (
                        <p className="text-sm text-muted-foreground mt-1">
                          Posts from your connections and your own posts
                        </p>
                      )
                    ) : (
                      <p className="text-sm text-muted-foreground mt-1">
                        Discover our vibrant sustainability community
                      </p>
                    )}
                  </div>
                  {session?.user && (
                    <div className="flex items-center space-x-4">
                      <FeedFilter
                        value={feedFilter}
                        onChange={handleFeedFilterChange}
                        isLoggedIn={!!session?.user}
                      />
                      {/* Only show ConnectionsDrawer if user is logged in */}
                      <ConnectionsDrawer />
                      {currentUser?.role?.includes("admin") && (
                        <button
                          onClick={syncAllCounts}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs"
                          disabled={isLoading}
                        >
                          {isLoading ? "Syncing..." : "Sync Counts"}
                        </button>
                      )}
                    </div>
                  )}
                </div>                {/* Only show PostCreator if user is signed in */}
                {session?.user && (
                  <PostCreator
                    currentUser={currentUser}
                    userPostCount={userPostCount}
                    onCreatePost={handleCreatePost}
                  />
                )}

                <PremiumAdsCarousel premiumAds={premiumAds} />

                {/* Enhanced Engagement Counter */}
                <EngagementCounter stats={engagementStats} />

                {/* Content based on authentication status */}
                {session?.user ? (
                  // Logged-in users see the full social feed
                  <div className="space-y-6">
                    {isLoading ? (
                      <div className="flex justify-center p-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                      </div>
                    ) : posts.length > 0 ? (
                      posts.map((post: PostData) => (
                        <SocialFeedPost
                          key={post.id}
                          post={post}
                          currentUserId={session?.user?.id}
                          currentUserProfile={{
                            name: currentUser.name,
                            avatar: currentUser.avatar
                          }}
                          onLikePost={handleToggleLike}
                          onCommentOnPost={handleAddComment}
                          onLikeComment={handleLikeComment}
                          onEditComment={handleEditComment}
                          onDeleteComment={handleDeleteComment}
                          onEditPost={handleEditPost}
                          onDeletePost={handleDeletePost}
                        />
                      ))
                    ) : (
                      <Card className="p-8 text-center">
                        <p className="text-muted-foreground">
                          {feedFilter === "connections"
                            ? "No posts from your connections yet. Connect with more users to see their posts in your feed!"
                            : "Be the first to share something with the community!"}
                        </p>
                        {/* Only show Find Connections button if user is logged in */}
                        {feedFilter === "connections" && session?.user && (
                          <Button
                            onClick={() => document.querySelector('[aria-label=\"Open connections\"]')?.dispatchEvent(new MouseEvent('click'))}
                            className="mt-4"
                            variant="outline"
                          >
                            <UserPlus className="mr-2 h-4 w-4" />
                            Find Connections
                          </Button>
                        )}
                      </Card>
                    )}
                  </div>
                ) : (
                  // Non-logged-in users see the sign-up prompt
                  <SocialSignUpPrompt />
                )}
              </div>
            </SidebarInset>
            {!isMobile && (
              <Sidebar
                collapsible="icon"
                side="right"
                className="w-64 lg:w-80 flex-shrink-0"
              >
                {/* Show premium sponsor ads on the right */}
                <AdvertisingSidebar
                  standardAds={[]}
                  premiumAds={premiumAds}
                />
              </Sidebar>
            )}
          </div>
        </SidebarProvider>
      </RepliesProvider>
    </Layout>
  );
};

export default SocialFeed;
