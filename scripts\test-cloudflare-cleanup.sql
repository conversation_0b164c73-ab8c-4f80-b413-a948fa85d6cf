-- Test script to help debug Cloudflare media cleanup
-- This script helps identify what images should be deleted for a user

-- ================================================================
-- PART 1: FIND USERS WITH CLOUDFLARE IMAGES
-- ================================================================

-- Find users who have Cloudflare images
SELECT 
    'Users with Cloudflare Images' as test_name,
    p.id as user_id,
    p.first_name,
    p.last_name,
    p.avatar_url,
    CASE 
        WHEN p.avatar_url LIKE '%imagedelivery.net%' THEN 'Cloudflare URL'
        WHEN p.avatar_url IS NOT NULL AND NOT p.avatar_url LIKE 'http%' THEN 'Possible Cloudflare ID'
        WHEN p.avatar_url IS NULL THEN 'No avatar'
        ELSE 'External URL'
    END as avatar_type
FROM profiles p
WHERE p.avatar_url IS NOT NULL
ORDER BY 
    CASE 
        WHEN p.avatar_url LIKE '%imagedelivery.net%' THEN 1
        WHEN NOT p.avatar_url LIKE 'http%' THEN 2
        ELSE 3
    END,
    p.created_at DESC
LIMIT 10;

-- ================================================================
-- PART 2: CHECK SOCIAL POSTS WITH IMAGES
-- ================================================================

-- Find social posts with Cloudflare images
SELECT 
    'Social Posts with Images' as test_name,
    sp.id as post_id,
    sp.user_id,
    sp.media_url,
    sp.media_type,
    CASE 
        WHEN sp.media_url LIKE '%imagedelivery.net%' THEN 'Cloudflare URL'
        WHEN sp.media_url IS NOT NULL AND NOT sp.media_url LIKE 'http%' THEN 'Possible Cloudflare ID'
        WHEN sp.media_url IS NULL THEN 'No media'
        ELSE 'External URL'
    END as media_type_classification
FROM social_posts sp
WHERE sp.media_url IS NOT NULL 
    AND sp.media_type = 'image'
ORDER BY 
    CASE 
        WHEN sp.media_url LIKE '%imagedelivery.net%' THEN 1
        WHEN NOT sp.media_url LIKE 'http%' THEN 2
        ELSE 3
    END,
    sp.created_at DESC
LIMIT 10;

-- ================================================================
-- PART 3: CHECK BUSINESS LOGOS
-- ================================================================

-- Find businesses with Cloudflare logos
SELECT 
    'Business Logos' as test_name,
    b.id as business_id,
    b.owner_id,
    b.name,
    b.logo_url,
    CASE 
        WHEN b.logo_url LIKE '%imagedelivery.net%' THEN 'Cloudflare URL'
        WHEN b.logo_url IS NOT NULL AND NOT b.logo_url LIKE 'http%' THEN 'Possible Cloudflare ID'
        WHEN b.logo_url IS NULL THEN 'No logo'
        ELSE 'External URL'
    END as logo_type
FROM businesses b
WHERE b.logo_url IS NOT NULL
ORDER BY 
    CASE 
        WHEN b.logo_url LIKE '%imagedelivery.net%' THEN 1
        WHEN NOT b.logo_url LIKE 'http%' THEN 2
        ELSE 3
    END,
    b.created_at DESC
LIMIT 10;

-- ================================================================
-- PART 4: COMPREHENSIVE USER MEDIA AUDIT
-- ================================================================

-- Create a function to audit all media for a specific user
CREATE OR REPLACE FUNCTION public.audit_user_media(target_user_id UUID)
RETURNS TABLE (
    media_type TEXT,
    media_url TEXT,
    source_table TEXT,
    source_id UUID,
    cloudflare_classification TEXT,
    extracted_image_id TEXT
) AS $$
BEGIN
    -- Profile avatar
    RETURN QUERY
    SELECT 
        'avatar'::TEXT,
        p.avatar_url::TEXT,
        'profiles'::TEXT,
        p.id,
        CASE 
            WHEN p.avatar_url LIKE '%imagedelivery.net%' THEN 'Cloudflare URL'
            WHEN p.avatar_url IS NOT NULL AND NOT p.avatar_url LIKE 'http%' THEN 'Possible Cloudflare ID'
            ELSE 'External/Other'
        END::TEXT,
        CASE 
            WHEN p.avatar_url LIKE '%imagedelivery.net%' THEN 
                split_part(split_part(p.avatar_url, '/', -2), '/', 1)
            WHEN p.avatar_url IS NOT NULL AND NOT p.avatar_url LIKE 'http%' THEN 
                p.avatar_url
            ELSE NULL
        END::TEXT
    FROM profiles p
    WHERE p.id = target_user_id AND p.avatar_url IS NOT NULL;
    
    -- Social post media
    RETURN QUERY
    SELECT 
        'social_post'::TEXT,
        sp.media_url::TEXT,
        'social_posts'::TEXT,
        sp.id,
        CASE 
            WHEN sp.media_url LIKE '%imagedelivery.net%' THEN 'Cloudflare URL'
            WHEN sp.media_url IS NOT NULL AND NOT sp.media_url LIKE 'http%' THEN 'Possible Cloudflare ID'
            ELSE 'External/Other'
        END::TEXT,
        CASE 
            WHEN sp.media_url LIKE '%imagedelivery.net%' THEN 
                split_part(split_part(sp.media_url, '/', -2), '/', 1)
            WHEN sp.media_url IS NOT NULL AND NOT sp.media_url LIKE 'http%' THEN 
                sp.media_url
            ELSE NULL
        END::TEXT
    FROM social_posts sp
    WHERE sp.user_id = target_user_id 
        AND sp.media_url IS NOT NULL 
        AND sp.media_type = 'image';
    
    -- Business logos
    RETURN QUERY
    SELECT 
        'business_logo'::TEXT,
        b.logo_url::TEXT,
        'businesses'::TEXT,
        b.id,
        CASE 
            WHEN b.logo_url LIKE '%imagedelivery.net%' THEN 'Cloudflare URL'
            WHEN b.logo_url IS NOT NULL AND NOT b.logo_url LIKE 'http%' THEN 'Possible Cloudflare ID'
            ELSE 'External/Other'
        END::TEXT,
        CASE 
            WHEN b.logo_url LIKE '%imagedelivery.net%' THEN 
                split_part(split_part(b.logo_url, '/', -2), '/', 1)
            WHEN b.logo_url IS NOT NULL AND NOT b.logo_url LIKE 'http%' THEN 
                b.logo_url
            ELSE NULL
        END::TEXT
    FROM businesses b
    WHERE b.owner_id = target_user_id AND b.logo_url IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================================================
-- PART 5: USAGE INSTRUCTIONS
-- ================================================================

/*
USAGE INSTRUCTIONS:

1. First, run the queries above to see what users have Cloudflare images

2. To audit a specific user's media:
   SELECT * FROM public.audit_user_media('USER_ID_HERE');

3. To test the cleanup function manually:
   a. Note down the image URLs/IDs from the audit
   b. Call the cleanup-user-media edge function via HTTP
   c. Check the edge function logs for detailed results

4. Example curl command to test cleanup function:
   curl -X POST "YOUR_SUPABASE_URL/functions/v1/cleanup-user-media" \
     -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
     -H "Content-Type: application/json" \
     -d '{"userId": "USER_ID_HERE"}'

5. Check Cloudflare dashboard to verify images are actually deleted

DEBUGGING TIPS:

- Look for "imagedelivery.net" URLs - these are definitely Cloudflare
- Image IDs that don't start with "http" might be Cloudflare IDs
- Check the edge function logs for detailed deletion attempts
- Verify CLOUDFLARE_API_TOKEN and CLOUDFLARE_ACCOUNT_ID are set correctly
*/

SELECT 'Cloudflare cleanup test script loaded successfully' as status;
