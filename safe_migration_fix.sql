-- =====================================================
-- SAFE MIGRATION FIX - Check Old Data First
-- =====================================================
-- Fix migration by checking what data actually exists

-- =====================================================
-- 1. CHECK WHAT'S IN THE OLD TABLE
-- =====================================================

-- Check what status values actually exist in user_connections
SELECT 
    '=== OLD TABLE STATUS VALUES ===' as check_phase,
    status,
    COUNT(*) as count
FROM user_connections
GROUP BY status
ORDER BY status;

-- Check the enum type definition
SELECT 
    '=== ENUM TYPE DEFINITION ===' as check_phase,
    enumlabel as allowed_values
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'connection_status'
)
ORDER BY enumsortorder;

-- =====================================================
-- 2. FIX SOCIAL_CONNECTIONS CONSTRAINT
-- =====================================================

-- Drop the existing status constraint on social_connections
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find the constraint name
    SELECT conname INTO constraint_name
    FROM pg_constraint 
    WHERE conrelid = 'social_connections'::regclass 
    AND contype = 'c'
    AND pg_get_constraintdef(oid) LIKE '%status%'
    LIMIT 1;
    
    -- Drop it if it exists
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE social_connections DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    END IF;
END $$;

-- Add the correct constraint that includes all possible statuses
ALTER TABLE social_connections 
ADD CONSTRAINT social_connections_status_check 
CHECK (status IN ('pending', 'connected', 'declined', 'blocked'));

-- =====================================================
-- 3. SAFE MIGRATION - ONLY MIGRATE EXISTING VALUES
-- =====================================================

-- Migrate only the status values that actually exist in the old table
INSERT INTO social_connections (
    requester_id, 
    recipient_id, 
    status, 
    created_at, 
    updated_at, 
    connected_at
)
SELECT 
    uc.user_id as requester_id,
    uc.connection_id as recipient_id,
    CASE 
        WHEN uc.status::text = 'accepted' THEN 'connected'
        WHEN uc.status::text = 'pending' THEN 'pending'
        WHEN uc.status::text = 'rejected' THEN 'declined'
        -- Only include blocked if it exists in the enum
        WHEN uc.status::text = 'blocked' THEN 'blocked'
        -- Handle any other values
        ELSE 'pending'
    END as status,
    COALESCE(uc.created_at, now()) as created_at,
    COALESCE(uc.updated_at, now()) as updated_at,
    CASE 
        WHEN uc.status::text = 'accepted' THEN COALESCE(uc.updated_at, uc.created_at, now())
        ELSE NULL 
    END as connected_at
FROM user_connections uc
WHERE NOT EXISTS (
    SELECT 1 FROM social_connections sc 
    WHERE (
        (sc.requester_id = uc.user_id AND sc.recipient_id = uc.connection_id)
        OR 
        (sc.requester_id = uc.connection_id AND sc.recipient_id = uc.user_id)
    )
)
AND EXISTS (SELECT 1 FROM profiles WHERE id = uc.user_id)
AND EXISTS (SELECT 1 FROM profiles WHERE id = uc.connection_id)
AND uc.user_id != uc.connection_id;

-- =====================================================
-- 4. VERIFICATION
-- =====================================================

-- Check migration results
SELECT 
    '=== MIGRATION RESULTS ===' as phase,
    'Old system total' as metric,
    COUNT(*) as count
FROM user_connections
UNION ALL
SELECT 
    '=== MIGRATION RESULTS ===',
    'New system total',
    COUNT(*)
FROM social_connections
UNION ALL
SELECT 
    '=== MIGRATION RESULTS ===',
    'Connected relationships',
    COUNT(*)
FROM social_connections 
WHERE status = 'connected'
UNION ALL
SELECT 
    '=== MIGRATION RESULTS ===',
    'Pending relationships',
    COUNT(*)
FROM social_connections 
WHERE status = 'pending'
UNION ALL
SELECT 
    '=== MIGRATION RESULTS ===',
    'Declined relationships',
    COUNT(*)
FROM social_connections 
WHERE status = 'declined';

-- Show status mapping results
SELECT 
    '=== STATUS MAPPING RESULTS ===' as mapping_phase,
    sc.status as new_status,
    COUNT(*) as count
FROM social_connections sc
GROUP BY sc.status
ORDER BY sc.status;

-- Show any unmapped statuses (if any)
SELECT 
    '=== UNMAPPED OLD STATUSES ===' as unmapped_check,
    uc.status::text as old_status,
    COUNT(*) as count
FROM user_connections uc
WHERE uc.status::text NOT IN ('accepted', 'pending', 'rejected', 'blocked')
GROUP BY uc.status::text;

-- =====================================================
-- 5. SUCCESS MESSAGE
-- =====================================================

SELECT 
    '✅ SAFE MIGRATION COMPLETE!' as status,
    'Data migrated based on actual old table values' as message,
    'Check results above for verification' as instruction,
    'Ready to continue with cleanup' as next_step;
