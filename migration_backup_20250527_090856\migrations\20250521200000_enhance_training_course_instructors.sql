-- Drop the dependent view first
DROP VIEW IF EXISTS training_courses_view;

-- Create a new type for instructor information
DO $$ BEGIN
    CREATE TYPE instructor_type AS (
        id UUID,
        name TEXT,
        title TEXT,
        organization TEXT,
        type TEXT
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add new column for structured instructor data and migrate existing data
ALTER TABLE training_courses 
ADD COLUMN instructors_data JSONB DEFAULT '[]'::jsonb;

-- Migrate any existing instructor data
UPDATE training_courses
SET instructors_data = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'name', instructor,
            'type', 'manual'
        )
    )
    FROM unnest(instructors) instructor
)
WHERE instructors IS NOT NULL;

-- Now we can safely drop the old column
ALTER TABLE training_courses
DROP COLUMN IF EXISTS instructors;

-- Add comment for the new column
COMMENT ON COLUMN training_courses.instructors_data IS 'Structured data for course instructors including registered users and manual entries';

-- Recreate the view with the new column structure
CREATE OR REPLACE VIEW training_courses_view AS
SELECT 
    t.*,
    nc.name as category_name,
    p.first_name || ' ' || p.last_name as creator_name,
    p.avatar_url as creator_avatar_url,
    (
        SELECT COUNT(*)
        FROM training_course_enrollments 
        WHERE course_id = t.id AND status = 'approved'
    ) as enrollment_count
FROM training_courses t
LEFT JOIN netzero_categories nc ON nc.id = t.category_id
LEFT JOIN profiles p ON p.id = t.creator_id
WHERE t.status = 'Published' OR auth.uid() = t.creator_id;

-- Enable row level security
ALTER TABLE training_courses ENABLE ROW LEVEL SECURITY;

-- Update policies for the new column
CREATE POLICY "Enable select for authenticated users"
    ON training_courses
    FOR SELECT
    TO authenticated
    USING (status = 'Published' OR auth.uid() = creator_id);

CREATE POLICY "Enable insert for authenticated users"
    ON training_courses
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = creator_id);

CREATE POLICY "Enable update for course creators"
    ON training_courses
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = creator_id)
    WITH CHECK (auth.uid() = creator_id);
