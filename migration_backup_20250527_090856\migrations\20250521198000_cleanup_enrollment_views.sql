-- Drop all previous versions of enrollment views and functions
DROP VIEW IF EXISTS training_course_enrollments_view;
DROP FUNCTION IF EXISTS get_enrollment_access;

-- Recreate the view with the secure implementation
CREATE VIEW training_course_enrollments_view AS
SELECT 
    e.id,
    e.course_id,
    e.user_id,
    e.profile_id,
    e.status,
    e.request_message,
    e.created_at,
    e.updated_at,
    CONCAT(p.first_name, ' ', p.last_name) as user_name,
    p.avatar_url,
    p.title as user_title,
    p.organization as user_organization,
    p.email as user_email,
    t.title as course_title,
    t.organization_name as course_provider
FROM 
    training_course_enrollments e
JOIN 
    profiles p ON e.profile_id = p.id
JOIN 
    training_courses t ON e.course_id = t.id;

-- Grant access to authenticated users
GRANT SELECT ON training_course_enrollments_view TO authenticated;

COMMENT ON VIEW training_course_enrollments_view IS 'Secure view of training course enrollments with user and course information. Security is handled by RLS policies on base tables.';
