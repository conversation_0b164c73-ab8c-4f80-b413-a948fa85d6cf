-- Fix notification trigger functions that are still using user_id instead of profile_id
-- This migration updates the outdated trigger functions to use the correct column names

B<PERSON>IN;

-- Drop ALL existing notification trigger functions to ensure clean slate
DROP FUNCTION IF EXISTS public.notify_post_like() CASCADE;
DROP FUNCTION IF EXISTS public.notify_post_comment() CASCADE;
DROP FUNCTION IF EXISTS public.notify_comment_like() CASCADE;
DROP FUNCTION IF EXISTS public.notify_comment_reply() CASCADE;

-- Drop and recreate the notification trigger functions with correct column references
-- These functions were using the old user_id column but the table now uses profile_id

-- 1. Fix notify_post_like function
CREATE OR REPLACE FUNCTION public.notify_post_like()
RETURNS TRIGGER AS $$
DECLARE
  post_owner_user_id UUID;
BEGIN
  -- Get post owner user_id from social_posts (social_posts uses user_id column)
  SELECT user_id INTO post_owner_user_id
  FROM public.social_posts
  WHERE id = NEW.post_id;

  -- Don't notify if user likes their own post
  IF post_owner_user_id = NEW.user_id THEN
    RETURN NEW;
  END IF;

  -- Insert notification using profile_id column (notifications table uses profile_id)
  -- The user_id from social tables corresponds to profile_id in notifications table
  INSERT INTO public.notifications (profile_id, type, content, related_id, related_type, actor_id)
  VALUES (
    post_owner_user_id,
    'post_like',
    'liked your post',
    NEW.post_id,
    'post',
    NEW.user_id
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Fix notify_post_comment function
CREATE OR REPLACE FUNCTION public.notify_post_comment()
RETURNS TRIGGER AS $$
DECLARE
  post_owner_user_id UUID;
BEGIN
  -- Get post owner user_id from social_posts (social_posts uses user_id column)
  SELECT user_id INTO post_owner_user_id
  FROM public.social_posts
  WHERE id = NEW.post_id;

  -- Don't notify if user comments on their own post
  IF post_owner_user_id = NEW.user_id THEN
    RETURN NEW;
  END IF;

  -- Insert notification using profile_id column (notifications table uses profile_id)
  -- The user_id from social tables corresponds to profile_id in notifications table
  INSERT INTO public.notifications (profile_id, type, content, related_id, related_type, actor_id)
  VALUES (
    post_owner_user_id,
    'post_comment',
    'commented on your post',
    NEW.post_id,
    'post',
    NEW.user_id
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Fix notify_comment_like function
CREATE OR REPLACE FUNCTION public.notify_comment_like()
RETURNS TRIGGER AS $$
DECLARE
  comment_owner_user_id UUID;
BEGIN
  -- Get comment owner user_id from post_comments (post_comments uses user_id column)
  SELECT user_id INTO comment_owner_user_id
  FROM public.post_comments
  WHERE id = NEW.comment_id;

  -- Don't notify if user likes their own comment
  IF comment_owner_user_id = NEW.user_id THEN
    RETURN NEW;
  END IF;

  -- Insert notification using profile_id column (notifications table uses profile_id)
  -- The user_id from social tables corresponds to profile_id in notifications table
  INSERT INTO public.notifications (profile_id, type, content, related_id, related_type, actor_id)
  VALUES (
    comment_owner_user_id,
    'comment_like',
    'liked your comment',
    NEW.comment_id,
    'comment',
    NEW.user_id
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure triggers are properly connected to the updated functions
-- (The triggers should already exist, but this ensures they're using the updated functions)

-- Drop and recreate triggers to ensure they use the updated functions
DROP TRIGGER IF EXISTS notify_post_like_trigger ON public.post_likes;
CREATE TRIGGER notify_post_like_trigger
  AFTER INSERT ON public.post_likes
  FOR EACH ROW
  EXECUTE FUNCTION public.notify_post_like();

DROP TRIGGER IF EXISTS notify_post_comment_trigger ON public.post_comments;
CREATE TRIGGER notify_post_comment_trigger
  AFTER INSERT ON public.post_comments
  FOR EACH ROW
  EXECUTE FUNCTION public.notify_post_comment();

DROP TRIGGER IF EXISTS notify_comment_like_trigger ON public.comment_likes;
CREATE TRIGGER notify_comment_like_trigger
  AFTER INSERT ON public.comment_likes
  FOR EACH ROW
  EXECUTE FUNCTION public.notify_comment_like();

COMMIT;
