-- Create business_relevant_industries junction table
CREATE TABLE IF NOT EXISTS public.business_relevant_industries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_id UUID REFERENCES public.businesses(id) ON DELETE CASCADE NOT NULL,
    industry_id UUID REFERENCES public.industries(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(business_id, industry_id)
);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_business_relevant_industries_business_id ON public.business_relevant_industries(business_id);
CREATE INDEX IF NOT EXISTS idx_business_relevant_industries_industry_id ON public.business_relevant_industries(industry_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.business_relevant_industries ENABLE ROW LEVEL SECURITY;

-- Create policies for business_relevant_industries
CREATE POLICY "Anyone can view business relevant industries"
    ON public.business_relevant_industries
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Business owners can manage their business industries"
    ON public.business_relevant_industries
    FOR ALL
    TO authenticated
    USING (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    )
    WITH CHECK (
        business_id IN (
            SELECT id 
            FROM public.businesses 
            WHERE owner_id = auth.uid()
        )
    );

-- Add helpful comments
COMMENT ON TABLE public.business_relevant_industries IS 'Junction table linking businesses to their relevant industries';
COMMENT ON COLUMN public.business_relevant_industries.business_id IS 'Reference to the business';
COMMENT ON COLUMN public.business_relevant_industries.industry_id IS 'Reference to the industry';
