
-- Create a dedicated user consent settings table
CREATE TABLE IF NOT EXISTS public.user_consent_settings (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  share_email_with_event_creators BOOLEAN NOT NULL DEFAULT FALSE,
  share_email_with_attendees BOOLEAN NOT NULL DEFAULT FALSE,
  share_contact_details BOOLEAN NOT NULL DEFAULT FALSE,
  consent_updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS to the consent settings table
ALTER TABLE public.user_consent_settings ENABLE ROW LEVEL SECURITY;

-- Users can only view and edit their own consent settings
CREATE POLICY view_own_consent_settings 
  ON public.user_consent_settings 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY update_own_consent_settings 
  ON public.user_consent_settings 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Add a trigger to automatically create default consent settings for new users
CREATE OR REPLACE FUNCTION create_default_user_consent_settings()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_consent_settings (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add trigger to create default consent settings whenever a profile is created
CREATE TRIGGER create_consent_settings_on_profile_creation
  AFTER INSERT ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION create_default_user_consent_settings();

-- Add consent field to event_signups table to track specific event consent
ALTER TABLE public.event_signups 
ADD COLUMN IF NOT EXISTS gdpr_consent BOOLEAN DEFAULT FALSE;

-- Update the existing view to properly handle consent settings
DROP VIEW IF EXISTS public.event_signups_with_users;

CREATE VIEW public.event_signups_with_users AS
  SELECT 
    es.id,
    es.event_id,
    es.user_id,
    es.signup_date,
    es.status,
    es.hide_attendance,
    es.notes,
    es.gdpr_consent,
    es.created_at,
    es.updated_at,
    -- Only expose identifiable information, never emails directly through the view
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    p.avatar_url,
    p.title,
    p.organization
  FROM 
    public.event_signups es
  JOIN 
    auth.users au ON es.user_id = au.id
  LEFT JOIN 
    public.profiles p ON es.user_id = p.id;

-- Grant appropriate permissions on the view
ALTER VIEW public.event_signups_with_users OWNER TO postgres;
GRANT SELECT ON public.event_signups_with_users TO authenticated;
GRANT SELECT ON public.event_signups_with_users TO service_role;

-- Create a secure function to get event attendee emails ONLY for event creators
CREATE OR REPLACE FUNCTION get_event_attendee_emails(event_id UUID)
RETURNS TABLE (
  user_id UUID,
  full_name TEXT,
  email TEXT,
  consent_status TEXT
) 
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow event creators to access emails
  IF EXISTS (
    SELECT 1 FROM events 
    WHERE id = event_id 
    AND created_by = auth.uid()
  ) THEN
    RETURN QUERY
    SELECT 
      es.user_id,
      CONCAT(p.first_name, ' ', p.last_name) as full_name,
      CASE 
        WHEN (es.gdpr_consent = TRUE OR ucs.share_email_with_event_creators = TRUE) THEN p.email
        ELSE NULL
      END as email,
      CASE 
        WHEN es.gdpr_consent = TRUE THEN 'event specific consent'
        WHEN ucs.share_email_with_event_creators = TRUE THEN 'global consent'
        ELSE 'no consent'
      END as consent_status
    FROM 
      event_signups es
    JOIN 
      profiles p ON es.user_id = p.id
    LEFT JOIN
      user_consent_settings ucs ON es.user_id = ucs.user_id
    WHERE 
      es.event_id = event_id;
  ELSE
    RAISE EXCEPTION 'Access denied: You must be the event creator to view participant emails';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION get_event_attendee_emails TO authenticated;

-- Create a secure function to update a user's event-specific consent
CREATE OR REPLACE FUNCTION update_event_gdpr_consent(p_event_id UUID, p_consent BOOLEAN)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
  v_updated BOOLEAN;
BEGIN
  UPDATE event_signups
  SET gdpr_consent = p_consent,
      updated_at = now()
  WHERE event_id = p_event_id
    AND user_id = auth.uid();
    
  GET DIAGNOSTICS v_updated = ROW_COUNT;
  
  RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION update_event_gdpr_consent TO authenticated;

-- Create a secure function to update a user's global consent settings
CREATE OR REPLACE FUNCTION update_user_consent_settings(
  p_share_email_with_event_creators BOOLEAN,
  p_share_email_with_attendees BOOLEAN,
  p_share_contact_details BOOLEAN
)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
  v_updated BOOLEAN;
BEGIN
  INSERT INTO user_consent_settings (
    user_id,
    share_email_with_event_creators,
    share_email_with_attendees,
    share_contact_details,
    consent_updated_at,
    updated_at
  )
  VALUES (
    auth.uid(),
    p_share_email_with_event_creators,
    p_share_email_with_attendees,
    p_share_contact_details,
    now(),
    now()
  )
  ON CONFLICT (user_id) 
  DO UPDATE SET
    share_email_with_event_creators = p_share_email_with_event_creators,
    share_email_with_attendees = p_share_email_with_attendees,
    share_contact_details = p_share_contact_details,
    consent_updated_at = now(),
    updated_at = now();
    
  GET DIAGNOSTICS v_updated = ROW_COUNT;
  
  RETURN v_updated > 0;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION update_user_consent_settings TO authenticated;

-- Function for event attendees to see other attendees' emails (only if both parties consented)
CREATE OR REPLACE FUNCTION get_event_attendee_contact_details(p_event_id UUID)
RETURNS TABLE (
  user_id UUID,
  full_name TEXT,
  email TEXT
)
SECURITY DEFINER
AS $$
BEGIN
  -- First check if the user is actually attending this event
  IF NOT EXISTS (
    SELECT 1 FROM event_signups
    WHERE event_id = p_event_id AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Access denied: You must be an attendee of this event';
    RETURN;
  END IF;
  
  -- Check if the user has consented to share their email with attendees
  -- Only return emails of other attendees who have also consented
  RETURN QUERY
  SELECT 
    es.user_id,
    CONCAT(p.first_name, ' ', p.last_name) as full_name,
    CASE 
      -- Only return email if both the requester and the attendee have consented
      WHEN (es.gdpr_consent = TRUE OR ucs.share_email_with_attendees = TRUE) AND
           EXISTS (
             SELECT 1 FROM user_consent_settings 
             WHERE user_id = auth.uid() AND share_email_with_attendees = TRUE
           )
      THEN p.email
      ELSE NULL
    END as email
  FROM 
    event_signups es
  JOIN 
    profiles p ON es.user_id = p.id
  LEFT JOIN
    user_consent_settings ucs ON es.user_id = ucs.user_id
  WHERE 
    es.event_id = p_event_id;
END;
$$ LANGUAGE plpgsql;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION get_event_attendee_contact_details TO authenticated;

-- Add RLS to profiles table to protect emails
DO $$ 
BEGIN
  -- Check if RLS is already enabled
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'profiles' 
    AND rowsecurity = true
  ) THEN
    -- Enable RLS
    ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    
    -- Create policy for users to see their own profile
    CREATE POLICY view_own_profile
      ON public.profiles
      FOR SELECT
      USING (auth.uid() = id);
      
    -- Create policy for users to update their own profile
    CREATE POLICY update_own_profile
      ON public.profiles
      FOR UPDATE
      USING (auth.uid() = id);
      
    -- Create policy for admin access if needed
    -- CREATE POLICY admin_access_profiles...
  END IF;
END $$;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_event_signups_user_id ON public.event_signups(user_id);
CREATE INDEX IF NOT EXISTS idx_event_signups_event_id ON public.event_signups(event_id);
CREATE INDEX IF NOT EXISTS idx_user_consent_settings_user_id ON public.user_consent_settings(user_id);
