import { NextApiRequest, NextApiResponse } from 'next';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { buffer } from 'micro';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const sig = req.headers['stripe-signature'] as string;
  let event: Stripe.Event;

  try {
    const body = await buffer(req);
    event = stripe.webhooks.constructEvent(
      body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return res.status(400).json({ error: 'Invalid signature' });
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        
        console.log('Checkout session completed:', session.id);
        
        // Update user's subscription tier
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            subscription_tier: session.metadata?.tier,
            subscription_status: 'active',
            subscription_start_date: new Date().toISOString(),
            customer_id: session.customer as string,
          })
          .eq('id', session.metadata?.user_id);

        if (updateError) {
          console.error('Error updating user subscription:', updateError);
        } else {
          console.log('User subscription updated successfully');
        }
        
        break;

      case 'customer.subscription.updated':
        const updatedSubscription = event.data.object as Stripe.Subscription;
        
        // Update subscription status based on Stripe status
        const status = updatedSubscription.status === 'active' ? 'active' :
                      updatedSubscription.status === 'past_due' ? 'past_due' :
                      updatedSubscription.status === 'canceled' ? 'cancelled' :
                      'incomplete';

        await supabase
          .from('profiles')
          .update({
            subscription_status: status,
          })
          .eq('customer_id', updatedSubscription.customer);
        
        break;

      case 'customer.subscription.deleted':
        const deletedSubscription = event.data.object as Stripe.Subscription;
        
        // Downgrade user to free tier
        await supabase
          .from('profiles')
          .update({
            subscription_tier: 'seed',
            subscription_status: 'cancelled',
            subscription_end_date: new Date().toISOString(),
          })
          .eq('customer_id', deletedSubscription.customer);
        
        break;

      case 'invoice.payment_failed':
        const failedInvoice = event.data.object as Stripe.Invoice;
        
        // Update subscription status to past_due
        await supabase
          .from('profiles')
          .update({
            subscription_status: 'past_due',
          })
          .eq('customer_id', failedInvoice.customer);
        
        break;

      case 'invoice.payment_succeeded':
        const succeededInvoice = event.data.object as Stripe.Invoice;
        
        // Update subscription status to active
        await supabase
          .from('profiles')
          .update({
            subscription_status: 'active',
          })
          .eq('customer_id', succeededInvoice.customer);
        
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
};
